import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../datatable/data_table_2.dart';
import '../../datatable/paginated_data_table_2.dart';
import '../../kawosoti/schema/widgets/date_forward_backward.dart';
import '../../utils/app_colors.dart';
import '../levelHistory/level_history_data_source.dart';

/// Route options are used to configure certain features of
/// the given example
String getCurrentRouteOption(BuildContext context) {
  var isEmpty = ModalRoute.of(context) != null &&
          ModalRoute.of(context)!.settings.arguments != null &&
          ModalRoute.of(context)!.settings.arguments is String
      ? ModalRoute.of(context)!.settings.arguments as String
      : '';

  return isEmpty;
}

// Route options
const dflt = 'Default';
const noData = 'No data';
const autoRows = 'Auto rows';
const showBordersWithZebraStripes = 'Borders with Zebra';
const custPager = 'Custom pager';
const defaultSorting = 'Default sorting';
const selectAllPage = 'Select all at page';
const rowTaps = 'Row Taps';
const rowHeightOverrides = 'Row height overrides';
const fixedColumnWidth = 'Fixed column width';

class DivisionHistoryPage extends StatefulHookConsumerWidget {
  const DivisionHistoryPage({this.siteId = "site1"});

  final String siteId;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _DivisionHistoryPageState();
}

class _DivisionHistoryPageState extends ConsumerState<DivisionHistoryPage> {
  LevelHistorySiteDataSource? siteDataSource;
  final PaginatorController _controller = PaginatorController();
  int _rowsPerPage = PaginatedDataTable.defaultRowsPerPage;
  int tankId = 1;

  List<DataColumn> get _columns {
    return [
      DataColumn2(
        size: ColumnSize.L,
        label: const Text(
          'Date',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text(
          'Level',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'M1',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'M2',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Float',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),

    ];
  }

  @override
  void initState() {
    siteDataSource =
        LevelHistorySiteDataSource(userType: ref.read(userState).role ?? 1);
    siteDataSource?.SiteId = widget.siteId + "_level_history";
    siteDataSource?.tankId = 1;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    final prefs = ref.read(sharedPreferencesServiceProvider);
    void handleClick(String value ) {
      if(value == prefs.getTankName("tankone")){
        tankId = 1;
        setState(() {
          siteDataSource?.tankId = 1;
          _controller.goToFirstPage();
        });
      }else if(value == prefs.getTankName("tanktwo")){
        tankId = 2;
        setState(() {
          siteDataSource?.tankId = 2;
          _controller.goToFirstPage();
        });
      }else if(value == prefs.getTankName("tankthree")){
        tankId = 3;
        setState(() {
          siteDataSource?.tankId = 3;
          _controller.goToFirstPage();
        });
      }else if(value == prefs.getTankName("tankfour")){
        tankId = 4;
        setState(() {
          siteDataSource?.tankId = 4;
          _controller.goToFirstPage();
        });
      }else if(value == prefs.getTankName("tankfive")){
        tankId = 5;
        setState(() {
          siteDataSource?.tankId = 5;
          _controller.goToFirstPage();
        });
      }else if(value == prefs.getTankName("tanksix")){
        tankId = 6;
        setState(() {
          siteDataSource?.tankId = 6;
          _controller.goToFirstPage();
        });
      }else{
        tankId = 7;
        setState(() {
          siteDataSource?.tankId = 7;
          _controller.goToFirstPage();
        });
      }
    }

    return BaseScaffold(
      showAppBar: false,
      appbar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: AppColors.themeColor,
        centerTitle: true,
        title: Text(
          tankId == 1 ? "Level History ${prefs.getTankName("tankone")}"
              : tankId==2 ? "Level History ${prefs.getTankName("tanktwo")}" : tankId == 3 ?
              "Level History ${prefs.getTankName("tankthree")}" : tankId == 4 ?
          "Level History ${prefs.getTankName("tankfour")}" : tankId == 5 ?
          "Level History ${prefs.getTankName("tankfive")}" : tankId == 6 ?
          "Level History ${prefs.getTankName("tanksix")}"
              : "Level History ${prefs.getTankName("tankseven")}" ,
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: handleClick,
            itemBuilder: (BuildContext context) {
              return {prefs.getTankName("tankone"), prefs.getTankName("tanktwo"),
                }.map((String choice) {
                return PopupMenuItem<String>(
                  value: choice,
                  child: Text(choice),
                );
              }).toList();

            },
          )
        ],
      ),
      child: Column(
        children: [
          DateForwardBackward(onDateChange: (data) {
            siteDataSource?.startDate = data;
            _controller.goToFirstPage();
          }),
          SizedBox(
            height: 20,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                AsyncPaginatedDataTable2(
                    showCheckboxColumn: false,
                    horizontalMargin: 16,
                    columnSpacing: 0,
                    wrapInCard: false,
                    rowsPerPage: 15,
                    minWidth: 500,
                    availableRowsPerPage: [15],
                    autoRowsToHeight:
                        getCurrentRouteOption(context) == autoRows,
                    // Default - do nothing, autoRows - goToLast, other - goToFirst
                    pageSyncApproach: getCurrentRouteOption(context) == dflt
                        ? PageSyncApproach.doNothing
                        : getCurrentRouteOption(context) == autoRows
                            ? PageSyncApproach.goToLast
                            : PageSyncApproach.goToFirst,
                    fit: FlexFit.tight,
                    initialFirstRowIndex: 0,
                    onRowsPerPageChanged: (value) {
                      // No need to wrap into setState, it will be called inside the widget
                      // and trigger rebuild
                      //setState(() {
                      print('Row per page changed to $value');
                      _rowsPerPage = value!;
                      //});
                    },
                    controller: _controller,
                    empty: Center(
                        child: Container(
                            padding: const EdgeInsets.all(20),
                            color: AppColors.themeColor,
                            child: const Text(
                              'No data',
                              style:
                                  TextStyle(fontSize: 18, color: Colors.white),
                            ))),
                    loading: Center(
                        child: CircularProgressIndicator(
                      color: Colors.blue,
                    )),
                    errorBuilder: (e) => Center(
                          child: SelectableText(e.toString()),
                        ),
                    columns: _columns,
                    source: siteDataSource!),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
