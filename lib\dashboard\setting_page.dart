import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/dashboard/widgets/setting_form.dart';
import 'package:si/provider/dashboard_provider.dart';

class SettingPage extends HookConsumerWidget {
  SettingPage({this.site = "site1"});

  final String site;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final setting =
        ref.watch(settingProvider(site == "site1" ? "setting1" : "setting2"));

    return BaseScaffold(
      showAppBar: true,
      appbarText: site == "site1" ? "Site 1 Setting" : "Site 2 Setting",
      child: setting.when(
          data: (data) {
            return SingleChildScrollView(child: SettingForm(settingModel: data, site: site,));
          },
          error: (er, st) {
            return Text(er.toString());
          },
          loading: () => const LoadingIndicator()),
    );
  }
}
