

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import 'motor_widget.dart';

class SourceWidget extends HookConsumerWidget{

  const SourceWidget(this.text);

  final String text;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return  Container(
      height: 60,
      color: Colors.blueAccent,
      child: Center(
          child: Text(
            text,
            style: TextStyle(color: Colors.white, fontSize: 16),
          )),
    );
  }
}

class OpenWellWidget extends HookConsumerWidget{


  const OpenWellWidget({required this.height,required this.width});

  final double height;
  final double width;

  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFFBAE5F3);


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Image.asset("assets/icons/open_well.png",
    fit: BoxFit.fill,
    height: height, width: width,);
      Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide.none,
          left: BorderSide(color: Colors.black, width: 1) ,
          bottom:  BorderSide(color: Colors.black, width: 1),
          right:  BorderSide(color: Colors.black, width: 1), // No border on the right
        ),
        borderRadius: BorderRadius.all(Radius.circular(4)),
      ),
      child: Stack(
        children: [
          Center(
            child: Container(
              height: height-4,
              width: width -4,
              child: Stack(
                children: [
                  GestureDetector(
                    child: WaveWidget(
                      config: CustomConfig(
                        colors: _colors,
                        durations: _durations,
                        heightPercentages: [1 - 0.85, 1 - 0.85],
                      ),
                      backgroundColor: _backgroundColor,
                      size: Size(double.infinity, double.infinity),
                      waveAmplitude: 0,
                      heightPercentage: 0.9,
                    ),
                  ),
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        "Open well",
                        style: TextStyle(fontSize: 20, ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

}

class SumpWellWidget extends HookConsumerWidget{


  const SumpWellWidget({required this.height,required this.width});

  final double height;
  final double width;

  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFFBAE5F3);




  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: width,
      height: height,

      child: Image.asset("assets/icons/sumpwell.webp")

    );
  }

}