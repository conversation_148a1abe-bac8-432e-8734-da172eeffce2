<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="waterbilling">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="icons/Icon-192.png"/>

  <title>Swodeshi Scada System</title>
  <link rel="manifest" href="manifest.json">
  <style>
    /* Splash screen styles */
    #splash-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #ffffff; /* Background color */
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      z-index: 9999;
    }

    /* Logo styling */
    #splash-logo {
      width: 150px;
      height: 150px;
      margin-bottom: 20px;
    }

    /* Loading indicator */
    #loading-indicator {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-top: 4px solid #007bff; /* Color of the spinner */
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }

    /* Keyframes for spinner animation */
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  </style>
</head>
<body>
<script src="flutter_bootstrap.js" async></script>

<!-- Splash screen -->
<div id="splash-screen">
  <img id="splash-logo" src="splash/img/light-4x.png" alt="Logo" />
  <div id="loading-indicator"></div>
</div>

<!-- Flutter app -->
<script src="flutter.js" defer></script>
<script>
  // Remove splash screen when the Flutter app is ready
  window.addEventListener("flutter-first-frame", function () {
    document.getElementById("splash-screen").style.display = "none";
  });
</script>


</body>
</html>
