
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/dashboard/settingcontroller/setting_state.dart';
import 'package:si/model/setting_model.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/signup/ui/form_label_text.dart';

import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';

class SettingForm extends ConsumerStatefulWidget {
  const SettingForm({super.key, required this.settingModel, required this.site});

  final SettingModel settingModel;
  final String site;


  @override
  ConsumerState<SettingForm> createState() =>
      _SettingFormState();
}

class _SettingFormState extends ConsumerState<SettingForm> {
  final _settingKey = GlobalKey<FormState>();
  final _node = FocusScopeNode();
  final _actualHeightController = TextEditingController();
  final _maxAmpController = TextEditingController();
  final _minAmpController = TextEditingController();
  final _minHeightController = TextEditingController();
  final _maxHeightController = TextEditingController();
  final _modeController = TextEditingController();
  final _timerController = TextEditingController();

  String get actualHeight => _actualHeightController.text;
  String get maxAmp => _maxAmpController.text;
  String get minAmp => _minAmpController.text;
  String get minHeight => _minHeightController.text;
  String get maxHeight => _maxHeightController.text;
  String get mode => _modeController.text;
  String get timer => _timerController.text;

  // local variable used to apply AutovalidateMode.onUserInteraction and show
  // error hints only when the form has been submitted
  // For more details on how this is implemented, see:
  // https://codewithandrea.com/articles/flutter-text-field-form-validation/
  var _submitted = false;

  Future<void> _submit(SettingState state, WidgetRef ref) async {
    setState(() => _submitted = true);
    // only submit the form if validation passes
    if (_settingKey.currentState!.validate()) {
      final controller = ref.read(settingControllerProvider.notifier);
      //final token = await ref.read(fcmProvider).getToken();
      if(maxHeight.isNotEmpty && minHeight.isNotEmpty
          && minAmp.isNotEmpty && maxAmp.isNotEmpty
          && actualHeight.isNotEmpty && timer.isNotEmpty && mode.isNotEmpty){
        controller.sendLoadingState();
        final success = await controller.submit(widget.site,SettingModel(
          maxHeight: int.parse(maxHeight),minHeight: int.parse(minHeight),
          minAmps: double.parse(minAmp),maxAmps: double.parse(maxAmp),
          actualHeight: int.parse(actualHeight),timer: int.parse(timer),
          mode: int.parse(mode),
        ));
        if (success) {
          context.pop();
        }
      }else{
        EasyLoading.showError("None can be empty");
      }

    }
  }



  @override
  void initState() {
    super.initState();
    _actualHeightController.text = widget.settingModel.actualHeight.toString();
    _maxHeightController.text = widget.settingModel.maxHeight.toString();
    _minHeightController.text = widget.settingModel.minHeight.toString();
    _maxAmpController.text = widget.settingModel.maxAmps.toString();
    _minAmpController.text = widget.settingModel.minAmps.toString();
    _modeController.text = widget.settingModel.mode.toString();
    _timerController.text = widget.settingModel.timer.toString();
  }


  @override
  void dispose() {
    // * TextEditingControllers should be always disposed
    _node.dispose();
    _minHeightController.dispose();
    _maxHeightController.dispose();
    _minAmpController.dispose();
    _maxAmpController.dispose();
    _modeController.dispose();
    _timerController.dispose();
    _actualHeightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(settingControllerProvider);
    return Padding(
      padding: const EdgeInsets.all(Sizes.p8),
      child: FocusScope(
        node: _node,
        child: Form(
          key: _settingKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              gapH16,
              // email field
              CustomTextFormField(
                name: "actual_height",
                textInputFormatter: [
                  FilteringTextInputFormatter.allow(
                      RegExp(r'^\d{0,3}'))
                ],
                enabled: !state.isLoading,
                controller: _actualHeightController,
                hintText: "Actual Height",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.height_outlined,
                  color: Colors.grey,
                ),
              ),
              FormLabelText(label: "Enter actual height"),
              gapH16,
              Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          name: "max_height",
                          textInputFormatter: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d{0,3}'))
                          ],
                          enabled: !state.isLoading,
                          controller: _maxHeightController,
                          hintText: "Maximum Height",
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(
                            Icons.height_outlined,
                            color: Colors.grey,
                          ),
                        ),
                        FormLabelText(label: "Enter max height"),
                      ],
                    ),
                  ),
                  gapW24,
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          name: "min_height",
                          textInputFormatter: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d{0,3}'))
                          ],
                          enabled: !state.isLoading,
                          controller: _minHeightController,
                          hintText: "Minimum Height",
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(
                            Icons.height_outlined,
                            color: Colors.grey,
                          ),
                        ),
                        FormLabelText(label: "Enter min height"),
                      ],
                    ),
                  ),
                ],
              ),
              gapH16,
              Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          name: "max_amp",
                          enabled: !state.isLoading,
                          controller: _maxAmpController,
                          hintText: "Maximum Current",
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(
                            Icons.amp_stories_outlined,
                            color: Colors.grey,
                          ),
                        ),
                        FormLabelText(label: "Enter max current"),
                      ],
                    ),
                  ),
                  gapW24,
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          name: "min_amp",
                          enabled: !state.isLoading,
                          controller: _minAmpController,
                          hintText: "Minimum current",
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(
                            Icons.amp_stories_outlined,
                            color: Colors.grey,
                          ),
                        ),
                        FormLabelText(label: "Enter min current"),
                      ],
                    ),
                  ),
                ],
              ),
              gapH16,
              Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          name: "timer",
                          controller: _timerController,
                          hintText: "Timer",
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(
                            Icons.timer_outlined,
                            color: Colors.grey,
                          ),
                          textInputFormatter: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d{0,2}'))
                          ],
                          enabled: !state.isLoading,
                        ),
                        FormLabelText(label: "Enter timer in mins"),
                      ],
                    ),
                  ),
                  gapW24,
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          name: "mode",
                          textInputFormatter: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^[1-2]'))
                          ],
                          enabled: !state.isLoading,
                          controller: _modeController,
                          hintText: "Mode",
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(
                            Icons.mode_standby,
                            color: Colors.grey,
                          ),
                        ),
                        FormLabelText(label: "Enter mode"),
                      ],
                    ),
                  ),
                ],
              ),
              gapH16,
              // Password field

              gapH24,
              PrimaryButton(
                isLoading: state.isLoading,
                height: Sizes.p60,
                text: "Submit",
                onPressed: () async {
                  _submit(state, ref);
                  /*await ref.read(sharedPreferencesServiceProvider).setLogin();
                  context.router.replaceAll([DashBoardRouter()]);*/
                },
              ),
              gapH24,
            ],
          ),
        ),
      ),
    );
  }
}
