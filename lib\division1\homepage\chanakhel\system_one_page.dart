import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../../constants/app_sizes.dart';
import '../../model/motor_model.dart';


class SystemOnePage extends HookConsumerWidget {

  const SystemOnePage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());
    final motor12State = useState<MotorModel>(MotorModel());
    final motor20State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor12State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor12State.value.device_status == motor12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status == motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return  CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: horPadding8,
                            child: Container(
                              color: Colors.blueAccent,
                              height: 60,
                              width: double.infinity,
                              child: Center(child: const Text("बोरिङ्ग", style : TextStyle(color: Colors.white)))
                            ),
                          ),
                          HookConsumer(
                            builder: (context,ref, child) {
                              //final motor10 = ref.watch(motor10Provider);
                              return Container(
                                color: motor10State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                                height: 30,
                                width: 20,
                              );
                            }
                          ),
                          NewMotorUnit(ratio: 3.5,motorId: "10",
                            motorSettingModel: setting.motor10!,
                            motorModel: motor10State.value,
                           ),
                          HookConsumer(
                            builder: (context,ref, child) {
                              //final motor10 = ref.watch(motor10Provider);
                              return Container(
                                color: motor10State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                                height: 280,
                                width: 20,
                              );
                            }
                          ),

                        ],
                      ),
                    ),
                    Expanded(
                      flex: 7,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: horPadding4,
                            child: Container(
                              color: Colors.blueAccent,
                              height: 60,
                              width: double.infinity, child: Center(child: const Text("मुलको पानी", style : TextStyle(color: Colors.white)))

                            ),
                          ),
                          Container(
                            color: Colors.blueAccent,
                            height: 30,
                            width: 20,
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8.0),
                            child: NewTankUnit(3, "10",
                                setting.tank10!!, 180, double.infinity),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                                Column(
                                  children: [
                                    HookConsumer(
                                      builder: (context, ref, child) {
                                        //final motor11 = ref.watch(motor11Provider);
                                        return Container(
                                          color: motor11State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                                          height: 30,
                                          width: 20,
                                        );
                                      }
                                    ),
                                    NewMotorUnit(ratio: 3.5,motorId: "11",motorSettingModel: setting.motor11,
                                    motorModel: motor11State.value,),
                                    HookConsumer(
                                      builder: (context, ref, child) {
                                        //final motor11 = ref.watch(motor11Provider);
                                        return Container(
                                          color: motor11State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                                          height: 40,
                                          width: 20,
                                        );
                                      }
                                    ),
                                  ],
                                ),
                              Column(
                                children: [
                                  HookConsumer(
                                    builder: (context, ref, child) {
                                      //final motor12 = ref.watch(motor12Provider);
                                      return Container(
                                        color: motor12State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                                        height: 30,
                                        width: 20,
                                      );
                                    }
                                  ),
                                  NewMotorUnit(ratio: 3.5,motorId: "12",motorSettingModel: setting.motor12,
                                  motorModel: motor12State.value,),
                                  HookConsumer(
                                    builder: (context, ref, child) {
                                      //final motor12 = ref.watch(motor12Provider);
                                      return Container(
                                        color: motor12State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                                        height: 40,
                                        width: 20,
                                      );
                                    }
                                  ),
                                ],
                              ),
                            ],
                          )

                        ],
                      ),
                    ),
                  ]),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: NewTankUnit(3, "20", setting.tank20!, 200, double.infinity),
              ),
              Container(
                color: Colors.blueAccent,
                height: 30,
                width: 20,
              ),
              Container(
                color: Colors.blueAccent,
                height: 20,
                width: double.infinity,
              ),
              Row(
                children: [
                  gapW16,
                  Column(
                    children: [
                      HookConsumer(
                        builder: (context, ref, child) {
                          //final motor20 = ref.watch(motor20Provider);
                          return Container(
                            color: motor20State.value.device_status==3 ? Colors.blueAccent :  Colors.grey,
                            height: 90,
                            width: 20,
                          );
                        }
                      ),
                      NewMotorUnit(ratio: 2.7,motorId: "20",motorSettingModel: setting.motor20,
                      motorModel: motor20State.value,),
                      gapH48,
                    ],
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: HookConsumer(
                      builder: (context, ref, child) {
                        //final motor20 = ref.watch(motor20Provider);
                        return Container(
                          color: motor20State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                          height: 20,
                          width: 70,
                        );
                      }
                    ),
                  ),
                  Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: NewTankUnit(3, "30", setting.tank30!, 200,150),
                      ),
                      Container(
                        color: Colors.blueAccent,
                        height: 100,
                        width: 20,
                      ),
                    ],
                  ),

                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: NewTankUnit(3, "31", setting.tank31!, 200,double.infinity),
              ),
              gapH48


            ],
          ),
        ),
      ],
    );
  }
}
