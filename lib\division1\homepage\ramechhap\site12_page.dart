import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import 'mobile_site12_page.dart';

class RamechapSite12Page extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    final motor10State = useState<MotorModel>(MotorModel());

    ref.listen(refreshProvider, (previous, next) async {
      ref
          .read(divisionStateProvider(prefs.getSiteId()).notifier)
          .getSetting(prefs.getSiteId());
    });

    final size = MediaQuery.of(context).size;
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        motor10State.value = MotorModel(
            motorAmps: datasnapshot['current'],
            output_status: datasnapshot['output_status'],
            voltage: datasnapshot['voltage'],
            time: datasnapshot['time'],
            VoltageFaultStatus: datasnapshot['voltage_fault'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (motor10State.value.device_status ==
          motor10State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    return siteSetting.when(
        success: (setting, message) {
          return BaseScaffold(
              showLeftIcon: false,
              appbarText: setting?.name ?? "",
              showAction: prefs.getUserRole() > 3 ? true : false,
              onActionClick: () async {
                final setting = await context.push("/location");
              },
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFF87CEFA), // Light Blue (Sky color)
                      Color(0xFFE0FFFF),
                      Color(0xFF87CEFA), // Light Blue (Sky color)
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: MobileSite12Page(setting!),
              ));
        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }
}
