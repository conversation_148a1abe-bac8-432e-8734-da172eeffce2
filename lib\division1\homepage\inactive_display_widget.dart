

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:si/division1/model/motor_setting_model.dart';

import '../model/motor_model.dart';
import 'motor_moving.dart';

class InactiveDisplay extends StatelessWidget{
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding:
        const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.all(Radius.circular(14)),
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(4.0),
                child: Text(
                  'Motor',
                  style: TextStyle(color: Colors.grey, fontSize: 16),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(4.0),
                child: Text(
                 '0.00 A',
                  style: TextStyle(
                      color:
                      Colors.grey),
                ),
              ),
              Expanded(
                flex: 5,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                  ),
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.all(Radius.circular(14)),
                    ),
                    child: GestureDetector(

                        child: MotorMoving(false, MotorModel(), MotorSettingModel())),
                  ),
                ),
              ),
              Consumer(builder: (context, WidgetRef ref, child) {

                return Expanded(
                  flex: 3,
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: FlutterSwitch(
                          width: 100.0,
                          height: 40.0,
                          activeColor: Colors.green,
                          inactiveColor: Colors.grey,
                          valueFontSize: 16.0,
                          toggleSize: 30.0,
                          value: false,
                          borderRadius: 30.0,
                          padding: 8.0,
                          showOnOff: true,
                          onToggle: (val) async {

                          }),
                    ),
                  ),
                );
              })
            ],
          ),
        ));
  }

}