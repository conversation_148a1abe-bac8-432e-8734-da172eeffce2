

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BlockContainer extends HookConsumerWidget{

  const BlockContainer({Key? key, required this.height,required this.width, required this.text, this.color}) : super(key: key);

  final double height;
  final double width;
  final String text;
  final Color? color;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: height,
      width: width,
      color: color ?? Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: Colors.black)),
        child: Center(child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(this.text,textAlign: TextAlign.center,),
        )),
      ),
    );
  }

}