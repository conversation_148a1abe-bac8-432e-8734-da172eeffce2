

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/katunje/mobile_katunje_page.dart';
import 'package:si/division1/homepage/katunje/web_katunje_page.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../services/shared_preferences_service.dart';
import '../../../utils/app_colors.dart';
import '../../provider/division_provider.dart';

class KatunjePage extends HookConsumerWidget{

    Widget build(BuildContext context, WidgetRef ref) {
      final prefs = ref.watch(sharedPreferencesServiceProvider);
      final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
      final width = MediaQuery.of(context).size.width;
      ref.listen(refreshProvider, (previous, next)  async {
        ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
      });


      return siteSetting.when(
          success: (setting, message) {
              return BaseScaffold(
                  showLeftIcon: false,
                  showAppBar: width<600 ? true : false,
                  appbarText: setting?.name ?? "",
                  showAction: prefs.getUserRole() >3 ? true : false,
                  onActionClick:() async {
                    final setting = await context.push("/location");
                  },
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF87CEFA), // Light Blue (Sky color)
                          Color(0xFFE0FFFF),
                          Color(0xFF87CEFA), // Light Blue (Sky color)
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,),),
                    child:  width>600 ?
                    WebKatunjePage(setting!) :
                    MobileKatunjePage(setting!))
                  );

          },
          unInitialized: () {
            return Container();
          },
          error: (er) {
            log(er.toString());
            return Container();
          },
          unauthorized: () {
            return Container();
          },
          loading: () => LoadingIndicator());
    }



}