import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';

import '../../../constants/app_sizes.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';

class SimkholaSiteTwoPage extends HookConsumerWidget {
  const SimkholaSiteTwoPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor30State = useState<MotorModel>(MotorModel());
    final motor40State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("30"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor30State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor30State.value.device_status == motor30State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("40"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor40State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor40State.value.device_status == motor40State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Container(
                color: Colors.blueAccent,
                height: 60,
                width: double.infinity,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      Container(
                        color: motor30State.value.device_status == 3
                            ? Colors.blueAccent
                            : Colors.grey,
                        height: 30,
                        width: 20,
                      ),
                      NewMotorUnit(
                        ratio: 3.5,
                        motorId: "30",
                        motorSettingModel: setting.motor30,
                        motorModel: motor30State.value,
                      ),
                      Container(
                        color: motor30State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                        height: 40,
                        width: 20,
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        color: motor40State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                        height: 30,
                        width: 20,
                      ),
                      NewMotorUnit(
                        ratio: 3.5,
                        motorId: "40",
                        motorSettingModel: setting.motor40,
                        motorModel: motor40State.value,
                      ),
                      Container(
                        color: motor40State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                        height: 40,
                        width: 20,
                      )
                    ],
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child:
                    NewTankUnit(3, "30", setting.tank30!, 200, double.infinity),
              ),
              gapH48
            ],
          ),
        ),
      ],
    );
  }
}
