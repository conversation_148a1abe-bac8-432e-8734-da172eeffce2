

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';

import '../../../constants/app_sizes.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';

class SystemTwoPage extends HookConsumerWidget{
  const SystemTwoPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final motor40State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("40"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor40State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor40State.value.device_status == motor40State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    return  CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Container(
                color: Colors.blueAccent,
                height: 70,
                width: double.infinity, child: Center(child: const Text("मुलको पानी", style : TextStyle(color: Colors.white)))

              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          color: Colors.blueAccent,
                          height: 60,
                          width: 20,

                        ),
                        NewTankUnit(3, "40", setting.tank40!, 200, 150),
                        gapH64,
                      ],
                    ),
                    HookConsumer(
                      builder: (context, ref,child) {
                        return Container(
                          color: motor40State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                          height: 20,
                          width: 60,
                        );
                      }
                    ),
                    Column(
                      children: [
                        gapH24,
                        NewMotorUnit(ratio: 2.5,motorSettingModel: setting.motor40,motorId: "40",
                        motorModel: motor40State.value,),
                        HookConsumer(
                          builder: (context, ref, child) {
                           // final motor40 = ref.watch(motor40Provider);
                            return Container(
                              color: motor40State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                              height: 110,
                              width: 20,
                            );
                          }
                        ),
                      ],
                    ),
                  ]),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: NewTankUnit(3, "50", setting.tank50!, 200, double.infinity),
              ),
              gapH48

            ],
          ),
        ),
      ],
    );
  }

}