rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    function isAuthed() {
      return request.auth != null;
    }
    
    match /{document=**} {
      allow read, write: if false;
    }
    
    match /users/{userId} {
      allow read, write: if isAuthed();
		}
    
     match /logs/{logId} {
      allow read, write: if isAuthed();
		}
    
    match /site1_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site2_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    
    match /site3_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site3_motor_history/{id}{
    	allow read, write: if isAuthed();
    }
    
  	match /site4_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site5_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site6_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site7_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site8_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site9_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /site10_tank_history/{id}{
    	allow read, write: if isAuthed();
    }
    
    match /usersList/{userId} {
      allow read, write: if isAuthed();
		}
    
  	match /settings/{id} {
      allow read, write: if isAuthed();
		}
    
    match /setting/{id} {
      allow read, write: if isAuthed();
		}
    
  
    
    match /site1/{site1Id} {
      allow read, write: if isAuthed();
		}
    
    match /site2/{site2Id} {
      allow read, write: if isAuthed();
		}
    
    match /site3/{site3Id} {
      allow read, write: if isAuthed();
		}
  
    match /site4/{site4Id} {
      allow read, write: if isAuthed();
		}
    
    match /site5/{site5Id} {
      allow read, write: if isAuthed();
		}
    
    match /site6/{site6Id} {
      allow read, write: if isAuthed();
		}
    
    match /site7/{site7Id} {
      allow read, write: if isAuthed();
		}
    
    match /site8/{site8Id} {
      allow read, write: if isAuthed();
		}
    
    match /site1History/{site1Id} {
      allow read, write: if isAuthed();
		}
    
    match /site2History/{site2Id} {
      allow read, write: if isAuthed();
		}
    
    match /site3History/{site3Id} {
      allow read, write: if isAuthed();
		}
  
    match /site4History/{site4Id} {
      allow read, write: if isAuthed();
		}
    
    match /site5History/{site5Id} {
      allow read, write: if isAuthed();
		}
    
    match /site6History/{site6Id} {
      allow read, write: if isAuthed();
		}
    
    match /site7History/{site7Id} {
      allow read, write: if isAuthed();
		}
    
    match /site8History/{site8Id} {
      allow read, write: if isAuthed();
		}
    
    match /settings/{settingId} {
      allow read, write: if isAuthed();
		}
    
    match /bwHistory/{id} {
      allow read, write: if isAuthed();
		}
    
    match /scheduler/{id} {
      allow read, write: if isAuthed();
		}
    match /loginHistory/{id} {
      allow read, write: if isAuthed();
		}
    
  
  }
}