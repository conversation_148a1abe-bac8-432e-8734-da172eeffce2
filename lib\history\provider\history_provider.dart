

import 'package:cloud_firestore/cloud_firestore.dart';

import '../model/history_model.dart';

final tankOnehistoryQuery = FirebaseFirestore.instance.collection('tank_history_one')
    .orderBy('time',descending: true).withConverter<LevelHistorymodel>(
  fromFirestore: (snapshot, _) => LevelHistorymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final tankTwohistoryQuery = FirebaseFirestore.instance.collection('tank_history_two')
    .orderBy('time',descending: true).withConverter<LevelHistorymodel>(
  fromFirestore: (snapshot, _) => LevelHistorymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final motorOnehistoryQuery = FirebaseFirestore.instance.collection('motor_history_one')
    .orderBy('time',descending: true).withConverter<Historymodel>(
  fromFirestore: (snapshot, _) => Historymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final motorTwohistoryQuery = FirebaseFirestore.instance.collection('motor_history_two')
    .orderBy('time',descending: true).withConverter<Historymodel>(
  fromFirestore: (snapshot, _) => Historymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final timerOnehistoryQuery = FirebaseFirestore.instance.collection('timer_history_one')
    .orderBy('time',descending: true).withConverter<Historymodel>(
  fromFirestore: (snapshot, _) => Historymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final timerTwohistoryQuery = FirebaseFirestore.instance.collection('timer_history_two')
    .orderBy('time',descending: true).withConverter<Historymodel>(
  fromFirestore: (snapshot, _) => Historymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final floatOnehistoryQuery = FirebaseFirestore.instance.collection('float_history_one')
    .orderBy('time',descending: true).withConverter<Historymodel>(
  fromFirestore: (snapshot, _) => Historymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final floatTwohistoryQuery = FirebaseFirestore.instance.collection('float_history_two')
    .orderBy('time',descending: true).withConverter<Historymodel>(
  fromFirestore: (snapshot, _) => Historymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final siteOnehistoryQuery = FirebaseFirestore.instance.collection('history_site_one')
    .orderBy('time',descending: true).withConverter<AllHistorymodel>(
  fromFirestore: (snapshot, _) => AllHistorymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

final siteTwohistoryQuery = FirebaseFirestore.instance.collection('history_site_two')
    .orderBy('time',descending: true).withConverter<AllHistorymodel>(
  fromFirestore: (snapshot, _) => AllHistorymodel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);