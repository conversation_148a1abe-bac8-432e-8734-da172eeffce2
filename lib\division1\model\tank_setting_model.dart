



import '../../utils/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'tank_setting_model.freezed.dart';
part 'tank_setting_model.g.dart';

@freezed
class TankSettingModel with _$TankSettingModel {
  const factory TankSettingModel({
    @Default(true) bool level_sensor_enable,
    @Default(true) bool  float_enable,
    @Default(400) int max_height,
    @Default(50) int min_height,
    @Default(450) int actual_height,
    @Default(0) int offset,
    @Default(true) bool is_active,
    @Default(false) bool is_bw,
    @Default(15) int upload_time,
    @Default('') String name,
  }) = _TankSettingModel;

  factory TankSettingModel.fromJson(Map<String, dynamic> json) => _$TankSettingModelFromJson(json);
}


@freezed
class BorewellSettingModel with _$BorewellSettingModel {
  const factory BorewellSettingModel({
    @Default(0.0) double? height,
    @Default("") String?  name,
    @Default(true) bool is_active,
    @Default(15) int?  upload_time,
  }) = _BorewellSettingModel;

  factory BorewellSettingModel.fromJson(Map<String, dynamic> json) => _$BorewellSettingModelFromJson(json);
}
