

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/division1/homepage/widgets/distribution_widget.dart';
import 'package:si/division1/homepage/widgets/tank_widget.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';

class RanibariPage extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    final valve10State = useState<MotorModel>(const MotorModel());
    final valve11State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve10State.value = MotorModel(
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve10State.value.device_status ==
              valve10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve11State.value = MotorModel(
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve11State.value.device_status ==
              valve11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    return siteSetting.when(
        success: (data,message){
          return BaseScaffold(
              appbarText: data?.name ?? "",
              showLeftIcon: false,
              child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFF87CEFA), // Light Blue (Sky color)
                        Color(0xFFE0FFFF),
                        Color(0xFF87CEFA), // Light Blue (Sky color)
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          height: 60,
                          width: double.infinity,
                          color: Colors.blueAccent,
                          child: const Center(
                              child: Text("Source",style: TextStyle(color: Colors.white, fontSize: 20),)),
                        ),
                        Row(
                          children: [
                            gapW64,
                            Container(
                              height: 170,
                              width: 20,
                              color: Colors.blueAccent,
                            ),
                            Container(
                              height: 20,
                              width: 80,
                              color: Colors.blueAccent,
                            ),
                            CommonPassingValveWidget(name: data!.valve10!.name,
                                valveId: "10", active: true,
                                role: prefs.getUserRole(), ratio: 4, valveModel: valve10State.value),
                            DistributionWidget(name: "D1", height: 50, width: 80,
                            active: valve10State.value.device_status == 3 ? true : false),

                          ],
                        ),
                        Container(
                          height: 20,
                          color: Colors.blueAccent,
                          width: double.infinity
                        ),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Column(children: [
                              Container(
                                height: 30,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                              TankBlock(tankId: "10", tankSettingModel: data!.tank10!, height: 200 , width: 140),
                              gapH16,
                            ],),

                            Container(
                              height: 20,
                              width: 50,
                              color: Colors.blueAccent,
                            ),
                            Column(
                              children: [


                                CommonPassingValveWidget(name: data.valve11!.name,
                                    valveId: "11", active: true,
                                    role: prefs.getUserRole(), ratio: 4, valveModel: valve11State.value),
                                Container(
                                  height: 86,
                                  width: 20,
                                  color: valve11State.value.device_status == 3 ? Colors.blueAccent : Colors.grey,
                                ),

                              ],
                            ),

                          ],
                        ),
                        Container(
                          height: 20,
                            color: valve11State.value.device_status == 3 ? Colors.blueAccent : Colors.grey,
                          width: double.infinity
                        ),
                        Column(children: [
                          Container(
                            height: 30,
                            width: 20,
                            color: valve11State.value.device_status == 3 ? Colors.blueAccent : Colors.grey,
                          ),
                          TankBlock(tankId: "11", tankSettingModel: data!.tank11!, height: 200 , width: 200),
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                        ],),
                        Container(
                          height: 60,
                          width: double.infinity,
                          color: Colors.blueAccent,
                          child: const Center(
                              child: Text("Distribution",style: TextStyle(color: Colors.white, fontSize: 20),)),
                        ),
                      ],
                    ),
                  )

              ));
        },
        unInitialized: ()=>Container(),
        error: (er)=> Container(),
        unauthorized: ()=> Container(),
        loading: ()=>LoadingIndicator());



  }
  
}