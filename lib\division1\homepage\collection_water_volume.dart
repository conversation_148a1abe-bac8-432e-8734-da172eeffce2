

import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/flow_reading_box.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../../constants/app_sizes.dart';
import '../model/tank_model.dart';

class CollectionWaterVolumeWidget extends HookConsumerWidget {

  const CollectionWaterVolumeWidget( this.id, this.motorState, this.name);


  final String id;
  final bool motorState;
  final String name;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final volume = useState<OutputInputVolumeModel>(const OutputInputVolumeModel());
    final calculateUnit = (volume.value.volume/1000).floor().toString();
    final array = calculateUnit.split('');
    ref.listen<AsyncValue<DatabaseEvent>>(outputProvider(id), (previous, next)  async {
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final afterVolumeData = OutputInputVolumeModel.fromJson(aftercleanData);
        log(afterVolumeData.toString());
        volume.value = afterVolumeData;
      }
    });
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              name ?? 'Motor',
              style: TextStyle(color: volume.value.status ==1 ?  Colors.green :Colors.grey, fontSize: 18),
            ),
            Padding(
              padding: const EdgeInsets.all(0.0),
              child: Text(" ("+(volume.value.volume/1000).toStringAsFixed(2)+" kl)", style: TextStyle(fontSize: 16,color:
              volume.value.status == 1 ?  Colors.green  :Colors.grey)),
            ),
          ],
        ),
        gapH8,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: array.map((e) => FlowReadingBox(e, motorState)).toList(),
            ),
            gapW8,
            Text("Units", style: TextStyle(color: motorState ? Colors.green : Colors.black),)
          ],
        )
      ],
    );
  }
}