import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';

import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';

class MobileHaripurPage extends HookConsumerWidget {
  const MobileHaripurPage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    ref.listen(refreshProvider, (previous, next) async {
      ref
          .read(divisionStateProvider(prefs.getSiteId()).notifier)
          .getSetting(prefs.getSiteId());
    });

    final motor10State = useState<MotorModel>(const MotorModel());
    final motor11State = useState<MotorModel>(const MotorModel());
    final motor20State = useState<MotorModel>(const MotorModel());
    final motor21State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status ==
              motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status ==
              motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("21"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor21State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor21State.value.device_status ==
              motor21State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status ==
              motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return siteSetting.when(
        success: (setting, message) {
          return BaseScaffold(
            showAppBar: true,
            showLeftIcon: false,
            appbarText: "तल्कु खानेपानी",
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      Container(
                        height: 60,
                        width: double.infinity,
                        color: Colors.blueAccent,
                        child: Center(
                          child: Text(
                            "मुलको पानी",
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                      Container(
                        height: 40,
                        width: 30,
                        color: Colors.blueAccent,
                      ),
                      NewTankUnit(1, "10", setting!.tank10!, 170, 200),
                      Container(
                        height: 30,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                      Container(
                        height: 20,
                        width: double.infinity,
                        color: Colors.blueAccent,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Column(
                            children: [
                              Container(
                                height: 30,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                              NewMotorUnit(motorId: '10',motorModel: motor10State.value,motorSettingModel: setting.motor10,ratio: 3,),
                              Container(
                                height: 30,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Container(
                                height: 30,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                              NewMotorUnit(motorId: '11',motorModel: motor11State.value,motorSettingModel: setting.motor11,ratio: 3,),
                              Container(
                                height: 30,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                            ],
                          ),

                        ],
                      ),
                      Container(
                        height: 20,
                        width: double.infinity,
                        color: Colors.blueAccent,),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            children: [
                              Container(
                                  height: 20,
                                  width: 20,
                                  color: Colors.blueAccent
                              ),
                              NewTankUnit(2, "21", setting!.tank21!, 160, 130),
                              Container(
                                  height: 20,
                                  width: 20,
                                  color: Colors.blueAccent
                              ),
                              NewMotorUnit(motorId: '20',motorModel: motor20State.value,motorSettingModel: setting.motor20,ratio: 3,),
                              Container(
                                  height: 20,
                                  width: 20,
                                  color: Colors.blueAccent
                              ),
                              NewTankUnit(2, "32", setting!.tank32!, 160, 130),


                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.only(top: 120.0),
                            child: Container(
                              height: 20,
                              width: 40,
                              color: Colors.blueAccent,
                            ),
                          ),
                          Column(
                            children: [
                              Container(
                                height: 70,
                                width: 40,
                              ),
                              NewTankUnit(2, "20", setting!.tank20!, 170, 130),
                              Container(
                                  height: 100,
                                  width: 20,
                                  color: Colors.blueAccent
                              ),
                              NewTankUnit(2, "11", setting!.tank11!, 170, 130),
                              Container(
                                  height: 120,
                                  width: 20,
                                  color: Colors.blueAccent
                              ),



                            ],
                          ),
                        ],
                      ),
                      Container(
                        height: 20,
                        width: double.infinity,
                        color: Colors.blueAccent,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Column(
                            children: [
                              Container(
                                height: 25,
                                width:20,
                                color: Colors.blueAccent,
                              ),
                              NewTankUnit(2, "12", setting!.tank12!, 170, 130),
                              Container(
                                height: 20,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                              NewTankUnit(2, "30", setting!.tank30!, 170, 130),

                            ],
                          ),
                          Column(
                            children: [
                              Container(
                                height: 20,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                              NewMotorUnit(motorId: '21',motorModel: motor21State.value,motorSettingModel: setting.motor21,ratio: 3,),
                              Container(
                                height: 20,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                              NewTankUnit(2, "31", setting!.tank31!, 170, 130),

                            ],
                          )
                        ],
                      )

                    ],
                  ),
                ),
              ],
            ),
          );
        },
        unInitialized: () {
          return Container();
        },
        error: (erro) {
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }
}
