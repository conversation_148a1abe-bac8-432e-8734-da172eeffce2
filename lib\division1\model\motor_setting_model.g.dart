// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'motor_setting_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MotorSettingModelImpl _$$MotorSettingModelImplFromJson(
        Map<String, dynamic> json) =>
    _$MotorSettingModelImpl(
      name: json['name'] as String? ?? '',
      hrs: json['hrs'] as String? ?? '',
      mins: json['mins'] as String? ?? '',
      power: json['power'] as String? ?? '',
      CF: (json['CF'] as num?)?.toInt() ?? -39,
      has_voltage: json['has_voltage'] as bool? ?? false,
      TH_Amps: (json['TH_Amps'] as num?)?.toDouble() ?? 0.0,
      max_current: (json['max_current'] as num?)?.toDouble() ?? 0.0,
      min_current: (json['min_current'] as num?)?.toDouble() ?? 0.0,
      is_active: json['is_active'] as bool? ?? true,
      upload_time: (json['upload_time'] as num?)?.toInt() ?? 15,
      mode: (json['mode'] as num?)?.toInt() ?? 1,
      current_threshold: (json['current_threshold'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$MotorSettingModelImplToJson(
        _$MotorSettingModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'hrs': instance.hrs,
      'mins': instance.mins,
      'power': instance.power,
      'CF': instance.CF,
      'has_voltage': instance.has_voltage,
      'TH_Amps': instance.TH_Amps,
      'max_current': instance.max_current,
      'min_current': instance.min_current,
      'is_active': instance.is_active,
      'upload_time': instance.upload_time,
      'mode': instance.mode,
      'current_threshold': instance.current_threshold,
    };

_$ValveSettingModelImpl _$$ValveSettingModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ValveSettingModelImpl(
      name: json['name'] as String? ?? '',
      is_active: json['is_active'] as bool? ?? true,
      upload_time: (json['upload_time'] as num?)?.toInt() ?? 15,
      mode: (json['mode'] as num?)?.toInt() ?? 1,
      hrs: json['hrs'] as String? ?? "",
      mins: json['mins'] as String? ?? "",
      days: json['days'] as String? ?? "",
    );

Map<String, dynamic> _$$ValveSettingModelImplToJson(
        _$ValveSettingModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'is_active': instance.is_active,
      'upload_time': instance.upload_time,
      'mode': instance.mode,
      'hrs': instance.hrs,
      'mins': instance.mins,
      'days': instance.days,
    };

_$SensorSettingModelImpl _$$SensorSettingModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SensorSettingModelImpl(
      is_active: json['is_active'] as bool? ?? true,
      tos: (json['tos'] as num?)?.toDouble() ?? 0.0,
      clos: (json['clos'] as num?)?.toDouble() ?? 0.0,
      phos: (json['phos'] as num?)?.toDouble() ?? 0.0,
      tscale: (json['tscale'] as num?)?.toInt() ?? 1,
      clscale: (json['clscale'] as num?)?.toInt() ?? 1,
      phscale: (json['phscale'] as num?)?.toInt() ?? 1,
    );

Map<String, dynamic> _$$SensorSettingModelImplToJson(
        _$SensorSettingModelImpl instance) =>
    <String, dynamic>{
      'is_active': instance.is_active,
      'tos': instance.tos,
      'clos': instance.clos,
      'phos': instance.phos,
      'tscale': instance.tscale,
      'clscale': instance.clscale,
      'phscale': instance.phscale,
    };

_$ScheduleModelImpl _$$ScheduleModelImplFromJson(Map<String, dynamic> json) =>
    _$ScheduleModelImpl(
      hrs: json['hrs'] as String? ?? "",
      mins: json['mins'] as String? ?? "",
    );

Map<String, dynamic> _$$ScheduleModelImplToJson(_$ScheduleModelImpl instance) =>
    <String, dynamic>{
      'hrs': instance.hrs,
      'mins': instance.mins,
    };
