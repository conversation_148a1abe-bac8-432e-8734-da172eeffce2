import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/valve/valve_provider.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../constants/app_sizes.dart';
import 'model/valve_model.dart';


class WaterFlowSwitch extends HookConsumerWidget {

  const WaterFlowSwitch({Key? key, required this.outputId, required this.onOffSwitch}) : super(key: key);

  final String outputId;

  final Function(bool) onOffSwitch;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final valve = useState<bool>(false);
    final showLoading = useState<bool>(false);
    final prefs = ref.read(sharedPreferencesServiceProvider);

    ref.listen<AsyncValue<DatabaseEvent>>(outputStatusProvider(outputId), (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        if(datasnapshot["status"]==1){
          valve.value = true;
        }else{
          valve.value = false;
        }
        EasyLoading.dismiss();
        showLoading.value = false;
        if(datasnapshot["status"]==2){
          onOffSwitch(false);
        }else{
          onOffSwitch(true);
        }
      }

    });
    log(prefs.getTankName("tankone"));
    log(outputId);
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(outputId=="site1/output1" ? prefs.getTankName("tankone"): outputId=="site1/output2"
            ? prefs.getTankName("tanktwo") : outputId=="site1/output3"
            ? prefs.getTankName("tankthree")+"(1)" :
        prefs.getTankName("tankthree")+"(2)", style: TextStyle(fontSize: 16, color: Colors.black),),
        gapW48,
        FlutterSwitch(
            width: 100.0,
            height: 40.0,
            activeColor: Colors.green,
            inactiveColor: Colors.grey,
            valueFontSize: 16.0,
            toggleSize: 30.0,
            value: valve.value,
            borderRadius: 30.0,
            padding: 8.0,
            showOnOff: true,
            onToggle: (val) async {
             if((ref.read(userState).role ?? 0)>=2){
               showLoading.value = true;
               if(valve.value){
                 await ref.read(rtdbProvider).stopOutput(outputId);
               }else{
                 await ref.read(rtdbProvider).startOutput(outputId);
               }
             }else{
               EasyLoading.showError("Permission denied");
             }

            }),
        showLoading.value ? Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: CircularProgressIndicator(),
        ) : Container()

      ],
    );
  }
}
