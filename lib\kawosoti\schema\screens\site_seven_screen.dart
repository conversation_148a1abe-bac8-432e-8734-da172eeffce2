import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/schema/widgets/overhead_sensor.dart';

import '../block_diagram/site_five_block_diagram.dart';
import '../model/site_model.dart';
import '../site_one/provider/site_one_provider.dart';
import '../widgets/chlorine_sensor.dart';
import '../widgets/ph_sensor.dart';
import '../widgets/turbidy_sensor.dart';

class SiteSevenScreen extends HookConsumerWidget {
  const SiteSevenScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteState = useState<SiteRtDbModel>(SiteRtDbModel());
    final width = MediaQuery.of(context).size.width;

    ref.listen<AsyncValue<DatabaseEvent>>(siteDataProvider('site7'),
            (previous, next) {
          final datasnapshot = next.asData?.value.snapshot.value as Map;
          siteState.value = SiteRtDbModel(
            Tub1: double.parse(datasnapshot['Tub1'].toString()),
            Chlorine: double.parse(datasnapshot['Chlorine'].toString()),
            PH: double.parse(datasnapshot['PH'].toString()),
            OHT: double.parse(datasnapshot['OHT'].toString()),
            RMU1_timestamp: datasnapshot['RMU1_timestamp'],
            Borewell: double.parse(datasnapshot['Borewell'].toString()),
          );
        });
    return SingleChildScrollView(
      child: Column(
        children: [
          SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SiteFiveBlockDiagram(
                siteRtDbModel: siteState.value,
              )),
          width > 900 ? Row(
            children: [
              Expanded(
                child: TurbidySensor(
                  sensorName: "Turbidity 1",
                  sensorValue: siteState.value.Tub1 ?? 0.0,
                  timeStamp: (siteState.value.RMU1_timestamp) ??
                      DateTime.now().millisecondsSinceEpoch,
                ),
              ),
              Expanded(
                child: OverHeadSensor(),
              ),
            ],
          ) : Column(
            children: [
              TurbidySensor(
                sensorName: "Turbidity 1",
                sensorValue: siteState.value.Tub1 ?? 0.0,
                timeStamp: (siteState.value.RMU1_timestamp) ??
                    DateTime.now().millisecondsSinceEpoch,
              ),
              OverHeadSensor(),
            ],
          ),
          width > 900 ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ChlorineSensor(
                  sensorValue: siteState.value.Chlorine ?? 0.0,
                  timeStamp: siteState.value.RMU1_timestamp ??
                      DateTime.now().millisecondsSinceEpoch),
              PHSensor(
                timeStamp: siteState.value.RMU1_timestamp ??
                    DateTime.now().millisecondsSinceEpoch,
                sensorValue: siteState.value.PH ?? 7.0,
              )
            ],
          ) :  Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ChlorineSensor(
                  sensorValue: siteState.value.Chlorine ?? 0.0,
                  timeStamp: siteState.value.RMU1_timestamp ??
                      DateTime.now().millisecondsSinceEpoch),
              PHSensor(
                timeStamp: siteState.value.RMU1_timestamp ??
                    DateTime.now().millisecondsSinceEpoch,
                sensorValue: siteState.value.PH ?? 7.0,
              )
            ],
          )
        ],
      ),
    );
  }
}
