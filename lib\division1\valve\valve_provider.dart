
import 'package:firebase_database/firebase_database.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../provider/division_provider.dart';

final valveProvider = StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, valveId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  return rtdb.readValve(valveId);
});

final outputStatusProvider = StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, outputId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  return rtdb.readValve(outputId);
});

