import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../constants/app_sizes.dart';
import '../primary_button.dart';

void showDateTimeDialog(
  BuildContext context, {
  String title = "Pick a date",
  bool showButton = false,
  double height = 370,
  String buttonText = "Add to Calendar",
  Widget? child,
  VoidCallback? onButtonPressed,
}) {
  showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
            height: height,
            padding: const EdgeInsets.only(top: 6.0),
            // The Bottom margin is provided to align the popup above the system navigation bar.
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            // Provide a background color for the popup.
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(Sizes.p32),
                topRight: Radius.circular(Sizes.p32),
              ),
            ),
            // Use a SafeArea widget to avoid system overlaps.
            child: Safe<PERSON>rea(
              top: false,
              child: Stack(
                children: [
                  Positioned(
                      top: 8,
                      right: 8,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Padding(
                          padding: padding8,
                          child: Icon(Icons.close, ),
                        ),
                      )),
                  Column(
                    children: [
                      Padding(
                        padding: padding16,
                        child: Text(
                          title,
                        ),
                      ),
                      Expanded(child: child!),
                      Padding(
                        padding: padding16,
                        child: PrimaryButton(
                          text: buttonText,
                          isGradient: true,
                          onPressed: () {
                            onButtonPressed?.call();
                          },
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ));
}
