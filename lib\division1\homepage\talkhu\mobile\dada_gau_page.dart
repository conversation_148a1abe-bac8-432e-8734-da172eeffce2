

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_common_motor_unit.dart';
import 'package:si/division1/homepage/widgets/distribution_widget.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';
import '../../new_motor_unit.dart';
import '../../new_tank_unit.dart';

class DadaGauPage extends HookConsumerWidget{

  const DadaGauPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor20State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status ==
              motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Container(
                height: 40,
                width: double.infinity,
                color: Colors.blueAccent,
                child: Center(
                  child: Text(
                    "सिम मुहान",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      Container(
                          height: 80,
                          width: 20,
                          color: Colors.blueAccent
                      ),
                      NewTankUnit(2, "21", setting!.tank21!, 170, 160),
                      Container(
                          height: 20,
                          width: 20,
                          color: Colors.blueAccent
                      ),
                      NewMotorUnit(motorId: '20',motorModel: motor20State.value,motorSettingModel: setting.motor20,ratio: 3,),
                      Container(
                          height: 20,
                          width: 20,
                          color: Colors.blueAccent
                      ),
                      NewTankUnit(2, "22", setting.tank22!, 160, 130),
                    ],
                  ),

                  Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 110.0),
                        child: Container(
                          height: 20,
                          width: 40,
                          color: Colors.blueAccent,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 240.0),
                        child: Container(
                          height: 20,
                          width: 40,
                          color: Colors.blueAccent,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [

                      Row(
                        children: [
                          NewCommonValveUnit(
                            valveModel: MotorModel(),
                            valveId: "21",
                            ratio: 4,
                            valveSettingModel: setting.valve21!,
                          ),
                          DistributionWidget(name: "टल्कु वस्ती", height: 30, width: 80)
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 48.0),
                        child: Container(
                          height: 30,
                          width: 20,
                          color: Colors.blueAccent,
                        ),
                      ),
                      NewTankUnit(2, "20", setting!.tank20!, 170, 160),
                      Padding(
                        padding: const EdgeInsets.only(left: 64.0),
                        child: Container(
                            height: 100,
                            width: 20,
                            color: Colors.blueAccent
                        ),
                      ),
                      NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "20",
                        ratio: 3,
                        valveSettingModel: setting.valve20!,
                      ),
                      //NewTankUnit(2, "11", setting!.tank11!, 170, 160),
                      Padding(
                        padding: const EdgeInsets.only(left: 64.0),
                        child: Container(
                            height: 100,
                            width: 20,
                            color: Colors.blueAccent
                        ),
                      ),

                    ],
                  ),
                ],
              ),
              Container(
                  height: 60,
                  width: double.infinity,
                  color: Colors.blueAccent,
                child: Center(child: Text("खार्पा", style: TextStyle(color: Colors.white),)),
              ),
            ],
          ),
        )
      ],
    );
  }

}