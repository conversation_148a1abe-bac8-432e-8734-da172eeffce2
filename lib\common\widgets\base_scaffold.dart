import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/style_manager.dart';

import '../../constants/assets_manager.dart';
import '../../utils/app_colors.dart';


class BaseScaffold extends ConsumerWidget {
  final bool? showFab;
  final bool isCircularFab;
  final bool showAppBar; // this only works when appbar is null or appbar is not provider
  final bool showLeftIcon;
  final bool isExtended;
  final Color appbarColor ;
  final Color onAppBarColor;
  final Color? fabColor;
  final Color? onFabColor;
  final String? appbarText;
  final IconData? fabIcon;
  final IconData? leftIcon;
  final Widget child;
  final Widget? bottomWidget;
  final PreferredSizeWidget? appbar;
  final VoidCallback? onFabClick;
  final VoidCallback? onBackpress;
  final EdgeInsetsGeometry? padding;
  final double bottomPadding;
  final String? extendedLabel;
  final bool? showAction;
  final VoidCallback? onActionClick;

  const BaseScaffold({
    Key? key,
    required this.child,
    this.showFab = false,
    this.isExtended = false,
    this.isCircularFab = false,
    this.showLeftIcon = true,
    this.showAppBar = true,
    this.bottomPadding = 0.0,
    this.appbarColor =  AppColors.themeColor,
    this.onAppBarColor = AppColors.onThemeColor,
    this.fabColor,
    this.onFabColor,
    this.appbarText,
    this.bottomWidget,

    this.fabIcon,
    this.leftIcon,
    this.onFabClick,
    this.appbar,
    this.onBackpress,
    this.padding,
    this.showAction,
    this.onActionClick,
    this.extendedLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final darkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: bottomPadding),
        child: AnimatedSlide(
          duration: const Duration(milliseconds: 400),
          offset: showFab! ? Offset.zero : const Offset(0, 2),
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 400),
            opacity: showFab! ? 1 : 0,
            child: isExtended
                ? FloatingActionButton.extended(
                    label: Text(extendedLabel ?? 'Support'),
                    shape: isCircularFab ? const CircleBorder() : null,
                    heroTag: null,
                    backgroundColor: fabColor,
                    icon: Icon(
                      fabIcon ?? Icons.add,
                      color: onFabColor ,
                    ),
                    tooltip: 'Support',
                    onPressed: () {
                      onFabClick?.call();
                    },
                  )
                : FloatingActionButton(
                    shape: isCircularFab ? const CircleBorder() : null,
                    heroTag: null,
                    backgroundColor: fabColor,
                    child: Icon(
                      fabIcon ?? Icons.add,
                      color: onFabColor,
                    ),
                    onPressed: () {
                      onFabClick?.call();
                    },
                  ),
          ),
        ),
      ),
      bottomNavigationBar: bottomWidget,
      appBar: appbar ??
          (showAppBar
              ? AppBar(
                  iconTheme: IconThemeData(color: Colors.white),
                  backgroundColor: AppColors.themeColor,
                  centerTitle: true,
                  actions: (showAction ?? false) ? [IconButton(onPressed: onActionClick, icon: !kIsWeb ? Icon(Icons.location_pin):Icon(Icons.logout_outlined))] : null,
                  title: Text(appbarText ?? 'Appbar', style: TextStyle(color: AppColors.onThemeColor),),
                  leading: showLeftIcon
                      ? IconButton(
                          icon: Icon(leftIcon ?? Icons.arrow_back, color: onAppBarColor),
                          onPressed: () {
                            onBackpress == null ? Navigator.of(context).pop() : onBackpress?.call();
                          },
                        )
                      : null,
                )
              : null),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(0),
          child: Container(
              constraints: const BoxConstraints.expand(),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF87CEFA), // Light Blue (Sky color)
                    Color(0xFFE0FFFF),
                    Color(0xFF87CEFA), // Light Blue (Sky color)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,),),
              child: child),
        ),
      ),
    );
  }
}

// class MyUpgraderMessages extends UpgraderMessages {
//   // @override
//   // String get title => 'My Title';
//   //
//   // @override
//   // String get body => 'My Body';
//   @override
//   String get prompt => 'My Prompt';
// }
