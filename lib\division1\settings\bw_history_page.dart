import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/division1/levelHistory/borewell_level_history_data_source.dart';
import 'package:si/division1/levelHistory/tank_level_history_data_source.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../datatable/data_table_2.dart';
import '../../datatable/paginated_data_table_2.dart';
import '../../kawosoti/schema/widgets/date_forward_backward.dart';
import '../../utils/app_colors.dart';
import '../levelHistory/level_history_data_source.dart';

/// Route options are used to configure certain features of
/// the given example
String getCurrentRouteOption(BuildContext context) {
  var isEmpty = ModalRoute.of(context) != null &&
      ModalRoute.of(context)!.settings.arguments != null &&
      ModalRoute.of(context)!.settings.arguments is String
      ? ModalRoute.of(context)!.settings.arguments as String
      : '';

  return isEmpty;
}

// Route options
const dflt = 'Default';
const noData = 'No data';
const autoRows = 'Auto rows';
const showBordersWithZebraStripes = 'Borders with Zebra';
const custPager = 'Custom pager';
const defaultSorting = 'Default sorting';
const selectAllPage = 'Select all at page';
const rowTaps = 'Row Taps';
const rowHeightOverrides = 'Row height overrides';
const fixedColumnWidth = 'Fixed column width';

class BwHistoryPage extends StatefulHookConsumerWidget {
  const BwHistoryPage({this.siteId = "site1"});

  final String siteId;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BwHistoryPageState();
}

class _BwHistoryPageState extends ConsumerState<BwHistoryPage> {
  BorewellHistorySiteDataSource? siteDataSource;
  final PaginatorController _controller = PaginatorController();
  int _rowsPerPage = PaginatedDataTable.defaultRowsPerPage;
  int bwId = 10;

  List<DataColumn> get _columns {
    return [
      DataColumn2(
        size: ColumnSize.M,
        label: const Text(
          'Date',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Level',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Rssi',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Device Id',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),

    ];
  }

  @override
  void initState() {
    siteDataSource =
        BorewellHistorySiteDataSource(userType: ref.read(userState).role ?? 1);
    siteDataSource?.SiteId = widget.siteId;
    siteDataSource?.deviceId = 10;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final List<int> idList = ref.read(localStorageProvider).getSettings().bw_id!;
    log(idList.toString());

    void handleClick(String value ) {
      bwId = int.parse(value);
      setState(() {
        siteDataSource?.deviceId = bwId;
        _controller.goToFirstPage();
      });

    }

    return BaseScaffold(
      showAppBar: false,
      appbar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: AppColors.themeColor,
        centerTitle: true,
        title: Text(
          "Level History Borewell $bwId",
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: handleClick,
            itemBuilder: (BuildContext context) {
              return idList.map((int choice) {
                return PopupMenuItem<String>(
                  value: choice.toString(),
                  child: Text(choice.toString()),
                );
              }).toList();
            },
          )
        ],
      ),
      child: Column(
        children: [
          DateForwardBackward(onDateChange: (data) {
            siteDataSource?.startDate = data.millisecondsSinceEpoch;
            _controller.goToFirstPage();
          }),
          SizedBox(
            height: 20,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                AsyncPaginatedDataTable2(
                    showCheckboxColumn: false,
                    horizontalMargin: 16,
                    columnSpacing: 0,
                    wrapInCard: false,
                    rowsPerPage: 15,
                    minWidth: 400,
                    availableRowsPerPage: [15],
                    autoRowsToHeight:
                    getCurrentRouteOption(context) == autoRows,
                    // Default - do nothing, autoRows - goToLast, other - goToFirst
                    pageSyncApproach: getCurrentRouteOption(context) == dflt
                        ? PageSyncApproach.doNothing
                        : getCurrentRouteOption(context) == autoRows
                        ? PageSyncApproach.goToLast
                        : PageSyncApproach.goToFirst,
                    fit: FlexFit.tight,
                    initialFirstRowIndex: 0,
                    onRowsPerPageChanged: (value) {
                      // No need to wrap into setState, it will be called inside the widget
                      // and trigger rebuild
                      //setState(() {
                      print('Row per page changed to $value');
                      _rowsPerPage = value!;
                      //});
                    },
                    controller: _controller,
                    empty: Center(
                        child: Container(
                            padding: const EdgeInsets.all(20),
                            color: AppColors.themeColor,
                            child: const Text(
                              'No data',
                              style:
                              TextStyle(fontSize: 18, color: Colors.white),
                            ))),
                    loading: Center(
                        child: CircularProgressIndicator(
                          color: Colors.blue,
                        )),
                    errorBuilder: (e) => Center(
                      child: SelectableText(e.toString()),
                    ),
                    columns: _columns,
                    source: siteDataSource!),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
