import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/history/provider/history_provider.dart';

import '../../common/widgets/widgets.dart';
import '../../utils/format.dart';
import '../model/history_model.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';

class TankHistoryPage extends HookConsumerWidget {
  TankHistoryPage({required this.site});

  final String site;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseScaffold(
      appbarText: site == "site1" ? "Float One History" : "Float Two History",
      child: FirestoreListView<LevelHistorymodel>(
        pageSize: 20,
        query: site == "site1" ? tankOnehistoryQuery : tankTwohistoryQuery,
        errorBuilder: (context,er,st){
          return Center(child: Text(er.toString(),style: TextStyle(color: Colors.black),));
        },
        loadingBuilder: (context){
          return Center(child: LoadingIndicator(),);
        },
        itemBuilder: (context, snapshot) {
          final history = snapshot.data();
          return Column(
            children: [
              ListTile(
                title: Text("Level: ${history.level}"),
                subtitle: Text(Format.date(history.time!)),
              ),
              Container(
                height: 0.5,
                color: Colors.grey,
              )
            ],
          );
        },
      ),
    );
  }
}
