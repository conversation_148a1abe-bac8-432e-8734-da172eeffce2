// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'level_history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DivisionLevelHistorymodel _$DivisionLevelHistorymodelFromJson(
    Map<String, dynamic> json) {
  return _DivisionLevelHistorymodel.fromJson(json);
}

/// @nodoc
mixin _$DivisionLevelHistorymodel {
  int get float => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  int get mD1 => throw _privateConstructorUsedError;
  int get mO1 => throw _privateConstructorUsedError;
  int get mD2 => throw _privateConstructorUsedError;
  int get mO2 => throw _privateConstructorUsedError;
  double get v1 => throw _privateConstructorUsedError;
  double get v2 => throw _privateConstructorUsedError;
  int get CF1 => throw _privateConstructorUsedError;
  int get CF2 => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  int get tankId => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this DivisionLevelHistorymodel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DivisionLevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DivisionLevelHistorymodelCopyWith<DivisionLevelHistorymodel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DivisionLevelHistorymodelCopyWith<$Res> {
  factory $DivisionLevelHistorymodelCopyWith(DivisionLevelHistorymodel value,
          $Res Function(DivisionLevelHistorymodel) then) =
      _$DivisionLevelHistorymodelCopyWithImpl<$Res, DivisionLevelHistorymodel>;
  @useResult
  $Res call(
      {int float,
      int level,
      int mD1,
      int mO1,
      int mD2,
      int mO2,
      double v1,
      double v2,
      int CF1,
      int CF2,
      String id,
      int tankId,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$DivisionLevelHistorymodelCopyWithImpl<$Res,
        $Val extends DivisionLevelHistorymodel>
    implements $DivisionLevelHistorymodelCopyWith<$Res> {
  _$DivisionLevelHistorymodelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DivisionLevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float = null,
    Object? level = null,
    Object? mD1 = null,
    Object? mO1 = null,
    Object? mD2 = null,
    Object? mO2 = null,
    Object? v1 = null,
    Object? v2 = null,
    Object? CF1 = null,
    Object? CF2 = null,
    Object? id = null,
    Object? tankId = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      float: null == float
          ? _value.float
          : float // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      mD1: null == mD1
          ? _value.mD1
          : mD1 // ignore: cast_nullable_to_non_nullable
              as int,
      mO1: null == mO1
          ? _value.mO1
          : mO1 // ignore: cast_nullable_to_non_nullable
              as int,
      mD2: null == mD2
          ? _value.mD2
          : mD2 // ignore: cast_nullable_to_non_nullable
              as int,
      mO2: null == mO2
          ? _value.mO2
          : mO2 // ignore: cast_nullable_to_non_nullable
              as int,
      v1: null == v1
          ? _value.v1
          : v1 // ignore: cast_nullable_to_non_nullable
              as double,
      v2: null == v2
          ? _value.v2
          : v2 // ignore: cast_nullable_to_non_nullable
              as double,
      CF1: null == CF1
          ? _value.CF1
          : CF1 // ignore: cast_nullable_to_non_nullable
              as int,
      CF2: null == CF2
          ? _value.CF2
          : CF2 // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      tankId: null == tankId
          ? _value.tankId
          : tankId // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DivisionLevelHistorymodelImplCopyWith<$Res>
    implements $DivisionLevelHistorymodelCopyWith<$Res> {
  factory _$$DivisionLevelHistorymodelImplCopyWith(
          _$DivisionLevelHistorymodelImpl value,
          $Res Function(_$DivisionLevelHistorymodelImpl) then) =
      __$$DivisionLevelHistorymodelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int float,
      int level,
      int mD1,
      int mO1,
      int mD2,
      int mO2,
      double v1,
      double v2,
      int CF1,
      int CF2,
      String id,
      int tankId,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$DivisionLevelHistorymodelImplCopyWithImpl<$Res>
    extends _$DivisionLevelHistorymodelCopyWithImpl<$Res,
        _$DivisionLevelHistorymodelImpl>
    implements _$$DivisionLevelHistorymodelImplCopyWith<$Res> {
  __$$DivisionLevelHistorymodelImplCopyWithImpl(
      _$DivisionLevelHistorymodelImpl _value,
      $Res Function(_$DivisionLevelHistorymodelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DivisionLevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float = null,
    Object? level = null,
    Object? mD1 = null,
    Object? mO1 = null,
    Object? mD2 = null,
    Object? mO2 = null,
    Object? v1 = null,
    Object? v2 = null,
    Object? CF1 = null,
    Object? CF2 = null,
    Object? id = null,
    Object? tankId = null,
    Object? time = freezed,
  }) {
    return _then(_$DivisionLevelHistorymodelImpl(
      float: null == float
          ? _value.float
          : float // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      mD1: null == mD1
          ? _value.mD1
          : mD1 // ignore: cast_nullable_to_non_nullable
              as int,
      mO1: null == mO1
          ? _value.mO1
          : mO1 // ignore: cast_nullable_to_non_nullable
              as int,
      mD2: null == mD2
          ? _value.mD2
          : mD2 // ignore: cast_nullable_to_non_nullable
              as int,
      mO2: null == mO2
          ? _value.mO2
          : mO2 // ignore: cast_nullable_to_non_nullable
              as int,
      v1: null == v1
          ? _value.v1
          : v1 // ignore: cast_nullable_to_non_nullable
              as double,
      v2: null == v2
          ? _value.v2
          : v2 // ignore: cast_nullable_to_non_nullable
              as double,
      CF1: null == CF1
          ? _value.CF1
          : CF1 // ignore: cast_nullable_to_non_nullable
              as int,
      CF2: null == CF2
          ? _value.CF2
          : CF2 // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      tankId: null == tankId
          ? _value.tankId
          : tankId // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DivisionLevelHistorymodelImpl implements _DivisionLevelHistorymodel {
  const _$DivisionLevelHistorymodelImpl(
      {this.float = 2,
      this.level = 0,
      this.mD1 = 2,
      this.mO1 = 2,
      this.mD2 = 2,
      this.mO2 = 2,
      this.v1 = 0.0,
      this.v2 = 0.0,
      this.CF1 = 0,
      this.CF2 = 0,
      this.id = "",
      this.tankId = 1,
      @TimestampNullableConverter() this.time});

  factory _$DivisionLevelHistorymodelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DivisionLevelHistorymodelImplFromJson(json);

  @override
  @JsonKey()
  final int float;
  @override
  @JsonKey()
  final int level;
  @override
  @JsonKey()
  final int mD1;
  @override
  @JsonKey()
  final int mO1;
  @override
  @JsonKey()
  final int mD2;
  @override
  @JsonKey()
  final int mO2;
  @override
  @JsonKey()
  final double v1;
  @override
  @JsonKey()
  final double v2;
  @override
  @JsonKey()
  final int CF1;
  @override
  @JsonKey()
  final int CF2;
  @override
  @JsonKey()
  final String id;
  @override
  @JsonKey()
  final int tankId;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'DivisionLevelHistorymodel(float: $float, level: $level, mD1: $mD1, mO1: $mO1, mD2: $mD2, mO2: $mO2, v1: $v1, v2: $v2, CF1: $CF1, CF2: $CF2, id: $id, tankId: $tankId, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DivisionLevelHistorymodelImpl &&
            (identical(other.float, float) || other.float == float) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.mD1, mD1) || other.mD1 == mD1) &&
            (identical(other.mO1, mO1) || other.mO1 == mO1) &&
            (identical(other.mD2, mD2) || other.mD2 == mD2) &&
            (identical(other.mO2, mO2) || other.mO2 == mO2) &&
            (identical(other.v1, v1) || other.v1 == v1) &&
            (identical(other.v2, v2) || other.v2 == v2) &&
            (identical(other.CF1, CF1) || other.CF1 == CF1) &&
            (identical(other.CF2, CF2) || other.CF2 == CF2) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.tankId, tankId) || other.tankId == tankId) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, float, level, mD1, mO1, mD2, mO2,
      v1, v2, CF1, CF2, id, tankId, time);

  /// Create a copy of DivisionLevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DivisionLevelHistorymodelImplCopyWith<_$DivisionLevelHistorymodelImpl>
      get copyWith => __$$DivisionLevelHistorymodelImplCopyWithImpl<
          _$DivisionLevelHistorymodelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DivisionLevelHistorymodelImplToJson(
      this,
    );
  }
}

abstract class _DivisionLevelHistorymodel implements DivisionLevelHistorymodel {
  const factory _DivisionLevelHistorymodel(
          {final int float,
          final int level,
          final int mD1,
          final int mO1,
          final int mD2,
          final int mO2,
          final double v1,
          final double v2,
          final int CF1,
          final int CF2,
          final String id,
          final int tankId,
          @TimestampNullableConverter() final DateTime? time}) =
      _$DivisionLevelHistorymodelImpl;

  factory _DivisionLevelHistorymodel.fromJson(Map<String, dynamic> json) =
      _$DivisionLevelHistorymodelImpl.fromJson;

  @override
  int get float;
  @override
  int get level;
  @override
  int get mD1;
  @override
  int get mO1;
  @override
  int get mD2;
  @override
  int get mO2;
  @override
  double get v1;
  @override
  double get v2;
  @override
  int get CF1;
  @override
  int get CF2;
  @override
  String get id;
  @override
  int get tankId;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of DivisionLevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DivisionLevelHistorymodelImplCopyWith<_$DivisionLevelHistorymodelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TankHistoryModel _$TankHistoryModelFromJson(Map<String, dynamic> json) {
  return _TankHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$TankHistoryModel {
  int get float => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  int get tankId => throw _privateConstructorUsedError;
  int get rssi => throw _privateConstructorUsedError;
  int? get time => throw _privateConstructorUsedError;

  /// Serializes this TankHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TankHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TankHistoryModelCopyWith<TankHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TankHistoryModelCopyWith<$Res> {
  factory $TankHistoryModelCopyWith(
          TankHistoryModel value, $Res Function(TankHistoryModel) then) =
      _$TankHistoryModelCopyWithImpl<$Res, TankHistoryModel>;
  @useResult
  $Res call({int float, int level, int tankId, int rssi, int? time});
}

/// @nodoc
class _$TankHistoryModelCopyWithImpl<$Res, $Val extends TankHistoryModel>
    implements $TankHistoryModelCopyWith<$Res> {
  _$TankHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TankHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float = null,
    Object? level = null,
    Object? tankId = null,
    Object? rssi = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      float: null == float
          ? _value.float
          : float // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      tankId: null == tankId
          ? _value.tankId
          : tankId // ignore: cast_nullable_to_non_nullable
              as int,
      rssi: null == rssi
          ? _value.rssi
          : rssi // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TankHistoryModelImplCopyWith<$Res>
    implements $TankHistoryModelCopyWith<$Res> {
  factory _$$TankHistoryModelImplCopyWith(_$TankHistoryModelImpl value,
          $Res Function(_$TankHistoryModelImpl) then) =
      __$$TankHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int float, int level, int tankId, int rssi, int? time});
}

/// @nodoc
class __$$TankHistoryModelImplCopyWithImpl<$Res>
    extends _$TankHistoryModelCopyWithImpl<$Res, _$TankHistoryModelImpl>
    implements _$$TankHistoryModelImplCopyWith<$Res> {
  __$$TankHistoryModelImplCopyWithImpl(_$TankHistoryModelImpl _value,
      $Res Function(_$TankHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TankHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float = null,
    Object? level = null,
    Object? tankId = null,
    Object? rssi = null,
    Object? time = freezed,
  }) {
    return _then(_$TankHistoryModelImpl(
      float: null == float
          ? _value.float
          : float // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      tankId: null == tankId
          ? _value.tankId
          : tankId // ignore: cast_nullable_to_non_nullable
              as int,
      rssi: null == rssi
          ? _value.rssi
          : rssi // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TankHistoryModelImpl implements _TankHistoryModel {
  const _$TankHistoryModelImpl(
      {this.float = 2,
      this.level = 0,
      this.tankId = 1,
      this.rssi = 1,
      this.time});

  factory _$TankHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TankHistoryModelImplFromJson(json);

  @override
  @JsonKey()
  final int float;
  @override
  @JsonKey()
  final int level;
  @override
  @JsonKey()
  final int tankId;
  @override
  @JsonKey()
  final int rssi;
  @override
  final int? time;

  @override
  String toString() {
    return 'TankHistoryModel(float: $float, level: $level, tankId: $tankId, rssi: $rssi, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TankHistoryModelImpl &&
            (identical(other.float, float) || other.float == float) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.tankId, tankId) || other.tankId == tankId) &&
            (identical(other.rssi, rssi) || other.rssi == rssi) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, float, level, tankId, rssi, time);

  /// Create a copy of TankHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TankHistoryModelImplCopyWith<_$TankHistoryModelImpl> get copyWith =>
      __$$TankHistoryModelImplCopyWithImpl<_$TankHistoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TankHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _TankHistoryModel implements TankHistoryModel {
  const factory _TankHistoryModel(
      {final int float,
      final int level,
      final int tankId,
      final int rssi,
      final int? time}) = _$TankHistoryModelImpl;

  factory _TankHistoryModel.fromJson(Map<String, dynamic> json) =
      _$TankHistoryModelImpl.fromJson;

  @override
  int get float;
  @override
  int get level;
  @override
  int get tankId;
  @override
  int get rssi;
  @override
  int? get time;

  /// Create a copy of TankHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TankHistoryModelImplCopyWith<_$TankHistoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BorewellHistoryModel _$BorewellHistoryModelFromJson(Map<String, dynamic> json) {
  return _BorewellHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$BorewellHistoryModel {
  int get siteId => throw _privateConstructorUsedError;
  double get level => throw _privateConstructorUsedError;
  int get bwId => throw _privateConstructorUsedError;
  int get rssi => throw _privateConstructorUsedError;
  int? get time => throw _privateConstructorUsedError;

  /// Serializes this BorewellHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BorewellHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BorewellHistoryModelCopyWith<BorewellHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BorewellHistoryModelCopyWith<$Res> {
  factory $BorewellHistoryModelCopyWith(BorewellHistoryModel value,
          $Res Function(BorewellHistoryModel) then) =
      _$BorewellHistoryModelCopyWithImpl<$Res, BorewellHistoryModel>;
  @useResult
  $Res call({int siteId, double level, int bwId, int rssi, int? time});
}

/// @nodoc
class _$BorewellHistoryModelCopyWithImpl<$Res,
        $Val extends BorewellHistoryModel>
    implements $BorewellHistoryModelCopyWith<$Res> {
  _$BorewellHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BorewellHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? level = null,
    Object? bwId = null,
    Object? rssi = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as double,
      bwId: null == bwId
          ? _value.bwId
          : bwId // ignore: cast_nullable_to_non_nullable
              as int,
      rssi: null == rssi
          ? _value.rssi
          : rssi // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BorewellHistoryModelImplCopyWith<$Res>
    implements $BorewellHistoryModelCopyWith<$Res> {
  factory _$$BorewellHistoryModelImplCopyWith(_$BorewellHistoryModelImpl value,
          $Res Function(_$BorewellHistoryModelImpl) then) =
      __$$BorewellHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int siteId, double level, int bwId, int rssi, int? time});
}

/// @nodoc
class __$$BorewellHistoryModelImplCopyWithImpl<$Res>
    extends _$BorewellHistoryModelCopyWithImpl<$Res, _$BorewellHistoryModelImpl>
    implements _$$BorewellHistoryModelImplCopyWith<$Res> {
  __$$BorewellHistoryModelImplCopyWithImpl(_$BorewellHistoryModelImpl _value,
      $Res Function(_$BorewellHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BorewellHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? level = null,
    Object? bwId = null,
    Object? rssi = null,
    Object? time = freezed,
  }) {
    return _then(_$BorewellHistoryModelImpl(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as double,
      bwId: null == bwId
          ? _value.bwId
          : bwId // ignore: cast_nullable_to_non_nullable
              as int,
      rssi: null == rssi
          ? _value.rssi
          : rssi // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BorewellHistoryModelImpl implements _BorewellHistoryModel {
  const _$BorewellHistoryModelImpl(
      {this.siteId = 1,
      this.level = 0.0,
      this.bwId = 1,
      this.rssi = 1,
      this.time});

  factory _$BorewellHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BorewellHistoryModelImplFromJson(json);

  @override
  @JsonKey()
  final int siteId;
  @override
  @JsonKey()
  final double level;
  @override
  @JsonKey()
  final int bwId;
  @override
  @JsonKey()
  final int rssi;
  @override
  final int? time;

  @override
  String toString() {
    return 'BorewellHistoryModel(siteId: $siteId, level: $level, bwId: $bwId, rssi: $rssi, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BorewellHistoryModelImpl &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.bwId, bwId) || other.bwId == bwId) &&
            (identical(other.rssi, rssi) || other.rssi == rssi) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, siteId, level, bwId, rssi, time);

  /// Create a copy of BorewellHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BorewellHistoryModelImplCopyWith<_$BorewellHistoryModelImpl>
      get copyWith =>
          __$$BorewellHistoryModelImplCopyWithImpl<_$BorewellHistoryModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BorewellHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _BorewellHistoryModel implements BorewellHistoryModel {
  const factory _BorewellHistoryModel(
      {final int siteId,
      final double level,
      final int bwId,
      final int rssi,
      final int? time}) = _$BorewellHistoryModelImpl;

  factory _BorewellHistoryModel.fromJson(Map<String, dynamic> json) =
      _$BorewellHistoryModelImpl.fromJson;

  @override
  int get siteId;
  @override
  double get level;
  @override
  int get bwId;
  @override
  int get rssi;
  @override
  int? get time;

  /// Create a copy of BorewellHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BorewellHistoryModelImplCopyWith<_$BorewellHistoryModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

LogsModel _$LogsModelFromJson(Map<String, dynamic> json) {
  return _LogsModel.fromJson(json);
}

/// @nodoc
mixin _$LogsModel {
  int get device_id => throw _privateConstructorUsedError;
  String get remark => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get siteId => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this LogsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LogsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LogsModelCopyWith<LogsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogsModelCopyWith<$Res> {
  factory $LogsModelCopyWith(LogsModel value, $Res Function(LogsModel) then) =
      _$LogsModelCopyWithImpl<$Res, LogsModel>;
  @useResult
  $Res call(
      {int device_id,
      String remark,
      String name,
      int type,
      String siteId,
      String id,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$LogsModelCopyWithImpl<$Res, $Val extends LogsModel>
    implements $LogsModelCopyWith<$Res> {
  _$LogsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LogsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device_id = null,
    Object? remark = null,
    Object? name = null,
    Object? type = null,
    Object? siteId = null,
    Object? id = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      device_id: null == device_id
          ? _value.device_id
          : device_id // ignore: cast_nullable_to_non_nullable
              as int,
      remark: null == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LogsModelImplCopyWith<$Res>
    implements $LogsModelCopyWith<$Res> {
  factory _$$LogsModelImplCopyWith(
          _$LogsModelImpl value, $Res Function(_$LogsModelImpl) then) =
      __$$LogsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int device_id,
      String remark,
      String name,
      int type,
      String siteId,
      String id,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$LogsModelImplCopyWithImpl<$Res>
    extends _$LogsModelCopyWithImpl<$Res, _$LogsModelImpl>
    implements _$$LogsModelImplCopyWith<$Res> {
  __$$LogsModelImplCopyWithImpl(
      _$LogsModelImpl _value, $Res Function(_$LogsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LogsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device_id = null,
    Object? remark = null,
    Object? name = null,
    Object? type = null,
    Object? siteId = null,
    Object? id = null,
    Object? time = freezed,
  }) {
    return _then(_$LogsModelImpl(
      device_id: null == device_id
          ? _value.device_id
          : device_id // ignore: cast_nullable_to_non_nullable
              as int,
      remark: null == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogsModelImpl implements _LogsModel {
  const _$LogsModelImpl(
      {this.device_id = 1,
      this.remark = "",
      this.name = "",
      this.type = 1,
      this.siteId = "",
      this.id = "",
      @TimestampNullableConverter() this.time});

  factory _$LogsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogsModelImplFromJson(json);

  @override
  @JsonKey()
  final int device_id;
  @override
  @JsonKey()
  final String remark;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final int type;
  @override
  @JsonKey()
  final String siteId;
  @override
  @JsonKey()
  final String id;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'LogsModel(device_id: $device_id, remark: $remark, name: $name, type: $type, siteId: $siteId, id: $id, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogsModelImpl &&
            (identical(other.device_id, device_id) ||
                other.device_id == device_id) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, device_id, remark, name, type, siteId, id, time);

  /// Create a copy of LogsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LogsModelImplCopyWith<_$LogsModelImpl> get copyWith =>
      __$$LogsModelImplCopyWithImpl<_$LogsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogsModelImplToJson(
      this,
    );
  }
}

abstract class _LogsModel implements LogsModel {
  const factory _LogsModel(
      {final int device_id,
      final String remark,
      final String name,
      final int type,
      final String siteId,
      final String id,
      @TimestampNullableConverter() final DateTime? time}) = _$LogsModelImpl;

  factory _LogsModel.fromJson(Map<String, dynamic> json) =
      _$LogsModelImpl.fromJson;

  @override
  int get device_id;
  @override
  String get remark;
  @override
  String get name;
  @override
  int get type;
  @override
  String get siteId;
  @override
  String get id;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of LogsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LogsModelImplCopyWith<_$LogsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FlowHistoryModel _$FlowHistoryModelFromJson(Map<String, dynamic> json) {
  return _FlowHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$FlowHistoryModel {
  int get mins => throw _privateConstructorUsedError;
  int get volume => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this FlowHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FlowHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FlowHistoryModelCopyWith<FlowHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlowHistoryModelCopyWith<$Res> {
  factory $FlowHistoryModelCopyWith(
          FlowHistoryModel value, $Res Function(FlowHistoryModel) then) =
      _$FlowHistoryModelCopyWithImpl<$Res, FlowHistoryModel>;
  @useResult
  $Res call(
      {int mins, int volume, @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$FlowHistoryModelCopyWithImpl<$Res, $Val extends FlowHistoryModel>
    implements $FlowHistoryModelCopyWith<$Res> {
  _$FlowHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FlowHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mins = null,
    Object? volume = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as int,
      volume: null == volume
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlowHistoryModelImplCopyWith<$Res>
    implements $FlowHistoryModelCopyWith<$Res> {
  factory _$$FlowHistoryModelImplCopyWith(_$FlowHistoryModelImpl value,
          $Res Function(_$FlowHistoryModelImpl) then) =
      __$$FlowHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int mins, int volume, @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$FlowHistoryModelImplCopyWithImpl<$Res>
    extends _$FlowHistoryModelCopyWithImpl<$Res, _$FlowHistoryModelImpl>
    implements _$$FlowHistoryModelImplCopyWith<$Res> {
  __$$FlowHistoryModelImplCopyWithImpl(_$FlowHistoryModelImpl _value,
      $Res Function(_$FlowHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlowHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mins = null,
    Object? volume = null,
    Object? time = freezed,
  }) {
    return _then(_$FlowHistoryModelImpl(
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as int,
      volume: null == volume
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FlowHistoryModelImpl implements _FlowHistoryModel {
  const _$FlowHistoryModelImpl(
      {this.mins = 0,
      this.volume = 0,
      @TimestampNullableConverter() this.time});

  factory _$FlowHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$FlowHistoryModelImplFromJson(json);

  @override
  @JsonKey()
  final int mins;
  @override
  @JsonKey()
  final int volume;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'FlowHistoryModel(mins: $mins, volume: $volume, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlowHistoryModelImpl &&
            (identical(other.mins, mins) || other.mins == mins) &&
            (identical(other.volume, volume) || other.volume == volume) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, mins, volume, time);

  /// Create a copy of FlowHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FlowHistoryModelImplCopyWith<_$FlowHistoryModelImpl> get copyWith =>
      __$$FlowHistoryModelImplCopyWithImpl<_$FlowHistoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FlowHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _FlowHistoryModel implements FlowHistoryModel {
  const factory _FlowHistoryModel(
          {final int mins,
          final int volume,
          @TimestampNullableConverter() final DateTime? time}) =
      _$FlowHistoryModelImpl;

  factory _FlowHistoryModel.fromJson(Map<String, dynamic> json) =
      _$FlowHistoryModelImpl.fromJson;

  @override
  int get mins;
  @override
  int get volume;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of FlowHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FlowHistoryModelImplCopyWith<_$FlowHistoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SensorHistoryModel _$SensorHistoryModelFromJson(Map<String, dynamic> json) {
  return _SensorHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$SensorHistoryModel {
  double get t1 => throw _privateConstructorUsedError;
  double get t2 => throw _privateConstructorUsedError;
  double get t3 => throw _privateConstructorUsedError;
  double get t4 => throw _privateConstructorUsedError;
  double get cl => throw _privateConstructorUsedError;
  double get ph => throw _privateConstructorUsedError;
  double get flow => throw _privateConstructorUsedError;
  double get cum_flow => throw _privateConstructorUsedError;
  int? get time1 => throw _privateConstructorUsedError;
  int? get time2 => throw _privateConstructorUsedError;
  int? get time3 => throw _privateConstructorUsedError;
  int? get time4 => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this SensorHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SensorHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SensorHistoryModelCopyWith<SensorHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SensorHistoryModelCopyWith<$Res> {
  factory $SensorHistoryModelCopyWith(
          SensorHistoryModel value, $Res Function(SensorHistoryModel) then) =
      _$SensorHistoryModelCopyWithImpl<$Res, SensorHistoryModel>;
  @useResult
  $Res call(
      {double t1,
      double t2,
      double t3,
      double t4,
      double cl,
      double ph,
      double flow,
      double cum_flow,
      int? time1,
      int? time2,
      int? time3,
      int? time4,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$SensorHistoryModelCopyWithImpl<$Res, $Val extends SensorHistoryModel>
    implements $SensorHistoryModelCopyWith<$Res> {
  _$SensorHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SensorHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? t1 = null,
    Object? t2 = null,
    Object? t3 = null,
    Object? t4 = null,
    Object? cl = null,
    Object? ph = null,
    Object? flow = null,
    Object? cum_flow = null,
    Object? time1 = freezed,
    Object? time2 = freezed,
    Object? time3 = freezed,
    Object? time4 = freezed,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      t1: null == t1
          ? _value.t1
          : t1 // ignore: cast_nullable_to_non_nullable
              as double,
      t2: null == t2
          ? _value.t2
          : t2 // ignore: cast_nullable_to_non_nullable
              as double,
      t3: null == t3
          ? _value.t3
          : t3 // ignore: cast_nullable_to_non_nullable
              as double,
      t4: null == t4
          ? _value.t4
          : t4 // ignore: cast_nullable_to_non_nullable
              as double,
      cl: null == cl
          ? _value.cl
          : cl // ignore: cast_nullable_to_non_nullable
              as double,
      ph: null == ph
          ? _value.ph
          : ph // ignore: cast_nullable_to_non_nullable
              as double,
      flow: null == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as double,
      cum_flow: null == cum_flow
          ? _value.cum_flow
          : cum_flow // ignore: cast_nullable_to_non_nullable
              as double,
      time1: freezed == time1
          ? _value.time1
          : time1 // ignore: cast_nullable_to_non_nullable
              as int?,
      time2: freezed == time2
          ? _value.time2
          : time2 // ignore: cast_nullable_to_non_nullable
              as int?,
      time3: freezed == time3
          ? _value.time3
          : time3 // ignore: cast_nullable_to_non_nullable
              as int?,
      time4: freezed == time4
          ? _value.time4
          : time4 // ignore: cast_nullable_to_non_nullable
              as int?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SensorHistoryModelImplCopyWith<$Res>
    implements $SensorHistoryModelCopyWith<$Res> {
  factory _$$SensorHistoryModelImplCopyWith(_$SensorHistoryModelImpl value,
          $Res Function(_$SensorHistoryModelImpl) then) =
      __$$SensorHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double t1,
      double t2,
      double t3,
      double t4,
      double cl,
      double ph,
      double flow,
      double cum_flow,
      int? time1,
      int? time2,
      int? time3,
      int? time4,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$SensorHistoryModelImplCopyWithImpl<$Res>
    extends _$SensorHistoryModelCopyWithImpl<$Res, _$SensorHistoryModelImpl>
    implements _$$SensorHistoryModelImplCopyWith<$Res> {
  __$$SensorHistoryModelImplCopyWithImpl(_$SensorHistoryModelImpl _value,
      $Res Function(_$SensorHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SensorHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? t1 = null,
    Object? t2 = null,
    Object? t3 = null,
    Object? t4 = null,
    Object? cl = null,
    Object? ph = null,
    Object? flow = null,
    Object? cum_flow = null,
    Object? time1 = freezed,
    Object? time2 = freezed,
    Object? time3 = freezed,
    Object? time4 = freezed,
    Object? time = freezed,
  }) {
    return _then(_$SensorHistoryModelImpl(
      t1: null == t1
          ? _value.t1
          : t1 // ignore: cast_nullable_to_non_nullable
              as double,
      t2: null == t2
          ? _value.t2
          : t2 // ignore: cast_nullable_to_non_nullable
              as double,
      t3: null == t3
          ? _value.t3
          : t3 // ignore: cast_nullable_to_non_nullable
              as double,
      t4: null == t4
          ? _value.t4
          : t4 // ignore: cast_nullable_to_non_nullable
              as double,
      cl: null == cl
          ? _value.cl
          : cl // ignore: cast_nullable_to_non_nullable
              as double,
      ph: null == ph
          ? _value.ph
          : ph // ignore: cast_nullable_to_non_nullable
              as double,
      flow: null == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as double,
      cum_flow: null == cum_flow
          ? _value.cum_flow
          : cum_flow // ignore: cast_nullable_to_non_nullable
              as double,
      time1: freezed == time1
          ? _value.time1
          : time1 // ignore: cast_nullable_to_non_nullable
              as int?,
      time2: freezed == time2
          ? _value.time2
          : time2 // ignore: cast_nullable_to_non_nullable
              as int?,
      time3: freezed == time3
          ? _value.time3
          : time3 // ignore: cast_nullable_to_non_nullable
              as int?,
      time4: freezed == time4
          ? _value.time4
          : time4 // ignore: cast_nullable_to_non_nullable
              as int?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SensorHistoryModelImpl implements _SensorHistoryModel {
  const _$SensorHistoryModelImpl(
      {this.t1 = 0.0,
      this.t2 = 0.0,
      this.t3 = 0.0,
      this.t4 = 0.0,
      this.cl = 0.0,
      this.ph = 0.0,
      this.flow = 0.0,
      this.cum_flow = 0.0,
      this.time1,
      this.time2,
      this.time3,
      this.time4,
      @TimestampNullableConverter() this.time});

  factory _$SensorHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SensorHistoryModelImplFromJson(json);

  @override
  @JsonKey()
  final double t1;
  @override
  @JsonKey()
  final double t2;
  @override
  @JsonKey()
  final double t3;
  @override
  @JsonKey()
  final double t4;
  @override
  @JsonKey()
  final double cl;
  @override
  @JsonKey()
  final double ph;
  @override
  @JsonKey()
  final double flow;
  @override
  @JsonKey()
  final double cum_flow;
  @override
  final int? time1;
  @override
  final int? time2;
  @override
  final int? time3;
  @override
  final int? time4;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'SensorHistoryModel(t1: $t1, t2: $t2, t3: $t3, t4: $t4, cl: $cl, ph: $ph, flow: $flow, cum_flow: $cum_flow, time1: $time1, time2: $time2, time3: $time3, time4: $time4, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SensorHistoryModelImpl &&
            (identical(other.t1, t1) || other.t1 == t1) &&
            (identical(other.t2, t2) || other.t2 == t2) &&
            (identical(other.t3, t3) || other.t3 == t3) &&
            (identical(other.t4, t4) || other.t4 == t4) &&
            (identical(other.cl, cl) || other.cl == cl) &&
            (identical(other.ph, ph) || other.ph == ph) &&
            (identical(other.flow, flow) || other.flow == flow) &&
            (identical(other.cum_flow, cum_flow) ||
                other.cum_flow == cum_flow) &&
            (identical(other.time1, time1) || other.time1 == time1) &&
            (identical(other.time2, time2) || other.time2 == time2) &&
            (identical(other.time3, time3) || other.time3 == time3) &&
            (identical(other.time4, time4) || other.time4 == time4) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, t1, t2, t3, t4, cl, ph, flow,
      cum_flow, time1, time2, time3, time4, time);

  /// Create a copy of SensorHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SensorHistoryModelImplCopyWith<_$SensorHistoryModelImpl> get copyWith =>
      __$$SensorHistoryModelImplCopyWithImpl<_$SensorHistoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SensorHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _SensorHistoryModel implements SensorHistoryModel {
  const factory _SensorHistoryModel(
          {final double t1,
          final double t2,
          final double t3,
          final double t4,
          final double cl,
          final double ph,
          final double flow,
          final double cum_flow,
          final int? time1,
          final int? time2,
          final int? time3,
          final int? time4,
          @TimestampNullableConverter() final DateTime? time}) =
      _$SensorHistoryModelImpl;

  factory _SensorHistoryModel.fromJson(Map<String, dynamic> json) =
      _$SensorHistoryModelImpl.fromJson;

  @override
  double get t1;
  @override
  double get t2;
  @override
  double get t3;
  @override
  double get t4;
  @override
  double get cl;
  @override
  double get ph;
  @override
  double get flow;
  @override
  double get cum_flow;
  @override
  int? get time1;
  @override
  int? get time2;
  @override
  int? get time3;
  @override
  int? get time4;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of SensorHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SensorHistoryModelImplCopyWith<_$SensorHistoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
