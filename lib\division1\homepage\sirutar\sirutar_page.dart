

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';

import '../../../common/widgets/widgets.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';

class SirutarPage extends HookConsumerWidget{

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    ref.listen(refreshProvider, (previous, next) async {
      ref
          .read(divisionStateProvider(prefs.getSiteId()).notifier)
          .getSetting(prefs.getSiteId());
    });



    final motor10State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status ==
              motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    return siteSetting.when(
        success: (setting, message) {
          return BaseScaffold(
              showLeftIcon: false,
              appbarText: "Sirutar Site",
              child:  CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          height: 60,
                          color: Colors.blue,
                          child: Center(
                            child: Text(
                              "मुलको पानी",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 40,
                                  color: Colors.blueAccent,
                                ),
                                NewMotorUnit(motorId: '10',motorModel: motor10State.value,motorSettingModel: setting!.motor10,ratio: 3,),
                                Container(
                                  width: 20,
                                  height: 40,
                                  color: Colors.blueAccent,

                                ),
                              ],
                            ),
                            Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 40,
                                  color: Colors.blueAccent,
                                ),
                                NewMotorUnit(
                                  active: false,

                                ),
                                Container(
                                  width: 20,
                                  height: 40,
                                  color: Colors.blueAccent,
                                ),
                              ],
                            ),
                          ],
                        ),
                        Container(
                          height: 30,
                          width: double.infinity,
                          color: Colors.blue,
                        ),
                        Container(
                          height: 30,
                          width: 20,
                          color: Colors.blue,
                        ),
                        NewTankUnit(1, "10", setting!.tank10!, 170, 200),
                      ],
                    ),
                  )
                ],
              ));
        },
        unInitialized: () {
          return Container();
        },
        error: (erro) {
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());


  }

}