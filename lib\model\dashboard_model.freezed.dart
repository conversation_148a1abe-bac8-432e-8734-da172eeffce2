// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DashboardModel _$DashboardModelFromJson(Map<String, dynamic> json) {
  return _DashboardModel.fromJson(json);
}

/// @nodoc
mixin _$DashboardModel {
  int? get float_status => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get float_updated_at => throw _privateConstructorUsedError;
  int? get motor_status => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get motor_updated_at => throw _privateConstructorUsedError;
  int? get timer_status => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get timer_updated_at => throw _privateConstructorUsedError;
  int? get level => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get level_updated_at => throw _privateConstructorUsedError;
  int? get maxHeight => throw _privateConstructorUsedError;
  int? get minHeight => throw _privateConstructorUsedError;
  int? get actualHeight => throw _privateConstructorUsedError;
  int? get timer => throw _privateConstructorUsedError;
  int? get mode => throw _privateConstructorUsedError;
  double? get current => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get current_updated_at => throw _privateConstructorUsedError;

  /// Serializes this DashboardModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DashboardModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DashboardModelCopyWith<DashboardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DashboardModelCopyWith<$Res> {
  factory $DashboardModelCopyWith(
          DashboardModel value, $Res Function(DashboardModel) then) =
      _$DashboardModelCopyWithImpl<$Res, DashboardModel>;
  @useResult
  $Res call(
      {int? float_status,
      @TimestampNullableConverter() DateTime? float_updated_at,
      int? motor_status,
      @TimestampNullableConverter() DateTime? motor_updated_at,
      int? timer_status,
      @TimestampNullableConverter() DateTime? timer_updated_at,
      int? level,
      @TimestampNullableConverter() DateTime? level_updated_at,
      int? maxHeight,
      int? minHeight,
      int? actualHeight,
      int? timer,
      int? mode,
      double? current,
      @TimestampNullableConverter() DateTime? current_updated_at});
}

/// @nodoc
class _$DashboardModelCopyWithImpl<$Res, $Val extends DashboardModel>
    implements $DashboardModelCopyWith<$Res> {
  _$DashboardModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DashboardModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float_status = freezed,
    Object? float_updated_at = freezed,
    Object? motor_status = freezed,
    Object? motor_updated_at = freezed,
    Object? timer_status = freezed,
    Object? timer_updated_at = freezed,
    Object? level = freezed,
    Object? level_updated_at = freezed,
    Object? maxHeight = freezed,
    Object? minHeight = freezed,
    Object? actualHeight = freezed,
    Object? timer = freezed,
    Object? mode = freezed,
    Object? current = freezed,
    Object? current_updated_at = freezed,
  }) {
    return _then(_value.copyWith(
      float_status: freezed == float_status
          ? _value.float_status
          : float_status // ignore: cast_nullable_to_non_nullable
              as int?,
      float_updated_at: freezed == float_updated_at
          ? _value.float_updated_at
          : float_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      motor_status: freezed == motor_status
          ? _value.motor_status
          : motor_status // ignore: cast_nullable_to_non_nullable
              as int?,
      motor_updated_at: freezed == motor_updated_at
          ? _value.motor_updated_at
          : motor_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      timer_status: freezed == timer_status
          ? _value.timer_status
          : timer_status // ignore: cast_nullable_to_non_nullable
              as int?,
      timer_updated_at: freezed == timer_updated_at
          ? _value.timer_updated_at
          : timer_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      level_updated_at: freezed == level_updated_at
          ? _value.level_updated_at
          : level_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxHeight: freezed == maxHeight
          ? _value.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      minHeight: freezed == minHeight
          ? _value.minHeight
          : minHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      actualHeight: freezed == actualHeight
          ? _value.actualHeight
          : actualHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      timer: freezed == timer
          ? _value.timer
          : timer // ignore: cast_nullable_to_non_nullable
              as int?,
      mode: freezed == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as double?,
      current_updated_at: freezed == current_updated_at
          ? _value.current_updated_at
          : current_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DashboardModelImplCopyWith<$Res>
    implements $DashboardModelCopyWith<$Res> {
  factory _$$DashboardModelImplCopyWith(_$DashboardModelImpl value,
          $Res Function(_$DashboardModelImpl) then) =
      __$$DashboardModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? float_status,
      @TimestampNullableConverter() DateTime? float_updated_at,
      int? motor_status,
      @TimestampNullableConverter() DateTime? motor_updated_at,
      int? timer_status,
      @TimestampNullableConverter() DateTime? timer_updated_at,
      int? level,
      @TimestampNullableConverter() DateTime? level_updated_at,
      int? maxHeight,
      int? minHeight,
      int? actualHeight,
      int? timer,
      int? mode,
      double? current,
      @TimestampNullableConverter() DateTime? current_updated_at});
}

/// @nodoc
class __$$DashboardModelImplCopyWithImpl<$Res>
    extends _$DashboardModelCopyWithImpl<$Res, _$DashboardModelImpl>
    implements _$$DashboardModelImplCopyWith<$Res> {
  __$$DashboardModelImplCopyWithImpl(
      _$DashboardModelImpl _value, $Res Function(_$DashboardModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DashboardModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float_status = freezed,
    Object? float_updated_at = freezed,
    Object? motor_status = freezed,
    Object? motor_updated_at = freezed,
    Object? timer_status = freezed,
    Object? timer_updated_at = freezed,
    Object? level = freezed,
    Object? level_updated_at = freezed,
    Object? maxHeight = freezed,
    Object? minHeight = freezed,
    Object? actualHeight = freezed,
    Object? timer = freezed,
    Object? mode = freezed,
    Object? current = freezed,
    Object? current_updated_at = freezed,
  }) {
    return _then(_$DashboardModelImpl(
      float_status: freezed == float_status
          ? _value.float_status
          : float_status // ignore: cast_nullable_to_non_nullable
              as int?,
      float_updated_at: freezed == float_updated_at
          ? _value.float_updated_at
          : float_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      motor_status: freezed == motor_status
          ? _value.motor_status
          : motor_status // ignore: cast_nullable_to_non_nullable
              as int?,
      motor_updated_at: freezed == motor_updated_at
          ? _value.motor_updated_at
          : motor_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      timer_status: freezed == timer_status
          ? _value.timer_status
          : timer_status // ignore: cast_nullable_to_non_nullable
              as int?,
      timer_updated_at: freezed == timer_updated_at
          ? _value.timer_updated_at
          : timer_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      level_updated_at: freezed == level_updated_at
          ? _value.level_updated_at
          : level_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxHeight: freezed == maxHeight
          ? _value.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      minHeight: freezed == minHeight
          ? _value.minHeight
          : minHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      actualHeight: freezed == actualHeight
          ? _value.actualHeight
          : actualHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      timer: freezed == timer
          ? _value.timer
          : timer // ignore: cast_nullable_to_non_nullable
              as int?,
      mode: freezed == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as double?,
      current_updated_at: freezed == current_updated_at
          ? _value.current_updated_at
          : current_updated_at // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DashboardModelImpl implements _DashboardModel {
  _$DashboardModelImpl(
      {this.float_status,
      @TimestampNullableConverter() this.float_updated_at,
      this.motor_status,
      @TimestampNullableConverter() this.motor_updated_at,
      this.timer_status,
      @TimestampNullableConverter() this.timer_updated_at,
      this.level,
      @TimestampNullableConverter() this.level_updated_at,
      this.maxHeight,
      this.minHeight,
      this.actualHeight,
      this.timer,
      this.mode,
      this.current,
      @TimestampNullableConverter() this.current_updated_at});

  factory _$DashboardModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DashboardModelImplFromJson(json);

  @override
  final int? float_status;
  @override
  @TimestampNullableConverter()
  final DateTime? float_updated_at;
  @override
  final int? motor_status;
  @override
  @TimestampNullableConverter()
  final DateTime? motor_updated_at;
  @override
  final int? timer_status;
  @override
  @TimestampNullableConverter()
  final DateTime? timer_updated_at;
  @override
  final int? level;
  @override
  @TimestampNullableConverter()
  final DateTime? level_updated_at;
  @override
  final int? maxHeight;
  @override
  final int? minHeight;
  @override
  final int? actualHeight;
  @override
  final int? timer;
  @override
  final int? mode;
  @override
  final double? current;
  @override
  @TimestampNullableConverter()
  final DateTime? current_updated_at;

  @override
  String toString() {
    return 'DashboardModel(float_status: $float_status, float_updated_at: $float_updated_at, motor_status: $motor_status, motor_updated_at: $motor_updated_at, timer_status: $timer_status, timer_updated_at: $timer_updated_at, level: $level, level_updated_at: $level_updated_at, maxHeight: $maxHeight, minHeight: $minHeight, actualHeight: $actualHeight, timer: $timer, mode: $mode, current: $current, current_updated_at: $current_updated_at)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DashboardModelImpl &&
            (identical(other.float_status, float_status) ||
                other.float_status == float_status) &&
            (identical(other.float_updated_at, float_updated_at) ||
                other.float_updated_at == float_updated_at) &&
            (identical(other.motor_status, motor_status) ||
                other.motor_status == motor_status) &&
            (identical(other.motor_updated_at, motor_updated_at) ||
                other.motor_updated_at == motor_updated_at) &&
            (identical(other.timer_status, timer_status) ||
                other.timer_status == timer_status) &&
            (identical(other.timer_updated_at, timer_updated_at) ||
                other.timer_updated_at == timer_updated_at) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.level_updated_at, level_updated_at) ||
                other.level_updated_at == level_updated_at) &&
            (identical(other.maxHeight, maxHeight) ||
                other.maxHeight == maxHeight) &&
            (identical(other.minHeight, minHeight) ||
                other.minHeight == minHeight) &&
            (identical(other.actualHeight, actualHeight) ||
                other.actualHeight == actualHeight) &&
            (identical(other.timer, timer) || other.timer == timer) &&
            (identical(other.mode, mode) || other.mode == mode) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.current_updated_at, current_updated_at) ||
                other.current_updated_at == current_updated_at));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      float_status,
      float_updated_at,
      motor_status,
      motor_updated_at,
      timer_status,
      timer_updated_at,
      level,
      level_updated_at,
      maxHeight,
      minHeight,
      actualHeight,
      timer,
      mode,
      current,
      current_updated_at);

  /// Create a copy of DashboardModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DashboardModelImplCopyWith<_$DashboardModelImpl> get copyWith =>
      __$$DashboardModelImplCopyWithImpl<_$DashboardModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DashboardModelImplToJson(
      this,
    );
  }
}

abstract class _DashboardModel implements DashboardModel {
  factory _DashboardModel(
          {final int? float_status,
          @TimestampNullableConverter() final DateTime? float_updated_at,
          final int? motor_status,
          @TimestampNullableConverter() final DateTime? motor_updated_at,
          final int? timer_status,
          @TimestampNullableConverter() final DateTime? timer_updated_at,
          final int? level,
          @TimestampNullableConverter() final DateTime? level_updated_at,
          final int? maxHeight,
          final int? minHeight,
          final int? actualHeight,
          final int? timer,
          final int? mode,
          final double? current,
          @TimestampNullableConverter() final DateTime? current_updated_at}) =
      _$DashboardModelImpl;

  factory _DashboardModel.fromJson(Map<String, dynamic> json) =
      _$DashboardModelImpl.fromJson;

  @override
  int? get float_status;
  @override
  @TimestampNullableConverter()
  DateTime? get float_updated_at;
  @override
  int? get motor_status;
  @override
  @TimestampNullableConverter()
  DateTime? get motor_updated_at;
  @override
  int? get timer_status;
  @override
  @TimestampNullableConverter()
  DateTime? get timer_updated_at;
  @override
  int? get level;
  @override
  @TimestampNullableConverter()
  DateTime? get level_updated_at;
  @override
  int? get maxHeight;
  @override
  int? get minHeight;
  @override
  int? get actualHeight;
  @override
  int? get timer;
  @override
  int? get mode;
  @override
  double? get current;
  @override
  @TimestampNullableConverter()
  DateTime? get current_updated_at;

  /// Create a copy of DashboardModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DashboardModelImplCopyWith<_$DashboardModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
