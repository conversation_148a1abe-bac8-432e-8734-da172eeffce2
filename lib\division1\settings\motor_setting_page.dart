

import 'dart:developer' as logging;
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/homepage/motor_list_page.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/settings/label_text.dart';
import 'package:si/division1/settings/schedule_setting_page.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../constants/assets_manager.dart';
import '../../provider/auth_provider.dart';
import '../homepage/tank_list_page.dart';
import '../model/motor_setting_model.dart';
import 'add_site_page.dart';


enum Options { Normal, Automatic, Scheduler }

class MotorSettingPage extends HookConsumerWidget {
  MotorSettingPage( this.motorId);

  final String? motorId;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  Future<void> _handleMotorSetting(
      BuildContext context,
      WidgetRef ref, {
        required TextEditingController motorNameController,
        required TextEditingController maxCurrentController,
        required TextEditingController minCurrentController,
        required TextEditingController currentThresholdController,
        required TextEditingController thAmpController,
        required TextEditingController CFController,
        required TextEditingController uplaodTimeController,
        required bool hasVoltage,
        required bool isActive,
        required int mode,
        String? hrs,
        String? mins,
      }) async {


    final repo = ref.read(divisionRepositoryProvider);
    final rtdbrepo = ref.read(divisionRtdbServiceProvider);
    final siteId = ref.read(sharedPreferencesServiceProvider).getSiteId();

        // final email = emailController.text;
        // final password = passwordController.text;
        // Logger().d('Email : $email \nPassword: $password');
        try {
          EasyLoading.show(status: 'Updating..');
          await repo.setMotorSetting(siteId: siteId,
              motorId:'motor'+motorId!,
              motorName:motorNameController.text.trim().toString(),
              thAmps: double.parse(thAmpController.text.trim().toString()),
              mode: mode,
              hrs: hrs,
              mins: mins,
              noLoadCurrent:double.parse(minCurrentController.text.trim().toString()),
              overloadCurrent:double.parse(maxCurrentController.text.trim().toString()),
              uploadTime: int.parse(uplaodTimeController.text.trim().toString()),
              currentThreshold: double.parse(currentThresholdController.text.trim().toString()),
              CF: int.parse(CFController.text.trim().toString()),
              hasVoltage: hasVoltage,);
          await rtdbrepo.updateMotorParameter(siteId:siteId,motorId:motorId!,
              maxCurrent:double.parse(maxCurrentController.text.trim().toString()),
              name: motorNameController.text.trim().toString(),
              TH_Amps: double.parse(thAmpController.text.trim().toString()),
              minCurrent: double.parse(minCurrentController.text.trim().toString()),
              mode: mode,
              hrs: hrs,
              mins: mins,
              uploadTime: int.parse(uplaodTimeController.text.trim().toString()),
              currentThreshold: double.parse(currentThresholdController.text.trim().toString()),
              CF: int.parse(CFController.text.trim().toString()),
              hasVoltage: hasVoltage,);
          final realSetting = await repo.getSiteSetting(siteId);
          await ref.read(localStorageProvider).setSettings(realSetting!);
          EasyLoading.dismiss();
          Navigator.pop(context,'refresh');

        } catch (e) {
          EasyLoading.showError('Error');
          //Logger().e('create user from email', e);
        }
        FocusScope.of(context).unfocus();

  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motorNameController = useTextEditingController();
    final maxCurrentController = useTextEditingController();
    final minCurrentController = useTextEditingController();
    final thAmpController = useTextEditingController();
    final currentThresholdController = useTextEditingController();
    final cfController = useTextEditingController();
    final uploadTimeController = useTextEditingController();
    final hasVoltage = useState(false);
    final motorSettingModel = getMotorModel(motorId ?? "10", ref.read(localStorageProvider).getSettings());

    final mode = useState(Options.Normal);
    final hrArray = useState<List<int>>([]);
    final minArray = useState<List<int>>([]);

    logging.log(motorSettingModel.toString());

    useEffect(() {
      // print('SessionDEt : ${sessionModel.id}');
      motorNameController.text = motorSettingModel.name.toString();
      maxCurrentController.text = motorSettingModel.max_current.toString();
      minCurrentController.text = motorSettingModel.min_current.toString();
      currentThresholdController.text = motorSettingModel.current_threshold.toString();
      cfController.text = motorSettingModel.CF.toString();
      hasVoltage.value = motorSettingModel.has_voltage;
      thAmpController.text = motorSettingModel.TH_Amps.toString();
      uploadTimeController.text = motorSettingModel.upload_time.toString();
      mode.value = motorSettingModel.mode==3 ? Options.Scheduler : motorSettingModel.mode == 2 ?  Options.Automatic : Options.Normal;
      if(mode.value == Options.Scheduler){
        hrArray.value = motorSettingModel.hrs.split(',').map((str) => int.parse(str)).toList();;
        minArray.value = motorSettingModel.mins.split(',').map((str) => int.parse(str)).toList();
      }
     // logging.log(maxCurrentController.value.toString());
      return null;
    }, const []);

    return BaseScaffold(
      showAppBar: true,
      appbarText: "Motor Setting",
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildTextFormFieldSection(context, ref,
                      motorNameController,
                      maxCurrentController,
                      minCurrentController,
                      currentThresholdController,
                      thAmpController,
                      cfController,
                      uploadTimeController,),
                    HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final voltageEnable = useState<bool>(hasVoltage.value);
                      return ListTile(
                        title: const Text('Has Voltage'),
                        trailing: CupertinoSwitch(
                          value: voltageEnable.value,
                          onChanged: (bool value) {
                            voltageEnable.value = value;
                            hasVoltage.value = voltageEnable.value;
                          },
                        ),
                        onTap: () {
                          voltageEnable.value = !voltageEnable.value;
                          hasVoltage.value = voltageEnable.value;
                        },
                      );
                    }),
                    Align(
                      alignment: Alignment.topLeft,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text("Motor Mode", style: TextStyle(fontSize: 18, )),
                        )),
                    ...Options.values.map((option) {
                      return RadioListTile<Options>(
                        title: Text(option.toString().split('.').last), // Display enum value name
                        value: option,
                        groupValue: mode.value,
                        onChanged: (value) {
                            mode.value = value!;
                            logging.log(mode.value.toString());
                        },
                      );
                    }).toList(),
                    if(mode.value==Options.Scheduler && hrArray.value.isNotEmpty)
                      Align(
                          alignment: Alignment.topLeft,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            child: Text("Saved time schedule", style: TextStyle(fontSize: 14, )),
                          )),
                      if(mode.value==Options.Scheduler && hrArray.value.isNotEmpty)
                      for(var i =0 ; i< hrArray.value.length ; i+=2)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Container(
                            height: 40,
                            child: Row(
                              children: [
                                GestureDetector(
                                    onTap: () async {

                                    },
                                    child: Text("Start Time")),
                                Container(width: 16,),
                                Text("${getHr(hrArray.value[i], minArray.value[i]==1 ? 15 : minArray.value[i] == 2 ? 30 : minArray.value[i]==3 ? 45 : 0)}"),
                                Expanded(
                                  child: Container(),
                                ),
                                GestureDetector(
                                    onTap: () async {

                                    },
                                    child: Text("Stop Time")),
                                Container(width: 16,),
                                Text("${getHr(hrArray.value[i+1], minArray.value[i+1]==1 ? 15 : minArray.value[i+1] == 2 ? 30 : minArray.value[i+1]==3 ? 45 : 0)}"),

                              ],
                            ),
                          ),
                        ),
                    mode.value == Options.Scheduler ?
                        ScheduleSettingPage(motorId!,
                                (hrArray, minArray) {
                                  _handleMotorSetting(context,ref,
                                      motorNameController: motorNameController,
                                      maxCurrentController: maxCurrentController,
                                      minCurrentController: minCurrentController,
                                      CFController: cfController,
                                      currentThresholdController: currentThresholdController,
                                      hasVoltage: hasVoltage.value,
                                      thAmpController: thAmpController,
                                      uplaodTimeController: uploadTimeController,
                                      hrs: hrArray,
                                      mins: minArray,
                                      mode: mode.value == Options.Automatic ? 2 : mode.value == Options.Scheduler ? 3 : 1,
                                      isActive: true);

                        }) : Container(),

        /* HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final levelSwitch = useState<bool>(.value);
                      return ListTile(
                        title: const Text('Level sensor'),
                        trailing: CupertinoSwitch(
                          value: levelSwitch.value,
                          onChanged: (bool value) {
                            levelSwitch.value = value;
                            sensorEnable.value = levelSwitch.value;
                          },
                        ),
                        onTap: () {
                          levelSwitch.value = !levelSwitch.value;
                          sensorEnable.value = levelSwitch.value;
                        },
                      );
                    }),
                    HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final activeSwitch = useState<bool>(activeEnable.value);
                      return ListTile(
                        title: const Text('Active'),
                        trailing: CupertinoSwitch(
                          value: activeSwitch.value,
                          onChanged: (bool value) {
                            activeSwitch.value = value;
                            activeEnable.value = activeSwitch.value;
                          },
                        ),
                        onTap: () {
                          activeSwitch.value = !activeSwitch.value;
                          activeEnable.value = activeSwitch.value;
                        },
                      );
                    }),*/
                    if(mode.value != Options.Scheduler)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24.0, vertical: 24),
                      child: PrimaryButton(
                        text: 'Save',
                        height: 54,
                        onPressed: () async {
                          _handleMotorSetting(context,ref,
                              motorNameController: motorNameController,
                              maxCurrentController: maxCurrentController,
                              minCurrentController: minCurrentController,
                              CFController: cfController,
                              currentThresholdController: currentThresholdController,
                              hasVoltage: hasVoltage.value,
                              thAmpController: thAmpController,
                              uplaodTimeController: uploadTimeController,
                              hrs: '',
                              mins:'',
                              mode: mode.value == Options.Automatic ? 2 : mode.value == Options.Scheduler ? 3 : 1,
                              isActive: true);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

        ],
      ),

    );
  }

  Widget _buildTextFormFieldSection(
      BuildContext context,
      WidgetRef ref,
      TextEditingController motorNameController,
      TextEditingController maxCurrentController,
      TextEditingController minCurrentController,
      TextEditingController currentThresholdController,
      TextEditingController thAmpController,
      TextEditingController CFController,
      TextEditingController uploadTimeController,
      ) {
    // final FocusNode? pwdFocusNode = FocusNode();

    // EdgeFunctionController edgeFunctionController = EdgeFunctionController();
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          gapH16,
          LabelText(label: "Enter Motor Name"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "motorName",
                controller: motorNameController,
                hintText: "Motor Name",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter Maximum current"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "maxCurrent",
                controller: maxCurrentController,
                hintText: "Maximum Current",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter Minimum current"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "minCurrent",
                controller: minCurrentController,
                hintText: "Minimum Current",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter Current Threshold"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "current_threshold",
                controller: currentThresholdController,
                hintText: "Current Threshold",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter TH Ams (Device)"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "th_ams",
                controller: thAmpController,
                hintText: "TH Ams",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter CF"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "cf",
                controller: CFController,
                hintText: "CF",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter Upload time"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "uploadTime",
                controller: uploadTimeController,
                hintText: "Enter upload time in mins",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),

        ],
      );
  }
}

