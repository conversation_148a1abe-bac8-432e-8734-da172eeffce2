import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/primary_button.dart';
import 'package:si/services/shared_preferences_service.dart';

import 'water_flow_switch.dart';
import 'water_painter.dart';

class ValvePage extends StatefulHookConsumerWidget {
  const ValvePage({Key? key}) : super(key: key);

  @override
  _ValvePageState createState() => _ValvePageState();
}

class _ValvePageState extends ConsumerState<ValvePage> with TickerProviderStateMixin{

  late AnimationController _controller1;
  late AnimationController _controller2;
  late AnimationController _controller3;
  late AnimationController _controller4;
  late Animation<double> _animation1;
  late Animation<double> _animation2;
  late Animation<double> _animation3;
  late Animation<double> _animation4;
  late AnimationController _waterController1;
  late AnimationController _waterController2;
  late AnimationController _waterController3;
  late AnimationController _waterController4;
  late Animation<double> _waterAnimation1;
  late Animation<double> _waterAnimation2;
  late Animation<double> _waterAnimation3;
  late Animation<double> _waterAnimation4;


  @override
  void initState() {
    super.initState();
    _controller1 =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation1 = Tween<double>(begin: 0, end: 1).animate(_controller1)
      ..addListener(() {
        setState(() {});
      });
    _controller2 =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation2 = Tween<double>(begin: 0, end: 1).animate(_controller2)
      ..addListener(() {
        setState(() {});
      });
    _controller3 =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation3 = Tween<double>(begin: 0, end: 1).animate(_controller3)
      ..addListener(() {
        setState(() {});
      });

    _controller4 =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation4 = Tween<double>(begin: 0, end: 1).animate(_controller4)
      ..addListener(() {
        setState(() {});
      });

    _waterController1 =
        AnimationController(duration: Duration(seconds: 1), vsync: this);
    _waterAnimation1 = Tween<double>(begin: 0, end: 1).animate(_waterController1);
    _waterController2 =
        AnimationController(duration: Duration(seconds: 1), vsync: this);
    _waterAnimation2 = Tween<double>(begin: 0, end: 1).animate(_waterController2);
    _waterController3 =
        AnimationController(duration: Duration(seconds: 1), vsync: this);
    _waterAnimation3 = Tween<double>(begin: 0, end: 1).animate(_waterController3);
    _waterController4 =
        AnimationController(duration: Duration(seconds: 1), vsync: this);
    _waterAnimation4 = Tween<double>(begin: 0, end: 1).animate(_waterController4);
    //_controller.forward(); // Repeat the animation
  }

  @override
  void dispose() {
    _controller1.dispose();
    _controller2.dispose();
    _controller3.dispose();
    _controller4.dispose();
    _waterController1.dispose();
    _waterController2.dispose();
    _waterController3.dispose();
    _waterController4.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final prefs = ref.read(sharedPreferencesServiceProvider);
    return BaseScaffold(
      appbarText: "Valves",
      showLeftIcon: false,
      child: SingleChildScrollView(
        child: Column(
          children: [
            Column(
              children: [
                SizedBox(
                  height: 16,
                ),
                Stack(
                  children: [
                    CustomPaint(
                      size: Size(width, 100),
                      painter: WaterFlowPainter(_animation1.value,4, width),
                    ),
                    AnimatedBuilder(
                      animation: _waterController1,
                      builder: (context, child) {
                        return Positioned(
                          top: 25,
                          left: 75,
                          child: Container(
                            width: 20,
                            height: 50 * _waterAnimation1.value,
                            color: Colors.black,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                WaterFlowSwitch(outputId: "site1/output1",onOffSwitch: (onOff){
                  if(onOff){
                    _controller1.forward();
                    _waterController1.reverse();
                  }else{
                    _controller1.reverse();
                    _waterController1.forward();
                  }
                },),
                SizedBox(
                  height: 16,
                ),
                Container(
                  color: Colors.grey,
                  height: 5,
                )
              ],
            ),
            Column(
              children: [
                Stack(
                  children: [
                    CustomPaint(
                      size: Size(width, 100),
                      painter: WaterFlowPainter(_animation2.value,4, width),
                    ),
                    AnimatedBuilder(
                      animation: _waterController2,
                      builder: (context, child) {
                        return Positioned(
                          top: 25,
                          left: 75,
                          child: Container(
                            width: 20,
                            height: 50 * _waterAnimation2.value,
                            color: Colors.black,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                WaterFlowSwitch(outputId: "site1/output2",onOffSwitch: (onOff){
                     if(onOff){
                    _controller2.forward();
                    _waterController2.reverse();
                  }else{
                    _controller2.reverse();
                    _waterController2.forward();

                  }
                },),
                SizedBox(
                  height: 16,
                ),
                Container(
                  color: Colors.grey,
                  height: 5,
                )
              ],
            ),
            Column(
              children: [
                Stack(
                  children: [
                    CustomPaint(
                      size: Size(width, 100),
                      painter: WaterFlowPainter(_animation3.value,4, width),
                    ),
                    AnimatedBuilder(
                      animation: _waterController3,
                      builder: (context, child) {
                        return Positioned(
                          top: 25,
                          left: 75,
                          child: Container(
                            width: 20,
                            height: 50 * _waterAnimation3.value,
                            color: Colors.black,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                WaterFlowSwitch(outputId: "site1/output3",onOffSwitch: (onOff){
                  if(onOff){
                    _controller3.forward();
                    _waterController3.reverse();
                  }else{
                    _controller3.reverse();
                    _waterController3.forward();

                  }
                },),
                SizedBox(
                  height: 16,
                ),
                Container(
                  color: Colors.grey,
                  height: 5,
                )
              ],
            ),
            Column(
              children: [
                Stack(
                  children: [
                    CustomPaint(
                      size: Size(width, 100),
                      painter: WaterFlowPainter(_animation4.value,4, width),
                    ),
                    AnimatedBuilder(
                      animation: _waterController4,
                      builder: (context, child) {
                        return Positioned(
                          top: 25,
                          left: 75,
                          child: Container(
                            width: 20,
                            height: 50 * _waterAnimation4.value,
                            color: Colors.black,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                WaterFlowSwitch(outputId: "site1/output4",onOffSwitch: (onOff){
                  if(onOff){
                    _controller4.forward();
                    _waterController4.reverse();
                  }else{
                    _controller4.reverse();
                    _waterController4.forward();

                  }
                },),
                SizedBox(
                  height: 16,
                ),
                Container(
                  color: Colors.grey,
                  height: 5,
                )
              ],
            ),

          ],
        ),
      ),
    );
  }


}

