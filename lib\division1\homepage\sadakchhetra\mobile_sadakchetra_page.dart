


import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../constants/app_sizes.dart';
import '../../../kawosoti/schema/widgets/widgets.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import '../widgets/inactive_valve_widget.dart';
import '../widgets/source_widget.dart';

class MobileSadakChhetraPage extends HookConsumerWidget{

  final DivisionSettingModel? setting;

  const MobileSadakChhetraPage(this.setting);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);

    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          gapH16,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                children: [
                  SumpWellWidget(height: 100, width: 90),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewMotorUnit(
                    motorId: '10',
                    motorSettingModel: setting!.motor10!,
                    motorModel: motor10State.value,
                    ratio: 3,
                  ),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(1, "10", setting!.tank10!, 170, 140),
                  Container(
                    height: 320,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                ],
              ),
              Column(
                children: [
                  SumpWellWidget(height: 100, width: 90),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewMotorUnit(
                    motorId: '11',
                    motorSettingModel: setting!.motor11!,
                    motorModel: motor11State.value,
                    ratio: 3,
                  ),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      child: SedimentTank(height: 100, width: 150)),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(1, "11", setting!.tank11!, 150, 140),
                  Row(
                    children: [
                      Column(
                        children: [
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          InactiveValveWidget(name: '3" inch', ratio: 4),
                        ],
                      ),
                      Column(
                        children: [
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          InactiveValveWidget(name: '3" inch', ratio: 4),
                        ],
                      )
                    ],
                  ),
                ],
              )
            ],
          ),
          Container(
            height: 20,
            width: double.infinity,
            color: Colors.blueAccent,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  Container(
                    color: Colors.blueAccent,
                    height: 40,
                    width: 20,
                  ),
                  InactiveValveWidget(name: '2" valve', ratio: 4)
                ],
              ),
              gapW16,
              Column(
                children: [
                  Container(
                    color: Colors.blueAccent,
                    height: 40,
                    width: 20,
                  ),
                  InactiveValveWidget(name: '2" valve', ratio: 4)

                ],
              ),
              gapW16,
              Column(
                children: [
                  Container(
                    color: Colors.blueAccent,
                    height: 40,
                    width: 20,
                  ),
                  InactiveValveWidget(name: '2" valve', ratio: 4)

                ],
              ),
            ],
          ),
          gapH16,
        ],
      ),
    );
  }
}