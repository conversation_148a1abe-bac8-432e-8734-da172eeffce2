


import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/division1/homepage/new_common_motor_unit.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';
import 'package:si/division1/homepage/widgets/distribution_widget.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../model/motor_model.dart';

class SchoolPage extends HookConsumerWidget{

  const SchoolPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 40,
                width: double.infinity,
                color: Colors.blueAccent,
                child: Center(
                  child: Text("खार्पा / डाडा गाउ", style: TextStyle(color: Colors.white),),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "40",
                        ratio: 4,
                        valveSettingModel: setting.valve40!,
                      ),
                      gapH16,
                      NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "41",
                        ratio: 4,
                        valveSettingModel: setting.valve41!,
                      ),
                      gapH64,
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        height: 20,
                        width: 30,
                        color: Colors.blueAccent,
                      ),
                      gapH80,
                      Container(
                        height: 20,
                        width: 30,
                        color: Colors.blueAccent,
                      ),
                      gapH64,
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        height: 40,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                      NewTankUnit(2, "12", setting.tank12!, 250, 120),
                      Container(
                        height: 60,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                     /* NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "44",
                        ratio: 4,
                        valveSettingModel: setting.valve44!,
                      ),*/
                      Container(
                        height: 80,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        height: 20,
                        width: 30,
                        color: Colors.blueAccent,
                      ),
                      gapH80,
                      Container(
                        height: 20,
                        width: 30,
                        color: Colors.blueAccent,
                      ),
                      gapH64,
                    ],
                  ),
                  Column(
                    children: [
                      NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "42",
                        ratio: 4,
                        valveSettingModel: setting.valve42!,
                      ),
                      gapH16,
                      NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "43",
                        ratio: 4,
                        valveSettingModel: setting.valve43!,
                      ),
                      gapH80,
                    ],
                  ),
                ],
              ),
              Container(
                width: double.infinity,
                height: 30,
                color: Colors.blueAccent,
              ),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  gapW24,
                  Expanded(
                    flex:1,
                      child: UpdownDistributionWidget(name: "तामांग टोल", height: 100, width: 40,turn: 0,)),
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        Container(
                          width: 20,
                          height: 30,
                          color: Colors.blueAccent,
                        ),
                        NewTankUnit(2, "30", setting.tank30!, 180, 250),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 30,
                                  color: Colors.blueAccent,
                                ),
                                NewCommonValveUnit(
                                  valveModel: MotorModel(),
                                  valveId: "22",
                                  ratio: 4,
                                  valveSettingModel: setting.valve22!,
                                ),
                                gapH80
                              ],
                            ),
                            Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 30,
                                  color: Colors.blueAccent,
                                ),
                                NewCommonValveUnit(
                                  valveModel: MotorModel(),
                                  valveId: "50",
                                  ratio: 4,
                                  valveSettingModel: setting.valve50!,
                                ),
                                UpdownDistributionWidget(name: "", height: 80, width: 30, turn: 0,)


                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),


                ],
              )


            ],
          ),
        )
      ],
    );
  }

}