


import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'provider/auth_provider.dart';

class AuthStateNotifier extends StateNotifier<User?> {
  final StateNotifierProviderRef ref;

  StreamSubscription<User?>? _authStateSubscription;

  AuthStateNotifier(this.ref) : super(null) {
    _authStateSubscription?.cancel();
    _authStateSubscription = ref.read(authRepositoryProvider).authStateChanges.listen((user) => state = user);
    // _read(authRepositoryProvider)
    //     .idTokenChanges
    //     .listen((user) => state = user);
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }

  Future<void> signOut() async {
    await ref.read(authRepositoryProvider).signOut();
  }

}
