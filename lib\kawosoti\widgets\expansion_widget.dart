

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../utils/app_colors.dart';

class ExpansionWidget extends HookConsumerWidget{

  const ExpansionWidget({required this.title, required this.children, this.icon});

  final String title;
  final List<Widget> children;
  final Widget? icon;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return  ExpansionTileTheme(
      data: ExpansionTileThemeData(
        collapsedIconColor:
        AppColors.themeColor, //This property is for collapsed arrow
      ),
      child: ExpansionTile(
        iconColor: AppColors.themeColor,
        leading: icon,
        title: Text(
          title,
          style: TextStyle(
              fontSize: 16, color: AppColors.onSideMenu),
        ),
        collapsedTextColor: Colors.black,
        textColor: Colors.black,
        children:children,
      ),
    );
  }

}