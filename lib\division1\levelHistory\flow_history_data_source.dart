

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:si/division1/repository/division_repository.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/services/firestore_path.dart';

import '../../../datatable/paginated_data_table_2.dart';
import '../../../utils/format.dart';
import '../model/level_history_model.dart';

// Async datasource for AsynPaginatedDataTabke2 example. Based on AsyncDataTableSource which
/// is an extension to FLutter's DataTableSource and aimed at solving
/// saync data fetching scenarious by paginated table (such as using Web API)
class FlowHistorySiteDataSource extends AsyncDataTableSource {

  FlowHistorySiteDataSource({this.userType=1}) {
    print('SiteDataSource created');
  }



  FlowHistorySiteDataSource.empty({this.userType=1}) {
    _empty = true;
    print('SiteDataSource.empty created');
  }

  FlowHistorySiteDataSource.error({this.userType=1}) {
    print('SiteDataSource.error created');
  }

  bool _empty = false;
  final int userType;


  DateTime? _siteDate;
  int tankId = 1;

  DateTime? startSiteDate;
  DateTime? endSiteDate;

  String  siteId = FirestorePath.schemaOne;

  int   totalVolume = 0;



  set startDate(DateTime? value) {
    startSiteDate = value;
    notifyListeners();
  }

  set SiteId(String value){
    siteId = value;
    notifyListeners();
  }

  set TankId(int id){
    tankId = id;
    notifyListeners();
  }

  int startingIndex = 0;

  List<FlowHistoryModel> listFlowModel = [];

  final IDivisionRepository _repo = DivisionRepository();


  @override
  Future<AsyncRowsResponse> getRows(int start, int end) async {
    var index = 0;
    final today = DateTime.now();

    var x = start==0  ? await _repo.getFlowHistory(siteId: siteId, tankId: tankId,date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0)) :
    start<startingIndex ?  listFlowModel.skip(start).take(end).toList()   :await _repo.getFlowHistory(siteId:siteId,tankId: tankId,
        startAfterDate: _siteDate, date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0));
    if(start==0){
      listFlowModel.clear();
    }
    final count = await _repo.getFlowHistoryLength(siteId:siteId,flowId:tankId,dateTime:(startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0)));
    if(count==0){
      _empty = true;
      return AsyncRowsResponse(listFlowModel.length, []);
    }else{
      int volume = 0;
      var r = AsyncRowsResponse(
          count,
          x.map((siteModel) {
            volume += siteModel?.volume ?? 0;
            index++;
            return DataRow(
              key: ValueKey<String>(siteModel!.time!.millisecondsSinceEpoch.toString()),
              selected: false,
              onSelectChanged: (value) {

              },
              cells: getSiteCells( start, index, siteModel),
            );
          }).toList());

      startingIndex =  start;
      if(start>=startingIndex){
        for(final model in x){
          listFlowModel.add(model!);
        }
      }
      _siteDate = (x.last!).time;
      totalVolume = volume;

      return r;
    }

  }
}


List<DataCell> getSiteCells(int start, int index, FlowHistoryModel siteModel) {
  return [
    DataCell(Text(Format.onlydate(siteModel.time!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.mins.toString()+" mins",style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text((siteModel.volume/1000).toStringAsFixed(2) + " kl",style: TextStyle(fontSize: 14,color : Colors.black),)),
    ];

}