


import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/model/dashboard_model.dart';
import 'package:si/provider/dashboard_provider.dart';

import '../../constants/app_sizes.dart';
import '../../utils/format.dart';

class FloatStatus extends HookConsumerWidget{

  FloatStatus(this.dashboardModel, this.site);

  final DashboardModel dashboardModel;
  final String site;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onDoubleTap: (){
        if(ref.read(userState).role==3){
          if(site=="site1"){
            context.push('/siteOne/history');
          }else{
            context.push('/siteTwo/history');
          }
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          gapH16,
          Image.asset(
            "assets/icons/ic_float.webp",
            width: 150,
            height: 150,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: vertPadding16,
                child: Transform.scale(
                  scale: 2,
                  child: Radio(
                    value: 0,
                    groupValue: 0,
                    onChanged: (value) {},
                    activeColor: dashboardModel.float_status==0 ? Colors.red : Colors.grey,
                  ),
                ),
              ),
              gapW24,
              Padding(
                padding: vertPadding16,
                child: Transform.scale(
                  scale: 2,
                  child: Radio(
                    value: 0,
                    groupValue: 0,
                    onChanged: (value) {},
                    activeColor: dashboardModel.float_status==1 ? Colors.green : Colors.grey,
                  ),
                ),
              )
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              Format.date(dashboardModel.float_updated_at!),
              style: TextStyle(color: Colors.black),
            ),
          ),
          gapH16,
        ],
      ),
    );
  }

}