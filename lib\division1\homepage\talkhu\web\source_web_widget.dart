


import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SourceWebWidget extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 430,
      width: 1250,
      color: Colors.white,
      child: Stack(
        children: [
          Positioned(
            left: 80,
            top: 64,
            child: Image.asset(
              "assets/icons/open_well.png",
              height : 220,
              width: 250,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }

}