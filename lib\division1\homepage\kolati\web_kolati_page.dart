

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:text_scroll/text_scroll.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../provider/auth_provider.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../new_motor_unit.dart';
import '../widgets/distribution_widget.dart';
import '../widgets/pipeline.dart';
import '../widgets/tank_widget.dart';
import '../widgets/valve_widget.dart';

class WebKolatiPage extends HookConsumerWidget {
  const WebKolatiPage(this.setting, {super.key});

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var size = MediaQuery.of(context).size;
    final  appDimension = ref.read(kolatiDimensionProvider);
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));

    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    return BaseScaffold(
        showAppBar: false,
        child: Center(
          child: Container(
              height: size.height,
              width: size.width,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF87CEFA), // Light Blue (Sky color)
                    Color(0xFFE0FFFF),
                    Color(0xFF87CEFA), // Light Blue (Sky color)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: siteSetting.when(success: (data, message){
                return  Column(
                  children: [
                    gapH8,
                    Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            children: [
                              Image.asset("assets/icons/logo.webp",height: 80, width: 80),
                              gapW8,
                              Column(
                                children: [
                                  Text("Powered by"),
                                  gapH8,
                                  Text("स्वदेशी इनोवेशन", style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),),

                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            child: const Center(
                              child: TextScroll(
                                'कोलाती खानेपानी उपभोक्ता तथा सरसफाइ समिती, सिन्धुपाल्चोक',
                                mode: TextScrollMode.bouncing,
                                velocity: Velocity(pixelsPerSecond: Offset(150, 0)),
                                delayBefore: Duration(milliseconds: 500),
                                pauseBetween: Duration(milliseconds: 50),
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            children: [
                              Image.asset("assets/icons/emblem.webp", height:80,width:80,),
                              Text("खानेपानी तथा ढल \nव्यवस्थापन आयोजना, सिन्धुपाल्चोक",
                                textAlign: TextAlign.center,
                                style: TextStyle(fontSize: 18,fontWeight: FontWeight.bold),),
                            ],
                          ),
                        ),

                      ],
                    ),
                    Expanded(
                      child: Stack(
                        children: [
                          
                          Positioned(
                            left: 8,
                            top: appDimension.sourceY(x: size.width , y: size.height),
                            child: Container(
                              height: appDimension.sourceHeight(x :size.width, y:size.height),
                              width: 110,
                              child: Column(
                                children: [
                                  Expanded(
                                    child: RotatedBox(
                                      quarterTurns: 2,
                                      child: LoadingAnimationWidget.prograssiveDots(
                                        color: Colors.lightBlueAccent,
                                        size: 40,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: RotatedBox(
                                      quarterTurns: 2,
                                      child: LoadingAnimationWidget.prograssiveDots(
                                        color: Colors.lightBlueAccent,
                                        size: 40,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: RotatedBox(
                                      quarterTurns: 2,
                                      child: LoadingAnimationWidget.prograssiveDots(
                                        color: Colors.lightBlueAccent,
                                        size: 40,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: RotatedBox(
                                      quarterTurns: 2,
                                      child: LoadingAnimationWidget.prograssiveDots(
                                        color: Colors.lightBlueAccent,
                                        size: 40,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                              top: appDimension.sourceY(x :size.width, y:size.height),
                              left: appDimension.sourceX(x :size.width, y:size.height),
                              child: Stack(
                                children: [
                                  Image.asset("assets/icons/dam.png",
                                    fit: BoxFit.fill,
                                    height: appDimension.sourceHeight(x :size.width, y:size.height),
                                    width: appDimension.sourceWidth(x :size.width, y:size.height),),
                                  Positioned(
                                      left: 80,
                                      top: 32,
                                      child: Text("मुहानको पानी", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),))
                                ],
                              )),
                          Positioned(
                            key: const ValueKey('T10'),
                            left: appDimension.tank10X(x: size.width, y: size.height),
                            top: appDimension.tank10Y(x: size.width, y: size.height),
                            child:  TankBlock(
                              tankId: "10",
                              tankSettingModel: data!.tank10!,
                              height: appDimension.tank10Height(
                                  x: size.width, y: size.height),
                              width: appDimension.tank10Width(
                                  x: size.width, y: size.height),
                            ),
                          ),
                          Positioned(
                            key: const ValueKey('T11'),
                            left: appDimension.tank11X(x: size.width, y: size.height),
                            top: appDimension.tank11Y(x: size.width, y: size.height),
                            child:  TankBlock(
                              tankId: "11",
                              tankSettingModel: data!.tank11!,
                              height: appDimension.tank11Height(
                                  x: size.width, y: size.height),
                              width: appDimension.tank11Width(
                                  x: size.width, y: size.height),
                            ),
                          ),
                          Positioned(
                            right: 8,
                            top: 8,
                            child: IconButton(
                              icon: const Icon(Icons.logout_outlined),
                              onPressed: () async {
                                final bool didRequestSignOut =
                                    await showAlertDialog(
                                      context: context,
                                      title: 'Logout',
                                      content:
                                      'Are you sure you want to logout?',
                                      cancelActionText: 'Cancel',
                                      defaultActionText: 'Yes',
                                    ) ??
                                        false;
                                if (didRequestSignOut == true) {
                                  try {
                                    EasyLoading.show(status: 'Logging Out');
                                    await ref
                                        .read(authRepositoryProvider)
                                        .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                                    await ref
                                        .read(authRepositoryProvider)
                                        .signOut();
                                    // MyApp.of(context).authService.authenticated = true;
                                    // onLoginCallback?.call(true);
                                    // AutoRouter.of(context).push(const DashboardRouter());
                                    EasyLoading.dismiss();
                                    context.replace('/login');
                                  } catch (err) {
                                    print(err);
                                    // debugPrint("Error :$err");
                                  }
                                }

                              },
                            ),
                          ),
                          Positioned(
                            top: appDimension.l1Y(x: size.width, y: size.height),
                            left: appDimension.l1X(x: size.width, y: size.height),
                            child: HorizontalPipeline(
                              length: appDimension.l1Length(x: size.width, y: size.height),
                              name: "L1",
                              isFlowing: true,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              leftFlow: false,
                              singleFlow: true,
                            ),
                          ),
                          Positioned(
                            top: appDimension.l2Y(x: size.width, y: size.height),
                            left: appDimension.l2X(x: size.width, y: size.height),
                            child: HorizontalPipeline(
                              length: appDimension.l2Length(x: size.width, y: size.height),
                              name: "L2",
                              isFlowing: true,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              leftFlow: false,
                              singleFlow: true,
                            ),
                          ),
                          Positioned(
                            key: const ValueKey('M10'),
                            left: appDimension.motor10X(x: size.width, y: size.height),
                            top: appDimension.motor10Y(x: size.width, y: size.height),
                            child:NewWebMotorUnit(motorId: '10',
                              motorModel: motor10State.value,motorSettingModel: setting.motor10,
                              width: 200,),

                          ),
                          Positioned(
                            top: appDimension.l3Y(x: size.width, y: size.height),
                            left: appDimension.l3X(x: size.width, y: size.height),
                            child: HorizontalPipeline(
                              length: appDimension.l3Length(x: size.width, y: size.height),
                              name: "L3",
                              isFlowing: motor10State.value.device_status==3 ? true : false,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              leftFlow: false,
                              singleFlow: true,
                            ),
                          ),
                          Positioned(
                            key: const ValueKey('M11'),
                            left: appDimension.motor11X(x: size.width, y: size.height),
                            top: appDimension.motor11Y(x: size.width, y: size.height),
                            child:NewWebMotorUnit(motorId: '11',
                              motorModel: motor11State.value,motorSettingModel: setting.motor11,
                              width: 200,),

                          ),
                          Positioned(
                            top: appDimension.l4Y(x: size.width, y: size.height),
                            left: appDimension.l4X(x: size.width, y: size.height),
                            child: HorizontalPipeline(
                              length: appDimension.l4Length(x: size.width, y: size.height),
                              name: "L4",
                              isFlowing: true,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              leftFlow: false,
                              singleFlow: true,
                            ),
                          ),

                          Positioned(
                            key: const ValueKey('T12'),
                            left: appDimension.tank12X(x: size.width, y: size.height),
                            top: appDimension.tank12Y(x: size.width, y: size.height),
                            child:  TankBlock(
                              tankId: "12",
                              tankSettingModel: data!.tank12!,
                              height: appDimension.tank12Height(
                                  x: size.width, y: size.height),
                              width: appDimension.tank12Width(
                                  x: size.width, y: size.height),
                            ),
                          ),
                          Positioned(
                            key: const ValueKey('T14'),
                            left: appDimension.tank14X(x: size.width, y: size.height),
                            top: appDimension.tank14Y(x: size.width, y: size.height),
                            child:  InactiveTankBlock(
                              height: appDimension.tank14Height(
                                  x: size.width, y: size.height),
                              width: appDimension.tank14Width(
                                  x: size.width, y: size.height),
                            ),
                          ),
                          Positioned(
                            top: appDimension.l5Y(x: size.width, y: size.height),
                            left: appDimension.l5X(x: size.width, y: size.height),
                            child: VerticalPipeline(
                              length: appDimension.l5Length(x: size.width, y: size.height),
                              name: "L5",
                              isFlowing: motor11State.value?.device_status==3 ?  true : false,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              singleFlow: true,
                            ),
                          ),
                          Positioned(
                            top: appDimension.v1Y(x: size.width, y: size.height),
                            left: appDimension.v1X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "10",
                              valveModel: MotorModel(),
                              name: "Valve 10",
                            ),
                          ),
                          Positioned(
                            top: appDimension.v2Y(x: size.width, y: size.height),
                            left: appDimension.v2X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "20",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            top: appDimension.v3Y(x: size.width, y: size.height),
                            left: appDimension.v3X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),

                          Positioned(
                            top: appDimension.v4Y(x: size.width, y: size.height),
                            left: appDimension.v4X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),

                          Positioned(
                            top: appDimension.v5Y(x: size.width, y: size.height),
                            left: appDimension.v5X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            top: appDimension.l6Y(x: size.width, y: size.height),
                            left: appDimension.l6X(x: size.width, y: size.height),
                            child: HorizontalPipeline(
                              length: appDimension.l6Length(x: size.width, y: size.height),
                              name: "L6",
                              isFlowing: true,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              leftFlow: true,
                              singleFlow: false,
                            ),
                          ),
                          Positioned(
                            top: appDimension.v6Y(x: size.width, y: size.height),
                            left: appDimension.v6X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            top: appDimension.l7Y(x: size.width, y: size.height),
                            left: appDimension.l7X(x: size.width, y: size.height),
                            child: HorizontalPipeline(
                              length: appDimension.l6Length(x: size.width, y: size.height),
                              name: "L7",
                              isFlowing: true,
                              isTop: true,
                              isRight: false,
                              isBottom: false,
                              isLeft: false,
                              leftFlow: true,
                              singleFlow: false,
                            ),
                          ),
                          Positioned(
                            top: appDimension.v7Y(x: size.width, y: size.height),
                            left: appDimension.v7X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            top: appDimension.v8Y(x: size.width, y: size.height),
                            left: appDimension.v8X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            top: appDimension.v9Y(x: size.width, y: size.height),
                            left: appDimension.v9X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            top: appDimension.v43Y(x: size.width, y: size.height),
                            left: appDimension.v43X(x: size.width, y: size.height),
                            child: ScadaValveWidget(
                              size: size,
                              role: prefs.getUserRole(),
                              width: appDimension.valveWidth(x: size.width, y: size.height),
                              height: appDimension.valveHeight(x: size.width, y: size.height),
                              showProgress: false,
                              valveId: "30",
                              valveModel: MotorModel(),
                              name: "Valve 20",
                            ),
                          ),
                          Positioned(
                            key: const ValueKey('D1'),
                            top: appDimension.d1Y(x: size.width, y: size.height),
                            left: appDimension.d1X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D1", height: 60, width: 100, turn: 0,),
                          ),
                          Positioned(
                            key: const ValueKey('D2'),
                            top: appDimension.d2Y(x: size.width, y: size.height),
                            left: appDimension.d2X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D2", height: 60, width: 100, turn: 0,),
                          ),
                          Positioned(
                            key: const ValueKey('D3'),
                            top: appDimension.d3Y(x: size.width, y: size.height),
                            left: appDimension.d3X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D3", height: 60, width: 100, turn: 0,),
                          ),

                          Positioned(
                            key: const ValueKey('D5'),
                            top: appDimension.d5Y(x: size.width, y: size.height),
                            left: appDimension.d5X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D5", height: 60, width: 100,),
                          ),

                          Positioned(
                            key: const ValueKey('D10'),
                            top: appDimension.d10Y(x: size.width, y: size.height),
                            left: appDimension.d10X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D10", height: 60, width: 100,),
                          ),

                          Positioned(
                            key: const ValueKey('D9'),
                            top: appDimension.d9Y(x: size.width, y: size.height),
                            left: appDimension.d9X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D9", height: 60, width: 100,),
                          ),

                          Positioned(
                            key: const ValueKey('D8'),
                            top: appDimension.d8Y(x: size.width, y: size.height),
                            left: appDimension.d8X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D8", height: 60, width: 100,),
                          ),

                          Positioned(
                            key: const ValueKey('D7'),
                            top: appDimension.d7Y(x: size.width, y: size.height),
                            left: appDimension.d7X(x: size.width, y: size.height),
                            child:RotatedDistributionWidget(name: "D7", height: 60, width: 100,turn : 0),

                          ),






                        ],
                      ),
                    ),
                  ],
                );
              },
                  unInitialized: ()=> Container(),
                  error: (er){
                    return Container();
                  },
                  unauthorized: ()=>Container(),
                  loading: ()=>LoadingIndicator())




          ),
        )
    );
  }


}