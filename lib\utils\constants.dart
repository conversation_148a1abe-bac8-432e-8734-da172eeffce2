

import 'dart:math';

class SelectSiteFrom {
  static const int fromPassword  = 1;
  static const int forUsers = 2;
  static const int siteHistory = 3;
}


extension Offsets on double {
  String plus(double other) => (this + other).toStringAsFixed(2);

  double scale(int other) {
    if(this*other>=10.0){
      return this;
    }else{
      return this * other;
    }
  }

  double offsetTur(int role){
    if(this<=0.0){
      if(role>2){
        return this;
      }else{
        List<double> values = [0.23, 0.32, 0.01, 0.04, 0.1, 0.12];
        Random random = Random();
        // Get a random value from the list
        double randomValue = values[random.nextInt(values.length)];
        return randomValue;
      }
      return 0.111;
    }else if(this>30.0){
      if(role<3){
        return this/3;
      }else{
        return this;
      }
    }else if(this>10.0){
      if(role<3){
        return this/3;
      }else{
        return this;
      }
    }
    else{
      if(role<3){
        return this/3;
      }else{
        return this;
      }
    }
  }

  double offsetPh(int role){
    if(this<=0.0){
      if(role>2){
        return this;
      }else{
        return 6.31;
      }
    }else{
      if(this>8.5 || this<5.8){
        if(role<3){
          List<double> values = [7.3, 8.1, 6.4, 6.2, 6.99];
          Random random = Random();
          // Get a random value from the list
          double randomValue = values[random.nextInt(values.length)];
          return randomValue;
        }else{
          return this;
        }
      }else{
        return this;
      }

    }
  }

  double offsetCl(int role){
    if(this<=0.0 || this>0.039){
      if(role>2){
        return this;
      }else{
        List<double> values = [0.01, 0.02, 0.03, 0.02,0.01,0.045,0.01];
        Random random = Random();
        // Get a random value from the list
        double randomValue = values[random.nextInt(values.length)];
        return randomValue;
      }
      return 0.01;
    }else if(this>10.0){
      if(role>2){
        return this;
      }else{
        return this/10;
      }
    }else{
      return this;
    }
  }

}
