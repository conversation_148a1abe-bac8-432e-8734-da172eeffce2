import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'custom_loading_indicator.dart';

class CustomNetworkImage extends HookConsumerWidget {
  const CustomNetworkImage(
      {Key? key, this.imagePath, this.imageHeight, this.imageWidth, this.fit, this.imageWebHeight, this.imageWebWidth})
      : super(key: key);

  final String? imagePath;
  final double? imageHeight;
  final double? imageWidth;
  final double? imageWebHeight;
  final double? imageWebWidth;
  final BoxFit? fit;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Image.network(
      imagePath ?? '',
      height: 100,
      width: 100,
      fit: fit ?? BoxFit.cover,
      loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
        if (loadingProgress == null) return child;
        return const SizedBox(
          height: 100,
          width: 100,
          child: LoadingIndicator(),
        );
      },
      errorBuilder: (context, url, error) => const SizedBox(
        height: 100,
        width: 100,
        child: Center(
          child: Icon(Icons.error),
        ),
      ),
    );
  }
}
