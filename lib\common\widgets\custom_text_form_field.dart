import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:si/constants/style_manager.dart';

import '../../constants/app_sizes.dart';
import '../../utils/app_colors.dart';


class CustomTextFormField extends StatelessWidget {
  const CustomTextFormField({
    Key? key,
    this.validator,
    this.controller,
    this.hintText,
    this.enabled = true,
    required this.name,
    this.initialTextStyle,
    this.obscureText,
    this.suffixIcon,
    this.onChanged,
    this.onSaved,
    this.prefixIcon,
    this.hintStyle,
    this.keyboardType,
    this.labelText,
    this.labelStyle,
    this.contentPadding,
    this.initialValue,
    this.isDense,
    this.inputDecoration,
    this.isCollapsed,
    this.maxLength,
    this.textCapitalization,
    this.textInputAction = TextInputAction.next,
    this.enabledBorder,
    this.border,
    this.focusedBorder,
    this.fillColor,
    this.maxLines,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.filled,
    this.textInputFormatter,
    this.onSubmitted,
    this.onEditingComplete,
  }) : super(key: key);

  final FormFieldValidator<String>? validator;
  final TextEditingController? controller;
  final String? hintText;
  final String? labelText;
  final String? initialValue;
  final bool? obscureText;
  final bool? isCollapsed;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final ValueChanged<String?>? onChanged;
  final VoidCallback? onEditingComplete;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;
  final EdgeInsetsGeometry? contentPadding;
  final String name;
  final TextInputType? keyboardType;
  final TextCapitalization? textCapitalization;
  final TextStyle? initialTextStyle;
  final bool? isDense;
  final bool enabled;
  final TextInputAction? textInputAction;
  final int? maxLength;
  final int? maxLines;
  final InputDecoration? inputDecoration;
  final InputBorder? enabledBorder;
  final InputBorder? focusedBorder;
  final InputBorder? border;
  final Color? fillColor;
  final bool? filled;

  final AutovalidateMode? autovalidateMode;
  final List<TextInputFormatter>? textInputFormatter;
  final ValueChanged<String?>? onSubmitted;
  final FormFieldSetter<String>? onSaved;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: enabled,
      inputFormatters: textInputFormatter ?? [],
      cursorColor: AppColors.themeColor,
      enableInteractiveSelection: true,
      maxLines: maxLines ?? 1,
      autovalidateMode: autovalidateMode!,
      textInputAction: textInputAction,
      key: Key(name),
      style: initialTextStyle,
      onFieldSubmitted: onSubmitted,
      textCapitalization: textCapitalization ?? TextCapitalization.none,
      keyboardType: keyboardType,
      obscureText: obscureText ?? false,
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      onSaved: onSaved,
      onEditingComplete: onEditingComplete,
      initialValue: initialValue,
      maxLength: maxLength,
      decoration: inputDecoration ??
          InputDecoration(
            fillColor: Colors.white,
            filled: true,
            enabled: enabled,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            hintText: hintText ?? name,
            hintStyle: hintStyle ?? context.hintText,
            labelStyle: labelStyle ?? context.textformText,
            errorStyle: context.errorText,
            contentPadding: contentPadding ??
                const EdgeInsets.only(top: 18.0, bottom: 18, left: 18),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Sizes.p8),
              borderSide: const BorderSide(
                color: Colors.red,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Sizes.p8),
              borderSide: const BorderSide(
                color: Colors.red,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Sizes.p8),
              borderSide: BorderSide(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Sizes.p8),
              borderSide: BorderSide(
                color: Colors.black.withOpacity(0.3),
              ),
            ),

            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Sizes.p8),
              borderSide: BorderSide(
                color: AppColors.themeColor,
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Sizes.p8),
              borderSide: BorderSide(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            errorMaxLines: 3,
            // enabled: !state.isLoading,
          ),
    );
  }
}

InputDecoration defaultInputDecoration(BuildContext context) => InputDecoration(
      fillColor: Colors.white,
      filled: true,
      errorStyle: context.errorText,
      contentPadding: const EdgeInsets.only(top: 18.0, bottom: 18, left: 18),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(Sizes.p8),
        borderSide: const BorderSide(
          color: Colors.red,
        ),
      ),

      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(Sizes.p8),
        borderSide: BorderSide(
          color: Colors.black.withOpacity(0.3),
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(Sizes.p8),
        borderSide: BorderSide(
          color: Colors.black.withOpacity(0.3),
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(Sizes.p8),
        borderSide: const BorderSide(
          color: Colors.red,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(Sizes.p8),
        borderSide: BorderSide(
          color: AppColors.themeColor,
        ),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(Sizes.p8),
        borderSide: BorderSide(
          color: Colors.black.withOpacity(0.3),
        ),
      ),
      // enabled: !state.isLoading,
    );
