// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'motor_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MotorModel _$MotorModelFromJson(Map<String, dynamic> json) {
  return _MotorModel.fromJson(json);
}

/// @nodoc
mixin _$MotorModel {
  double? get motorAmps => throw _privateConstructorUsedError;
  int get device_status => throw _privateConstructorUsedError;
  int get mobile_status => throw _privateConstructorUsedError;
  int get output_status => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;
  int get VoltageFaultStatus => throw _privateConstructorUsedError;
  double? get voltage => throw _privateConstructorUsedError;
  double? get freq => throw _privateConstructorUsedError;

  /// Serializes this MotorModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MotorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MotorModelCopyWith<MotorModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MotorModelCopyWith<$Res> {
  factory $MotorModelCopyWith(
          MotorModel value, $Res Function(MotorModel) then) =
      _$MotorModelCopyWithImpl<$Res, MotorModel>;
  @useResult
  $Res call(
      {double? motorAmps,
      int device_status,
      int mobile_status,
      int output_status,
      int time,
      int VoltageFaultStatus,
      double? voltage,
      double? freq});
}

/// @nodoc
class _$MotorModelCopyWithImpl<$Res, $Val extends MotorModel>
    implements $MotorModelCopyWith<$Res> {
  _$MotorModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MotorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? motorAmps = freezed,
    Object? device_status = null,
    Object? mobile_status = null,
    Object? output_status = null,
    Object? time = null,
    Object? VoltageFaultStatus = null,
    Object? voltage = freezed,
    Object? freq = freezed,
  }) {
    return _then(_value.copyWith(
      motorAmps: freezed == motorAmps
          ? _value.motorAmps
          : motorAmps // ignore: cast_nullable_to_non_nullable
              as double?,
      device_status: null == device_status
          ? _value.device_status
          : device_status // ignore: cast_nullable_to_non_nullable
              as int,
      mobile_status: null == mobile_status
          ? _value.mobile_status
          : mobile_status // ignore: cast_nullable_to_non_nullable
              as int,
      output_status: null == output_status
          ? _value.output_status
          : output_status // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      VoltageFaultStatus: null == VoltageFaultStatus
          ? _value.VoltageFaultStatus
          : VoltageFaultStatus // ignore: cast_nullable_to_non_nullable
              as int,
      voltage: freezed == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as double?,
      freq: freezed == freq
          ? _value.freq
          : freq // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MotorModelImplCopyWith<$Res>
    implements $MotorModelCopyWith<$Res> {
  factory _$$MotorModelImplCopyWith(
          _$MotorModelImpl value, $Res Function(_$MotorModelImpl) then) =
      __$$MotorModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? motorAmps,
      int device_status,
      int mobile_status,
      int output_status,
      int time,
      int VoltageFaultStatus,
      double? voltage,
      double? freq});
}

/// @nodoc
class __$$MotorModelImplCopyWithImpl<$Res>
    extends _$MotorModelCopyWithImpl<$Res, _$MotorModelImpl>
    implements _$$MotorModelImplCopyWith<$Res> {
  __$$MotorModelImplCopyWithImpl(
      _$MotorModelImpl _value, $Res Function(_$MotorModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of MotorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? motorAmps = freezed,
    Object? device_status = null,
    Object? mobile_status = null,
    Object? output_status = null,
    Object? time = null,
    Object? VoltageFaultStatus = null,
    Object? voltage = freezed,
    Object? freq = freezed,
  }) {
    return _then(_$MotorModelImpl(
      motorAmps: freezed == motorAmps
          ? _value.motorAmps
          : motorAmps // ignore: cast_nullable_to_non_nullable
              as double?,
      device_status: null == device_status
          ? _value.device_status
          : device_status // ignore: cast_nullable_to_non_nullable
              as int,
      mobile_status: null == mobile_status
          ? _value.mobile_status
          : mobile_status // ignore: cast_nullable_to_non_nullable
              as int,
      output_status: null == output_status
          ? _value.output_status
          : output_status // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      VoltageFaultStatus: null == VoltageFaultStatus
          ? _value.VoltageFaultStatus
          : VoltageFaultStatus // ignore: cast_nullable_to_non_nullable
              as int,
      voltage: freezed == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as double?,
      freq: freezed == freq
          ? _value.freq
          : freq // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MotorModelImpl implements _MotorModel {
  const _$MotorModelImpl(
      {this.motorAmps = 0.0,
      this.device_status = 0,
      this.mobile_status = 0,
      this.output_status = 0,
      this.time = 0,
      this.VoltageFaultStatus = 2,
      this.voltage = 0.0,
      this.freq = 0.0});

  factory _$MotorModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MotorModelImplFromJson(json);

  @override
  @JsonKey()
  final double? motorAmps;
  @override
  @JsonKey()
  final int device_status;
  @override
  @JsonKey()
  final int mobile_status;
  @override
  @JsonKey()
  final int output_status;
  @override
  @JsonKey()
  final int time;
  @override
  @JsonKey()
  final int VoltageFaultStatus;
  @override
  @JsonKey()
  final double? voltage;
  @override
  @JsonKey()
  final double? freq;

  @override
  String toString() {
    return 'MotorModel(motorAmps: $motorAmps, device_status: $device_status, mobile_status: $mobile_status, output_status: $output_status, time: $time, VoltageFaultStatus: $VoltageFaultStatus, voltage: $voltage, freq: $freq)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MotorModelImpl &&
            (identical(other.motorAmps, motorAmps) ||
                other.motorAmps == motorAmps) &&
            (identical(other.device_status, device_status) ||
                other.device_status == device_status) &&
            (identical(other.mobile_status, mobile_status) ||
                other.mobile_status == mobile_status) &&
            (identical(other.output_status, output_status) ||
                other.output_status == output_status) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.VoltageFaultStatus, VoltageFaultStatus) ||
                other.VoltageFaultStatus == VoltageFaultStatus) &&
            (identical(other.voltage, voltage) || other.voltage == voltage) &&
            (identical(other.freq, freq) || other.freq == freq));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, motorAmps, device_status,
      mobile_status, output_status, time, VoltageFaultStatus, voltage, freq);

  /// Create a copy of MotorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MotorModelImplCopyWith<_$MotorModelImpl> get copyWith =>
      __$$MotorModelImplCopyWithImpl<_$MotorModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MotorModelImplToJson(
      this,
    );
  }
}

abstract class _MotorModel implements MotorModel {
  const factory _MotorModel(
      {final double? motorAmps,
      final int device_status,
      final int mobile_status,
      final int output_status,
      final int time,
      final int VoltageFaultStatus,
      final double? voltage,
      final double? freq}) = _$MotorModelImpl;

  factory _MotorModel.fromJson(Map<String, dynamic> json) =
      _$MotorModelImpl.fromJson;

  @override
  double? get motorAmps;
  @override
  int get device_status;
  @override
  int get mobile_status;
  @override
  int get output_status;
  @override
  int get time;
  @override
  int get VoltageFaultStatus;
  @override
  double? get voltage;
  @override
  double? get freq;

  /// Create a copy of MotorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MotorModelImplCopyWith<_$MotorModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SensorModel _$SensorModelFromJson(Map<String, dynamic> json) {
  return _SensorModel.fromJson(json);
}

/// @nodoc
mixin _$SensorModel {
  double? get cl => throw _privateConstructorUsedError;
  double? get tur => throw _privateConstructorUsedError;
  double? get ph => throw _privateConstructorUsedError;
  double? get flow => throw _privateConstructorUsedError;
  double? get cum_flow => throw _privateConstructorUsedError;
  int? get time => throw _privateConstructorUsedError;

  /// Serializes this SensorModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SensorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SensorModelCopyWith<SensorModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SensorModelCopyWith<$Res> {
  factory $SensorModelCopyWith(
          SensorModel value, $Res Function(SensorModel) then) =
      _$SensorModelCopyWithImpl<$Res, SensorModel>;
  @useResult
  $Res call(
      {double? cl,
      double? tur,
      double? ph,
      double? flow,
      double? cum_flow,
      int? time});
}

/// @nodoc
class _$SensorModelCopyWithImpl<$Res, $Val extends SensorModel>
    implements $SensorModelCopyWith<$Res> {
  _$SensorModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SensorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cl = freezed,
    Object? tur = freezed,
    Object? ph = freezed,
    Object? flow = freezed,
    Object? cum_flow = freezed,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      cl: freezed == cl
          ? _value.cl
          : cl // ignore: cast_nullable_to_non_nullable
              as double?,
      tur: freezed == tur
          ? _value.tur
          : tur // ignore: cast_nullable_to_non_nullable
              as double?,
      ph: freezed == ph
          ? _value.ph
          : ph // ignore: cast_nullable_to_non_nullable
              as double?,
      flow: freezed == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as double?,
      cum_flow: freezed == cum_flow
          ? _value.cum_flow
          : cum_flow // ignore: cast_nullable_to_non_nullable
              as double?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SensorModelImplCopyWith<$Res>
    implements $SensorModelCopyWith<$Res> {
  factory _$$SensorModelImplCopyWith(
          _$SensorModelImpl value, $Res Function(_$SensorModelImpl) then) =
      __$$SensorModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? cl,
      double? tur,
      double? ph,
      double? flow,
      double? cum_flow,
      int? time});
}

/// @nodoc
class __$$SensorModelImplCopyWithImpl<$Res>
    extends _$SensorModelCopyWithImpl<$Res, _$SensorModelImpl>
    implements _$$SensorModelImplCopyWith<$Res> {
  __$$SensorModelImplCopyWithImpl(
      _$SensorModelImpl _value, $Res Function(_$SensorModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SensorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cl = freezed,
    Object? tur = freezed,
    Object? ph = freezed,
    Object? flow = freezed,
    Object? cum_flow = freezed,
    Object? time = freezed,
  }) {
    return _then(_$SensorModelImpl(
      cl: freezed == cl
          ? _value.cl
          : cl // ignore: cast_nullable_to_non_nullable
              as double?,
      tur: freezed == tur
          ? _value.tur
          : tur // ignore: cast_nullable_to_non_nullable
              as double?,
      ph: freezed == ph
          ? _value.ph
          : ph // ignore: cast_nullable_to_non_nullable
              as double?,
      flow: freezed == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as double?,
      cum_flow: freezed == cum_flow
          ? _value.cum_flow
          : cum_flow // ignore: cast_nullable_to_non_nullable
              as double?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SensorModelImpl implements _SensorModel {
  const _$SensorModelImpl(
      {this.cl = 0.0,
      this.tur = 0.0,
      this.ph = 0.0,
      this.flow = 0.0,
      this.cum_flow = 0.0,
      this.time = 0});

  factory _$SensorModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SensorModelImplFromJson(json);

  @override
  @JsonKey()
  final double? cl;
  @override
  @JsonKey()
  final double? tur;
  @override
  @JsonKey()
  final double? ph;
  @override
  @JsonKey()
  final double? flow;
  @override
  @JsonKey()
  final double? cum_flow;
  @override
  @JsonKey()
  final int? time;

  @override
  String toString() {
    return 'SensorModel(cl: $cl, tur: $tur, ph: $ph, flow: $flow, cum_flow: $cum_flow, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SensorModelImpl &&
            (identical(other.cl, cl) || other.cl == cl) &&
            (identical(other.tur, tur) || other.tur == tur) &&
            (identical(other.ph, ph) || other.ph == ph) &&
            (identical(other.flow, flow) || other.flow == flow) &&
            (identical(other.cum_flow, cum_flow) ||
                other.cum_flow == cum_flow) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, cl, tur, ph, flow, cum_flow, time);

  /// Create a copy of SensorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SensorModelImplCopyWith<_$SensorModelImpl> get copyWith =>
      __$$SensorModelImplCopyWithImpl<_$SensorModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SensorModelImplToJson(
      this,
    );
  }
}

abstract class _SensorModel implements SensorModel {
  const factory _SensorModel(
      {final double? cl,
      final double? tur,
      final double? ph,
      final double? flow,
      final double? cum_flow,
      final int? time}) = _$SensorModelImpl;

  factory _SensorModel.fromJson(Map<String, dynamic> json) =
      _$SensorModelImpl.fromJson;

  @override
  double? get cl;
  @override
  double? get tur;
  @override
  double? get ph;
  @override
  double? get flow;
  @override
  double? get cum_flow;
  @override
  int? get time;

  /// Create a copy of SensorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SensorModelImplCopyWith<_$SensorModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
