

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';

class FlowMeterWidget extends HookConsumerWidget{

  const FlowMeterWidget({required this.sensorModel});

  final SensorModel  sensorModel;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return  Stack(
      children: [
        Image.asset(
          "assets/icons/flow_meter.png",
          height: 120,
          width: 80,
          fit: BoxFit.cover,
        ),
        Positioned(
          top: 0,
          left:5,
          right:5,
          child: Center(
            child: Container(
              height: 40,
              width: 90,
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
                border: Border.all(color: Colors.black, width: 2),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text("${(sensorModel.flow??0.0).toStringAsFixed(2)} m3/hr", style: const TextStyle(color: Colors.white, fontSize: 10)),
                    Text("${(sensorModel.cum_flow??0.0).toStringAsFixed(2)} m3/hr", style: const TextStyle(color: Colors.white, fontSize: 10)),
                  ],
                ),
              ),
            )
          ),
        )
      ],
    );
  }

}


class MobileFlowMeterWidget extends HookConsumerWidget{

  const MobileFlowMeterWidget({required this.sensorId});

  final String sensorId;


  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final sensorState = useState<SensorModel>(SensorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(sensorProvider(sensorId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            sensorState.value = SensorModel(
              tur: datasnapshot['tur'],
              time: datasnapshot['time'],
              flow: datasnapshot['flow']!=null ? datasnapshot["flow"] : 0.0,
              cum_flow: datasnapshot['cum_flow']!=null ? datasnapshot["cum_flow"] : 0.0,);
          }
        });
    return  Container(
        width: 90,
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(10),
        ),
        child:  Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
              children: [

                Text((sensorState.value.flow?.toStringAsFixed(2) ?? '') + " m3/hr", style: TextStyle(color: Colors.white, fontSize: 10),),
                Text((sensorState.value.cum_flow?.toStringAsFixed(2) ?? '') +' m3/hr', style: TextStyle(color: Colors.white, fontSize: 10),),
              ]

          ),
        )
    );
  }

}