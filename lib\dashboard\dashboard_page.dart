


import 'package:adaptive_navigation/adaptive_navigation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/provider/dashboard_provider.dart';

import '../kawosoti/schema/model/user_model.dart';
import 'widgets/animated_branch_container.dart';



/// The enum for scaffold tab.
enum ScaffoldTab {
  /// The books tab.
  siteOne,

  /// The authors tab.
  siteTwo,

  /// The settings tab.
  settings
}

class DashboardPage extends HookConsumerWidget {
  /// Creates a [BookstoreScaffold].
  const DashboardPage({
    required this.navigationShell,
    required this.children,
    Key? key,
  }) : super(key: key ?? const ValueKey<String>('ScaffoldWithNavBar'));

  /// The navigation shell and container for the branch Navigators.
  final StatefulNavigationShell navigationShell;

  /// The children (branch Navigators) to display in a custom container
  /// ([AnimatedBranchContainer]).
  final List<Widget> children;

  @override
  Widget build(BuildContext context,WidgetRef ref) {
    final user = ref.watch(userProvider);
    ref.listen(userProvider, (previous, next) {
      ref.read(userState.notifier).state = next.asData?.value ?? UserModel();
    });
    return Scaffold(
      appBar: AppBar(title: Text("APPBAR"),),
      body: AnimatedBranchContainer(
        currentIndex: navigationShell.currentIndex,
        children: children,
      ),
      bottomNavigationBar: BottomNavigationBar(
        // Here, the items of BottomNavigationBar are hard coded. In a real
        // world scenario, the items would most likely be generated from the
        // branches of the shell route, which can be fetched using
        // `navigationShell.route.branches`.
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
              icon: Icon(Icons.propane_tank_outlined), label: 'Site One'),
          BottomNavigationBarItem(
              icon: Icon(Icons.propane_tank_outlined), label: 'Site Two'),
          BottomNavigationBarItem(
              icon: Icon(Icons.settings_ethernet_outlined), label: 'More'),
        ],
        currentIndex: navigationShell.currentIndex,
        onTap: (int index) => _onTap(context, index),
      ),
    );
  }

  /// Navigate to the current location of the branch at the provided index when
  /// tapping an item in the BottomNavigationBar.
  void _onTap(BuildContext context, int index) {
    // When navigating to a new branch, it's recommended to use the goBranch
    // method, as doing so makes sure the last navigation state of the
    // Navigator for the branch is restored.
    navigationShell.goBranch(
      index,
      // A common pattern when using bottom navigation bars is to support
      // navigating to the initial location when tapping the item that is
      // already active. This example demonstrates how to support this behavior,
      // using the initialLocation parameter of goBranch.
      initialLocation: index == navigationShell.currentIndex,
    );
  }
}

/// The scaffold for the book store.
/*class DashboardPage extends StatelessWidget {
  /// Creates a [BookstoreScaffold].
  const DashboardPage({
    required this.selectedTab,
    required this.child,
    super.key,
  });

  /// Which tab of the scaffold to display.
  final ScaffoldTab selectedTab;

  /// The scaffold body.
  final Widget child;

  @override
  Widget build(BuildContext context) => Scaffold(
    body: AdaptiveNavigationScaffold(
      selectedIndex: selectedTab.index,
      body: child,
      onDestinationSelected: (int idx) {
        switch (ScaffoldTab.values[idx]) {
          case ScaffoldTab.siteOne:
            context.go('/siteOne');
            break;
          case ScaffoldTab.siteTwo:
            print('siteTwo');
            context.go('/siteTwo');
            break;
          case ScaffoldTab.settings:
            print('settings');
            context.go('/settings');
            break;
        }
      },
      destinations: const <AdaptiveScaffoldDestination>[
        AdaptiveScaffoldDestination(
          title: 'Site One',
          icon: Icons.propane_tank_outlined,
        ),
        AdaptiveScaffoldDestination(
          title: 'Site Two',
          icon: Icons.propane_tank_outlined,
        ),
        AdaptiveScaffoldDestination(
          title: 'Settings',
          icon: Icons.settings,
        ),
      ],
    ),
  );
}*/
