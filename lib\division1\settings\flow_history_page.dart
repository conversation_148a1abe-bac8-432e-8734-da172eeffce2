import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/levelHistory/flow_history_data_source.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../datatable/data_table_2.dart';
import '../../datatable/paginated_data_table_2.dart';
import '../../kawosoti/schema/widgets/date_forward_backward.dart';
import '../levelHistory/level_history_data_source.dart';

/// Route options are used to configure certain features of
/// the given example
String getCurrentRouteOption(BuildContext context) {
  var isEmpty = ModalRoute.of(context) != null &&
      ModalRoute.of(context)!.settings.arguments != null &&
      ModalRoute.of(context)!.settings.arguments is String
      ? ModalRoute.of(context)!.settings.arguments as String
      : '';

  return isEmpty;
}

// Route options
const dflt = 'Default';
const noData = 'No data';
const autoRows = 'Auto rows';
const showBordersWithZebraStripes = 'Borders with Zebra';
const custPager = 'Custom pager';
const defaultSorting = 'Default sorting';
const selectAllPage = 'Select all at page';
const rowTaps = 'Row Taps';
const rowHeightOverrides = 'Row height overrides';
const fixedColumnWidth = 'Fixed column width';

class FlowHistoryPage extends StatefulHookConsumerWidget {
  const FlowHistoryPage({this.siteId = "site1"});

  final String siteId;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _FLowHistoryPageState();
}

class _FLowHistoryPageState extends ConsumerState<FlowHistoryPage> {
  FlowHistorySiteDataSource? siteDataSource;
  final PaginatorController _controller = PaginatorController();
  int _rowsPerPage = PaginatedDataTable.defaultRowsPerPage;
  int tankId = 1;

  List<DataColumn> get _columns {
    return [
      DataColumn2(
        size: ColumnSize.L,
        label: const Text(
          'Date',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text(
          'Pump run hour',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text(
          'Water discharge',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),

    ];
  }

  @override
  void initState() {
    siteDataSource =
        FlowHistorySiteDataSource(userType: ref.read(userState).role ?? 1);
    siteDataSource?.SiteId = "input1"+ "_daily_history";
    siteDataSource?.tankId = 1;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    void handleClick(String value) {
      switch (value) {
        case 'Pump 1':
          setState(() {
            tankId =1;
            siteDataSource?.SiteId = "input1"+ "_daily_history";
            _controller.goToFirstPage();
          });
          break;
        case 'Pump 2':
          setState(() {
            tankId = 2;
            siteDataSource?.SiteId = "input2"+ "_daily_history";
            _controller.goToFirstPage();
          });
          break;
        case 'Output 1':
          setState(() {
            tankId =3;
            siteDataSource?.SiteId = "output1"+ "_daily_history";
            _controller.goToFirstPage();
          });
          break;
        case 'Output 2':
          setState(() {
            tankId = 4;
            siteDataSource?.SiteId = "output2"+ "_daily_history";
            _controller.goToFirstPage();
          });
          break;
        case 'Output 3':
          setState(() {
            tankId = 5;
            siteDataSource?.SiteId = "output3"+ "_daily_history";
            _controller.goToFirstPage();
          });
          break;

        case 'Output 4':
          setState(() {
            tankId = 6;
            siteDataSource?.SiteId = "output4"+ "_daily_history";
            _controller.goToFirstPage();
          });
          break;
      }
    }

    return BaseScaffold(
      showAppBar: false,
      appbar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: Colors.blueAccent,
        centerTitle: true,
        title: Text(
          tankId == 1 ? "Flow Rate (Pump 1)" : tankId==2 ? "Flow Rate (Pump 2)"
              : tankId == 3 ? "Flow Rate (Output 1)" : tankId == 4 ? "Flow Rate (Output 2)": tankId == 5 ? "Flow Rate (Output 3)" :"Flow Rate (Output 4)" ,
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: handleClick,
            itemBuilder: (BuildContext context) {
              return {'Pump 1', 'Pump 2'}.map((String choice) {
                return PopupMenuItem<String>(
                  value: choice,
                  child: Text(choice),
                );
              }).toList();

            },
          )
        ],
      ),
      child: Column(
        children: [
          MonthForwardBackward(onDateChange: (data) {
            log(data.toString());
            siteDataSource?.startDate = data;
            _controller.goToFirstPage();
          }),
          SizedBox(
            height: 20,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                AsyncPaginatedDataTable2(
                    showCheckboxColumn: false,
                    horizontalMargin: 16,
                    columnSpacing: 0,
                    wrapInCard: false,
                    rowsPerPage: 40,
                    minWidth: 300,
                    availableRowsPerPage: [40],
                    autoRowsToHeight:
                    getCurrentRouteOption(context) == autoRows,
                    // Default - do nothing, autoRows - goToLast, other - goToFirst
                    pageSyncApproach: getCurrentRouteOption(context) == dflt
                        ? PageSyncApproach.doNothing
                        : getCurrentRouteOption(context) == autoRows
                        ? PageSyncApproach.goToLast
                        : PageSyncApproach.goToFirst,
                    fit: FlexFit.tight,
                    initialFirstRowIndex: 0,
                    onRowsPerPageChanged: (value) {
                      // No need to wrap into setState, it will be called inside the widget
                      // and trigger rebuild
                      //setState(() {
                      print('Row per page changed to $value');
                      _rowsPerPage = value!;
                      //});
                    },
                    controller: _controller,
                    empty: Center(
                        child: Container(
                            padding: const EdgeInsets.all(20),
                            color: Colors.grey[200],
                            child: const Text(
                              'No data',
                              style:
                              TextStyle(fontSize: 18, color: Colors.white),
                            ))),
                    loading: Center(
                        child: CircularProgressIndicator(
                          color: Colors.blue,
                        )),
                    errorBuilder: (e) => Center(
                      child: SelectableText(e.toString()),
                    ),
                    columns: _columns,
                    source: siteDataSource!),
              ],
            ),
          ),

        ],
      ),
    );
  }
}
