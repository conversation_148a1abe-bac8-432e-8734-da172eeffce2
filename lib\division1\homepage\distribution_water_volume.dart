

import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../model/tank_model.dart';

class DistributionWaterVolumeWidget extends HookConsumerWidget {

  const DistributionWaterVolumeWidget( this.id);


  final String id;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final volume = useState<OutputInputVolumeModel>(const OutputInputVolumeModel());
    ref.listen<AsyncValue<DatabaseEvent>>(outputProvider(id), (previous, next)  async {
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final afterVolumeData = OutputInputVolumeModel.fromJson(aftercleanData);
        log(afterVolumeData.toString());
        volume.value = afterVolumeData;
      }
    });
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Row(
        children: [
          volume.value.status == 1 ?Container(
            width: 20,
            decoration: BoxDecoration(
              border: Border.symmetric(vertical: BorderSide(color: Colors.black))
            ),
            child: RotatedBox(
              quarterTurns: 3,
              child: LoadingAnimationWidget.prograssiveDots(
                color: Colors.lightBlueAccent,
                size: 40,
              ),
            ),
          ) : Container(
            width: 20,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey,
                border: Border.symmetric(vertical: BorderSide(color: Colors.black))
            ),
            child: Container(),
          ),
          gapW8,
          Text((volume.value.volume/1000).toStringAsFixed(2)+" kl", style: TextStyle(fontSize: 14,
          color: volume.value.status==1 ? Colors.green : Colors.black)),
        ],
      ),
    );
  }
}