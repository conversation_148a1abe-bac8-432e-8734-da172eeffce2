import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/app_icon_widget.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/dashboard/widgets/display_widget.dart';
import 'package:si/dashboard/widgets/motor_moving.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import 'widgets/site_page.dart';

class SiteOnePage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteOneProvider = ref.watch(site1Provider);
    final user = ref.watch(userState);

    return BaseScaffold(
      appbarText: "Site One",
      showLeftIcon: false,
      showAction: ref.read(userState).role == 3,
      onActionClick: (){
          context.push('/siteOne/settingOne');
      },
      child: siteOneProvider.when(
          data: (data){
            return SitePage(data!,"site1");
          },
          error: (err,stack){
            return Text(err.toString());
          },
          loading: ()=> const LoadingIndicator()),
    );
  }
}
