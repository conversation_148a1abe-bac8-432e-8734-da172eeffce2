
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/repository/auth_repository.dart';

import 'signup_state.dart';

class SignupController extends StateNotifier<SignupState> {
  SignupController({
    required this.signupRepository,
  }) : super(SignupState());

  final IAuthRepository signupRepository;

  Future<bool> submit({required String fullname, required String phonenumber,required String email,
    required String password, required String siteId}) async {
    state = state.copyWith(value: const AsyncValue.loading());
    final value = await AsyncValue.guard(() => _signup(email,password,phonenumber,fullname, siteId));
    state = state.copyWith(value: value);
    return value.hasError == false;
  }

  Future<bool?> _signup(String email, String password, String phonenumber, String fullname, String siteId) async {
    return await signupRepository.createUserWithEmail(email,password, fullname, phonenumber, siteId);
  }
}
