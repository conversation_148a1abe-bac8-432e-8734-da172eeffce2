
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/qr_scanner_page.dart';
import 'package:si/provider/auth_provider.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:si/utils/constants.dart';

import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../utils/string_validators.dart';


class FormSectionForPassword extends ConsumerStatefulWidget {
  const FormSectionForPassword({super.key});

  @override
  ConsumerState<FormSectionForPassword> createState() =>
      _FormSectionForPasswordState();
}

class _FormSectionForPasswordState extends ConsumerState<FormSectionForPassword> {
  final _passwordFormKey = GlobalKey<FormState>();
  final _node = FocusScopeNode();
  final _passwordController = TextEditingController();

  String get password => _passwordController.text;

  // local variable used to apply AutovalidateMode.onUserInteraction and show
  // error hints only when the form has been submitted
  // For more details on how this is implemented, see:
  // https://codewithandrea.com/articles/flutter-text-field-form-validation/
  var _submitted = false;
  @override
  void initState() {
    super.initState();
  }


  @override
  void dispose() {
    // * TextEditingControllers should be always disposed
    _node.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final role = ref.read(sharedPreferencesServiceProvider).getUserRole() ?? 1;
    final width = MediaQuery.of(context).size.width;
    return Padding(
      padding: const EdgeInsets.all(Sizes.p8),
      child: FocusScope(
        node: _node,
        child: Form(
          key: _passwordFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              gapH16,
              // Password field
              Consumer(
                builder: (BuildContext context, WidgetRef ref, Widget? child) {
                  return CustomTextFormField(
                    controller: _passwordController,
                    name: "password",
                    hintText: "Password",
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    obscureText: role>2 ? false :true,
                    autovalidateMode: AutovalidateMode.onUserInteraction,

                    inputDecoration: defaultInputDecoration(context).copyWith(
                      hintText: "Password",
                      hintStyle: context.hintText,
                      prefixIcon: const Icon(
                        FontAwesomeIcons.key,
                        color: Colors.grey,
                      ),
                      suffixIcon: Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: IconButton(
                          onPressed: () async  {
                           final siteId = await context.push<String>('/scanner');
                            _passwordController.text = siteId ?? '';
                          },
                          // onPressed: () {},
                          icon: Icon(
                            Icons.qr_code_scanner_outlined,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
              if(role>=4)
              _buildSiteList(context, ref),
              gapH24,
              PrimaryButton(
                height: Sizes.p60,
                text: "Continue",
                onPressed: () async {
                    final site = await ref.read(divisionRepositoryProvider).getSiteId(_passwordController.text.toString());
                    await ref.read(localStorageProvider).setSettings(site!);
                    ref.read(sharedPreferencesServiceProvider).setSiteId(site?.id ?? '');
                    ref.read(sharedPreferencesServiceProvider).setSite();
                    ref.read(sharedPreferencesServiceProvider).setTankNumber(site?.tank_number ?? 1);
                    ref.read(sharedPreferencesServiceProvider).setMotorNumber(site?.motor_number ?? 1);
                    ref.read(sharedPreferencesServiceProvider).setHasValve(site?.has_valve ?? false);
                    ref.read(authRepositoryProvider).updateUserSiteId(ref.read(authRepositoryProvider).getCurrentUser()?.uid ?? '',
                      ref.read(sharedPreferencesServiceProvider).getSiteId());
                    if(context.mounted){

                      context.go('/home');

                    }

                },
              ),
              gapH24,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSiteList(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.bottomRight,
            child: TextButton(
              child: Text(
                "Select sites",
                style: context.displayText.copyWith(
                  decoration: TextDecoration.underline,
                  fontSize: 16,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: () async {
                  final password = await context.push<String>("/selectSite",extra: SelectSiteFrom.fromPassword);
                  _passwordController.text = password ?? '';
              },
            ),
          ),
        ],
      ),
    );
  }

}
