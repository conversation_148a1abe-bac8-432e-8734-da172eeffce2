

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';

import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../new_tank_unit.dart';
import '../../provider/division_provider.dart';
import 'package:firebase_database/firebase_database.dart';


class PandubazarSiteOnePage extends HookConsumerWidget{


  const PandubazarSiteOnePage(this.setting);

  final DivisionSettingModel setting;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor10State = useState<MotorModel>(const MotorModel());
    final motor11State = useState<MotorModel>(const MotorModel());
    final motor12State = useState<MotorModel>(const MotorModel());
    final motor20State = useState<MotorModel>(const MotorModel());
    final motor21State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor12State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor12State.value.device_status == motor12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status == motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("21"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor21State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor21State.value.device_status == motor21State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
              height: 16,
          ),
        ),
        SliverToBoxAdapter(
          child: SingleChildScrollView(
            scrollDirection : Axis.horizontal,
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '10',motorModel: motor10State.value,motorSettingModel: setting.motor10,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                          color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '11',motorModel: motor11State.value,motorSettingModel: setting.motor11,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '12',motorModel: motor12State.value,motorSettingModel: setting.motor12,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '20',motorModel: motor20State.value,motorSettingModel: setting.motor20,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '21',motorModel: motor21State.value,motorSettingModel: setting.motor21,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            color: Colors.blueAccent,
              height: 20,
            width: double.infinity,
          ),
        ),
        SliverToBoxAdapter(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(3, "10", setting.tank10!, 170, 110,sensorId: "10", hasTurbidity: true,setting: setting,),
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  )
                ],
              ),
              Container(
                color: Colors.blueAccent,
                height: 30,
                width: 20,
              ),
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(3, "11", setting.tank11!, 170, 110,sensorId: "11",
                  hasPh: true,hasChlorine: true,hasTurbidity: true, setting: setting,),
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  )
                ],
              ),
              Container(
                height: 30,
                width: 20,
              ),
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(3, "20", setting.tank20!, 170, 110, sensorId: "20",
                    setting: setting,
                    hasTurbidity: true,),
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  )
                ],
              ),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            height: 60,
            color: Colors.blueAccent,
            child: const Center(
              child: Text(
                "Distribution",
                style: TextStyle(color: Colors.white,fontSize: 18),
              ),
            ),
          ),
        )
      ],
    );
  }

}