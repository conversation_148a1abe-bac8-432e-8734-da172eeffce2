
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/model/dashboard_model.dart';

import '../common/widgets/base_scaffold.dart';
import '../common/widgets/widgets.dart';
import '../provider/dashboard_provider.dart';
import 'widgets/site_page.dart';

class SiteTwoPage extends HookConsumerWidget{

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteOneProvider = ref.watch(site2Provider);
    final user = ref.watch(userState);

    return BaseScaffold(
      appbarText: "Site Two",
      showLeftIcon: false,
      showAction: ref.read(userState).role == 3,
      onActionClick: (){
        context.push('/siteTwo/settingTwo');
      },
      child: siteOneProvider.when(
          data: (data){
            return SitePage(data!, "site2");
          },
          error: (err,stack){
            return Text("Error");
          },
          loading: ()=> const LoadingIndicator()),
    );
  }

}