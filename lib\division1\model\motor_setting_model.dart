


import '../../utils/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'motor_setting_model.freezed.dart';
part 'motor_setting_model.g.dart';

@freezed
class MotorSettingModel with _$MotorSettingModel {
  const factory MotorSettingModel({
    @Default('') String name,
    @Default('') String hrs,
    @Default('') String mins,
    @Default('') String power,
    @Default(-39) int CF,
    @Default(false) bool has_voltage,
    @Default(0.0) double TH_Amps,
    @Default(0.0) double  max_current,
    @Default(0.0) double  min_current,
    @Default(true) bool is_active,
    @Default(15) int upload_time,
    @Default(1) int mode,
    @Default(0.0) double current_threshold,
  }) = _MotorSettingModel;

  factory MotorSettingModel.fromJson(Map<String, dynamic> json) => _$MotorSettingModelFromJson(json);
}



@freezed
class ValveSettingModel with _$ValveSettingModel {
  const factory ValveSettingModel({
    @Default('') String name,
    @Default(true) bool is_active,
    @Default(15) int upload_time,
    @Default(1) int mode,
    @Default("") String hrs,
    @Default("") String mins,
    @Default("") String days,
  }) = _ValveSettingModel;

  factory ValveSettingModel.fromJson(Map<String, dynamic> json) => _$ValveSettingModelFromJson(json);
}


@freezed
class SensorSettingModel with _$SensorSettingModel {

  const factory SensorSettingModel({
    @Default(true) bool is_active,
    @Default(0.0) double tos,
    @Default(0.0) double clos,
    @Default(0.0) double phos,
    @Default(1) int tscale,
    @Default(1) int clscale,
    @Default(1) int phscale,
  }) = _SensorSettingModel;

  factory SensorSettingModel.fromJson(Map<String, dynamic> json) => _$SensorSettingModelFromJson(json);

}


@freezed
class ScheduleModel with _$ScheduleModel{
  const factory ScheduleModel({
    @Default("") String hrs,
    @Default("") String mins,


  }) = _ScheduleModel;

  factory ScheduleModel.fromJson(Map<String, dynamic> json) => _$ScheduleModelFromJson(json);
}