import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:si/dashboard/dashboard_page.dart';
import 'package:si/dashboard/setting_page.dart';
import 'package:si/dashboard/site_one_page.dart';
import 'package:si/history/ui/all_history_page.dart';
import 'package:si/history/ui/float_history_page.dart';
import 'package:si/history/ui/motor_history_page.dart';
import 'package:si/history/ui/tank_history_page.dart';
import 'package:si/history/ui/timer_history_page.dart';
import 'package:si/signup/ui/signup_page.dart';

import '../dashboard/more_page.dart';
import '../dashboard/site_two_page.dart';
import '../login/ui/login_page.dart';
import '../main.dart';
import '../provider/auth_provider.dart';
import 'go_router_refresh_stream.dart';
import 'not_found_screen.dart';

enum AppRoute { login, signUp, forgetPassword, dashboard }

final ValueKey<String> _scaffoldKey = const ValueKey<String>('App scaffold');
final GlobalKey<NavigatorState> _rootNavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'root');
final GlobalKey<NavigatorState> _tabANavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'tabANav');

final go1RouterProvider = Provider<GoRouter>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      redirect: (context, state) {
        final isLoggedIn = authRepository.isLogin();
        if (isLoggedIn) {
          return state.matchedLocation;
        } else {
          if (!isLoggedIn && state.matchedLocation == '/login') {
            return '/login';
          } else if (!isLoggedIn && state.matchedLocation == '/signup') {
            return '/signup';
          } else {
            return "/login";
          }
        }
      },
      routes: <RouteBase>[
        GoRoute(
          path: '/',
          redirect: (_, __) => '/siteOne',
        ),
        GoRoute(
          path: '/login',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              FadeTransitionPage(
            key: state.pageKey,
            child: LoginPage(),
          ),
        ),
        GoRoute(
          path: '/signup',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              FadeTransitionPage(
            key: state.pageKey,
            child: SignUpPage(),
          ),
        ),
        StatefulShellRoute(
          builder: (context, state, navigationShell) {
            return navigationShell;
          },
          navigatorContainerBuilder: (context, navigationShell, children) {
            return DashboardPage(
                navigationShell: navigationShell, children: children);
          },
          branches: <StatefulShellBranch>[
            StatefulShellBranch(
                routes: <RouteBase>[
                    GoRoute(path: "/siteOne",
                    builder: (context,state) => SiteOnePage(),
                      routes: <RouteBase>[
                        GoRoute(
                          path: 'settingOne',
                          pageBuilder: (BuildContext context, GoRouterState state) =>
                              FadeTransitionPage(
                            key: state.pageKey,
                            child: SettingPage(site:"site1"),
                          ),
                        ),
                        GoRoute(
                          path: 'tankHistory',
                          pageBuilder: (BuildContext context, GoRouterState state) =>
                              FadeTransitionPage(
                                key: state.pageKey,
                                child: TankHistoryPage(site:"site1"),
                              ),
                        ),
                        GoRoute(
                          path: 'floatHistory',
                          pageBuilder: (BuildContext context, GoRouterState state) =>
                              FadeTransitionPage(
                                key: state.pageKey,
                                child: FloatHistoryPage(site:"site1"),
                              ),
                        ),
                        GoRoute(
                          path: 'timerHistory',
                          pageBuilder: (BuildContext context, GoRouterState state) =>
                              FadeTransitionPage(
                                key: state.pageKey,
                                child: TimerHistoryPage(site:"site1"),
                              ),
                        ),
                        GoRoute(
                          path: 'motorHistory',
                          pageBuilder: (BuildContext context, GoRouterState state) =>
                              FadeTransitionPage(
                                key: state.pageKey,
                                child: MotorHistoryPage(site:"site1"),
                              ),
                        ),
                        GoRoute(
                          path: 'history',
                          pageBuilder: (BuildContext context, GoRouterState state) =>
                              FadeTransitionPage(
                                key: state.pageKey,
                                child: AllHistoryPage(site:"site1"),
                              ),
                        ),
                      ]
                    ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/siteTwo",
                    builder: (context,state) => SiteTwoPage(),
                    routes: <RouteBase>[
                      GoRoute(
                        path: 'settingTwo',
                        pageBuilder: (BuildContext context, GoRouterState state) =>
                            FadeTransitionPage(
                          key: state.pageKey,
                          child: SettingPage(site:"site2"),
                        ),
                      ),
                      GoRoute(
                        path: 'tankHistory',
                        pageBuilder: (BuildContext context, GoRouterState state) =>
                            FadeTransitionPage(
                              key: state.pageKey,
                              child: TankHistoryPage(site:"site2"),
                            ),
                      ),
                      GoRoute(
                        path: 'floatHistory',
                        pageBuilder: (BuildContext context, GoRouterState state) =>
                            FadeTransitionPage(
                              key: state.pageKey,
                              child: FloatHistoryPage(site:"site2"),
                            ),
                      ),
                      GoRoute(
                        path: 'timerHistory',
                        pageBuilder: (BuildContext context, GoRouterState state) =>
                            FadeTransitionPage(
                              key: state.pageKey,
                              child: TimerHistoryPage(site:"site2"),
                            ),
                      ),
                      GoRoute(
                        path: 'motorHistory',
                        pageBuilder: (BuildContext context, GoRouterState state) =>
                            FadeTransitionPage(
                              key: state.pageKey,
                              child: MotorHistoryPage(site:"site2"),
                            ),
                      ),
                      GoRoute(
                        path: 'history',
                        pageBuilder: (BuildContext context, GoRouterState state) =>
                            FadeTransitionPage(
                              key: state.pageKey,
                              child: AllHistoryPage(site:"site2"),
                            ),
                      ),
                    ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/more",
                    builder: (context,state) => MorePage(),
                  ),
                ]),
          ],

        )
      ]);
});

/*final goRouterProvider = Provider<GoRouter>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: false,
    redirect: (context, state) {
      final isLoggedIn = authRepository.isLogin();
      if (isLoggedIn) {
        return state.matchedLocation;
      } else {
        if (!isLoggedIn && state.matchedLocation == '/login') {
          return '/login';
        } else if (!isLoggedIn && state.matchedLocation == '/signup') {
          return '/signup';
        } else {
          return "/login";
        }
      }
    },
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        redirect: (_, __) => '/siteOne',
      ),
      GoRoute(
        path: '/login',
        pageBuilder: (BuildContext context, GoRouterState state) =>
            FadeTransitionPage(
          key: state.pageKey,
          child: LoginPage(),
        ),
      ),
      GoRoute(
        path: '/signup',
        pageBuilder: (BuildContext context, GoRouterState state) =>
            FadeTransitionPage(
          key: state.pageKey,
          child: SignUpPage(),
        ),
      ),
      GoRoute(
        path: '/settings',
        pageBuilder: (BuildContext context, GoRouterState state) =>
            FadeTransitionPage(
          key: _scaffoldKey,
          child: DashboardPage(
            selectedTab: ScaffoldTab.settings,
            child: SettingPage(),
          ),
        ),
      ),
      GoRoute(
        path: '/siteOne',
        pageBuilder: (BuildContext context, GoRouterState state) =>
            FadeTransitionPage(
          key: _scaffoldKey,
          child: DashboardPage(
            selectedTab: ScaffoldTab.siteOne,
            child: SiteOnePage(),
          ),
        ),
      ),
      GoRoute(
        path: '/siteTwo',
        pageBuilder: (BuildContext context, GoRouterState state) =>
            FadeTransitionPage(
          key: _scaffoldKey,
          child: DashboardPage(
            selectedTab: ScaffoldTab.siteTwo,
            child: SiteTwoPage(),
          ),
        ),
      ),
      *//*  GoRoute(
        path: '/',
        name: AppRoute.dashboard.name,
        builder: (context, state) => const ProductsListScreen(),
        routes: [
          GoRoute(
            path: 'product/:id',
            name: AppRoute.product.name,
            builder: (context, state) {
              final productId = state.pathParameters['id']!;
              return ProductScreen(productId: productId);
            },
            routes: [
              GoRoute(
                path: 'review',
                name: AppRoute.leaveReview.name,
                pageBuilder: (context, state) {
                  final productId = state.pathParameters['id']!;
                  return MaterialPage(
                    fullscreenDialog: true,
                    child: LeaveReviewScreen(productId: productId),
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: 'cart',
            name: AppRoute.cart.name,
            pageBuilder: (context, state) => const MaterialPage(
              fullscreenDialog: true,
              child: ShoppingCartScreen(),
            ),
            routes: [
              GoRoute(
                path: 'checkout',
                name: AppRoute.checkout.name,
                pageBuilder: (context, state) => const MaterialPage(
                  fullscreenDialog: true,
                  child: CheckoutScreen(),
                ),
              ),
            ],
          ),
          GoRoute(
            path: 'orders',
            name: AppRoute.orders.name,
            pageBuilder: (context, state) => const MaterialPage(
              fullscreenDialog: true,
              child: OrdersListScreen(),
            ),
          ),
          GoRoute(
            path: 'account',
            name: AppRoute.account.name,
            pageBuilder: (context, state) => const MaterialPage(
              fullscreenDialog: true,
              child: AccountScreen(),
            ),
          ),
          GoRoute(
            path: 'login',
            name: AppRoute.login.name,
            pageBuilder: (context, state) => const MaterialPage(
              fullscreenDialog: true,
              child: LoginPage(
              ),
            ),
          ),
        ],
      ),*//*
    ],
    errorBuilder: (context, state) => const NotFoundScreen(),
  );
});*/
