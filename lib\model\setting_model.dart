

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../utils/timestamp_converter.dart';

part 'setting_model.freezed.dart';
part 'setting_model.g.dart';

@freezed
class SettingModel with _$SettingModel {
  @JsonSerializable(explicitToJson: true)
  factory SettingModel({
    int? maxHeight,
    int? minHeight,
    double? maxAmps,
    double? minAmps,
    int? actualHeight,
    int? timer,
    int? mode,
  }) = _SettingModel;

  factory SettingModel.fromJson(Map<String, dynamic> json) => _$SettingModelFromJson(json);

}
