

import 'dart:developer';
import 'dart:math' as mathLog;

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../services/firestore_services.dart';

class DivisionRtdbRepository {

  DivisionRtdbRepository(this.sharedPreferencesService);

  final SharedPreferencesService sharedPreferencesService;

  final DatabaseReference databaseReference = FirebaseDatabase.instance.ref();

  final dbInstance = FirestoreService.instance;

  Future<void> startMotor(String motorId) async {
    try{
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/motor/'+motorId)
          .update({
        'mobile_status': 3,
        'from_mobile': true
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/motor/notify/'+motorId)
          .update({
        'status': 1,
      })
          .whenComplete(() => debugPrint('Reset Motor Status >Rest notify.'));

    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }
  }

  Future<void> startValve(String valveId) async {
    try{
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/valve/'+valveId)
          .update({
        'mobile_status': 3,
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
     /* await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/valve/notify/'+valveId)
          .update({
        'status': 1,
      })
          .whenComplete(() => debugPrint('Reset Motor Status >Rest notify.'));*/

    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }
  }


  Future<void> startStepValve(String valveId, int step) async {
    try{
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/valve/'+valveId)
          .update({
        'mobile_status': step,
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/valve/notify/'+valveId)
          .update({
        'status': 1,
      })
          .whenComplete(() => debugPrint('Reset Motor Status >Rest notify.'));

    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }
  }

  Future<void> startFilter(String filterId) async {
    try{
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/filter/'+filterId)
          .update({
        'mobile_status': 3,
        'from_mobile': true
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }
  }

  Future<void> updateScheduler({required String siteId,required String motorValveFilter,
    required String id,required String hrs, required String mins, required String days}) async {
    try{
      if(siteId=="site5"){
        await databaseReference.child(siteId+"/${motorValveFilter}/setting/$id").set(
            {
              "hrs":hrs,
              "mins":mins,
              "days":"",
              "mode":3,
              "config":3,
              "config_timer":3
            });

      }else{
        await databaseReference.child(siteId+"/${motorValveFilter}/setting/$id").set(
            {
              "hrs":hrs,
              "mins":mins,
              "days":"",
              "mode":3,
              "config":3,
              "config_timer":3
            });
      }

    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }
  }

  Future<void> updateMotorParameter({required String siteId,required String motorId,
    required double minCurrent, required double maxCurrent,
    required String name,
    required int CF, required double currentThreshold,
    required double TH_Amps,required int uploadTime,
    required bool hasVoltage,required int mode, String? hrs, String? mins}) async {
    try{
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/motor/setting/'+motorId)
          .update({
        'min': minCurrent==0.0 ? 0.01 : minCurrent,
        'max': maxCurrent==0.0 ? 0.01 : maxCurrent,
        'CF': CF,
        'TH_Amps':TH_Amps,
        'mode': mode,
        'upload_time':uploadTime,
      })
          .whenComplete(() => debugPrint('updateCurrent of motor'));
      if(mode!=3){
        if(sharedPreferencesService.getSiteId()=="site5"){
          await databaseReference.child(sharedPreferencesService.getSiteId()+"/scheduler/$motorId").remove();
        }
      }else{
        await updateScheduler(siteId: sharedPreferencesService.getSiteId(),motorValveFilter: "motor", id: motorId, hrs: hrs!, mins: mins!, days: "");
      }

      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/motor/notify/'+motorId)
          .update({
        'name': name,
      })
          .whenComplete(() => debugPrint('updatename of motor'));
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/motor/setting/'+motorId+"/version")
          .update({
        'name':  (mathLog.Random().nextDouble() * 256).toStringAsFixed(2),
      })
          .whenComplete(() => debugPrint('updateTankParameter'));

    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> updateValveParameter({required String siteId,required String valveId,
    required String name,
    required int mode, String? hrs, String? mins, String? days}) async {
    try{
      if(mode==3){
        log("siteId: ${sharedPreferencesService.getSiteId()}");
        await updateScheduler(siteId: sharedPreferencesService.getSiteId(), motorValveFilter:"valve",id: valveId, hrs: hrs!, mins: mins!,days: days!);
      }else{
        await databaseReference
            .child(sharedPreferencesService.getSiteId()+'/valve/setting/'+valveId)
            .update({
          'mode': mode,
          'config': 3,
          'config_timer': 3,
        })
            .whenComplete(() => debugPrint('updateCurrent of motor'));
      }
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/valve/notify/'+valveId)
          .update({
        'name': name,
      })
          .whenComplete(() => debugPrint('updatename of motor'));

    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> updateTankParameter({required String siteId,
      required String tankId,
      required int actualHeight,
      required int maxHeight,
      required int minHeight,
      required String name,
      required int upload_time}
      ) async {
    try{
      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/tank/setting/'+tankId)
          .update({
        'actual': actualHeight,
        'max': maxHeight,
        'min': minHeight,
        'upload_time':upload_time,
        'name':name,
      })
          .whenComplete(() => debugPrint('updateTankParameter'));

      await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/tank/notify/'+tankId)
          .update({
        'name':name,
      })
          .whenComplete(() => debugPrint('updateTankname'));

        await databaseReference
            .child(sharedPreferencesService.getSiteId()+'/tank/setting/'+tankId+"/version")
            .update({
          'name':  (mathLog.Random().nextDouble() * 256).toStringAsFixed(2),
        })
            .whenComplete(() => debugPrint('updateTankParameter'));
     /* await databaseReference
          .child(sharedPreferencesService.getSiteId()+'/'+tankId+"_Parameter")
          .update({
        'actualHeight': actualHeight,
        'maxHeight': maxHeight
      })
          .whenComplete(() => debugPrint('updateTankParameter'));
*/
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> stopMotor(String motorId) async {
    await databaseReference
        .child(sharedPreferencesService.getSiteId()+'/motor/'+motorId)
        .update({
      'mobile_status': 2,
      'from_mobile': true
    })
        .whenComplete(() => debugPrint('updateMotorStatus > Motor stopped from mobile.'));

  }

  Future<void> stopValve(String valveId) async {
    await databaseReference
        .child(sharedPreferencesService.getSiteId()+'/valve/'+valveId)
        .update({
      'mobile_status': 2,
    })
        .whenComplete(() => debugPrint('updateValveStatus > Valve stopped from mobile.'));
   /* await databaseReference
        .child(sharedPreferencesService.getSiteId()+'/valve/notify/'+valveId)
        .update({
      'status': 2,
    })
        .whenComplete(() => debugPrint('Reset Valve Status >Rest notify.'));*/
  }

  Future<void> stopFilter(String filterId) async {
    await databaseReference
        .child(sharedPreferencesService.getSiteId()+'/filter/'+filterId)
        .update({
      'mobile_status': 2,
      'from_mobile': true
    })
        .whenComplete(() => debugPrint('updateFilterStatus > Motor stopped from mobile.'));
  }
  Stream<DatabaseEvent> readMotor(String motorId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(motorId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }



  Stream<DatabaseEvent> readFilter(String filterId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(filterId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readValve(String valveId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(valveId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readOutStatus(String outputId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(outputId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }
  Stream<DatabaseEvent> readTank(String tankId) {
    final ref = databaseReference.child(tankId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readStreamSensor(String sensorId) {
    final ref = databaseReference.child(sensorId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readBorewell(String id) {
    final ref = databaseReference.child(id);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readOutput(String outputId) {
    //log(outputId);
    final ref = databaseReference.child(outputId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readInput(String inputId) {
    final ref = databaseReference.child(inputId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Future<String> getControl(String siteId,String tankId) async {
    try {
      DatabaseEvent event = await databaseReference
          .child("${siteId}/tank/notify/${tankId}/control").once();
      DataSnapshot snapshot = event.snapshot;
      return snapshot.value.toString();
    } catch (e) {
      return "Error: $e";
    }
  }

  Future<void> setControl(String siteId,String tankId,String control) async {
    try {
      print("${siteId} $tankId $control");
      await databaseReference
          .child("${siteId}/tank/notify/${tankId}/control").set(control);
    } catch (e) {
      print("Error: $e");
    }
  }




  Future<DatabaseEvent> getTankLevel(String tankId) async {
    final ref = databaseReference.child(tankId);
    return await ref.once();
  }



  Future<void> addMotorTank(String siteId, List<int> tankId, List<int> motorId, List<int> borewellId,List<int> valveId) async {
    try{
      for(var id in tankId){
        await databaseReference.child(siteId+'/tank/'+id.toString()).
          set({
          'float': 2,
          'level': 100,
          'time': (DateTime.now().millisecondsSinceEpoch/1000).floor()
        }).whenComplete(() => debugPrint('set Tank Level -> Set tank level.'));
        await databaseReference.child(siteId+'/tank/setting/'+id.toString())
            .set({
          'actual': 350,
          'max': 300,
          'min': 100,
          'name':"Tank "+id.toString(),
          'upload_time':15,
          'status':2,
        }).whenComplete(() => debugPrint('Tank Setting > Set tank setting.'));
        await databaseReference.child(siteId+'/tank/setting/'+id.toString()+'/version')
            .set({
          'name': 3.2,
        }).whenComplete(() => debugPrint('Version > Set version setting.'));
        await databaseReference.child(siteId+'/tank/notify/'+id.toString())
            .set({
          'control': "",
          'name':"Tank "+id.toString(),
          'status':2,
        }).whenComplete(() => debugPrint('Tank Setting > Set tank setting.'));
      }

      for(var id in borewellId){
        await databaseReference.child(siteId+'/bw/'+id.toString()).
        set({
          'level': 22.3,
          'time': (DateTime.now().millisecondsSinceEpoch/1000).floor()
        }).whenComplete(() => debugPrint('set Borewell Level -> Set Borewell level.'));
        await databaseReference.child(siteId+'/bw/setting/'+id.toString())
            .set({
          'height': 100,
          'name':"Borewell "+id.toString(),
          'upload_time':15,
          'status':2,
        }).whenComplete(() => debugPrint('Bw Setting > Set Bw setting.'));
        await databaseReference.child(siteId+'/bw/setting/'+id.toString()+'/version')
            .set({
          'name': 3.2,
        }).whenComplete(() => debugPrint('Version > Set version setting.'));
        await databaseReference.child(siteId+'/bw/notify/'+id.toString())
            .set({
          'name': "Borewell "+id.toString(),
          'status':2,
          'control':""
        }).whenComplete(() => debugPrint('Version > Set version setting.'));
      }


      for(var id in motorId){
        await databaseReference.child(siteId+'/motor/'+id.toString()).
        set({
          'current': 1.1,
          'device_status': 2,
          'mobile_status':2,
          'output_status':2,
          'from_mobile':true,
          'voltage':220.5,
          'voltage_fault':2,
          'time': (DateTime.now().millisecondsSinceEpoch/1000).floor()
        }).whenComplete(() => debugPrint('set Motor Level -> Set Motor level.'));
        await databaseReference.child(siteId+'/motor/setting/'+id.toString())
            .set({
          'max': 0.01,
          'min': 0.01,
          'TH_Amps':0.1,
          'CF':0,
          'mode':1,
          'name':"Motor "+id.toString(),
          'upload_time':15,
          'status':2,
        }).whenComplete(() => debugPrint('Motor Setting > Set Motor setting.'));
        await databaseReference.child(siteId+'/motor/setting/'+id.toString()+'/version')
            .set({
          'name': 3.2,
        }).whenComplete(() => debugPrint('Version > Set motor version setting.'));
        await databaseReference.child(siteId+'/motor/notify/'+id.toString())
            .set({
          'name': "Motor "+id.toString(),
          'status':2,
          'control':""
        }).whenComplete(() => debugPrint('Version > Set motor version setting.'));
      }

      for(var id in valveId){
        await databaseReference.child(siteId+'/valve/'+id.toString()).
        set({
          'device_status': 2,
          'mobile_status':2,
          'output_status':2,
          'time': (DateTime.now().millisecondsSinceEpoch/1000).floor()
        }).whenComplete(() => debugPrint('set Valve Level -> Set Valve level.'));
        await databaseReference.child(siteId+'/valve/setting/'+id.toString())
            .set({
          'mode':1,
          'name':"Valve "+id.toString(),
          'upload_time':15,
          'status':2,
        }).whenComplete(() => debugPrint('Valve Setting > Set Valve setting.'));
        await databaseReference.child(siteId+'/valve/setting/'+id.toString()+'/version')
            .set({
          'name': 3.2,
        }).whenComplete(() => debugPrint('Version > Set valve version setting.'));
        await databaseReference.child(siteId+'/valve/notify/'+id.toString())
            .set({
          'name': "Valve "+id.toString(),
          'status':2,
          'control':""
        }).whenComplete(() => debugPrint('Version > Set motor version setting.'));
      }


    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }



}