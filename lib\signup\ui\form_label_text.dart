import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:si/constants/style_manager.dart';

class FormLabelText extends StatelessWidget {
  const FormLabelText({Key? key, required this.label}) : super(key: key);

  final String label;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 18.0, right: 16, top: 8),
      child: Text(
        label,
        style: context.displayText,
      ),
    );
  }
}
