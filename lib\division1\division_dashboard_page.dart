


import 'package:adaptive_navigation/adaptive_navigation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/division1/homepage/haripur/haripur_page.dart';
import 'package:si/division1/homepage/kolati/kolati_page.dart';
import 'package:si/division1/homepage/pandubazaar/pandubazar_page.dart';
import 'package:si/division1/homepage/sandbox_page.dart';
import 'package:si/division1/homepage/talkhu/talkhu_page.dart';
import 'package:si/kawosoti/web_dashboard_page.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../dashboard/widgets/animated_branch_container.dart';
import '../kawosoti/schema/model/user_model.dart';
import '../utils/app_colors.dart';
import 'homepage/katunje/katunje_page.dart';



/// The enum for scaffold tab.
enum ScaffoldTab {
  /// The books tab.

  /// The authors tab.
  home,
  valve,

  /// The settings tab.
  settings
}

class DivisionDashboardPage extends HookConsumerWidget {
  /// Creates a [BookstoreScaffold].
  const DivisionDashboardPage({
    required this.navigationShell,
    required this.children,
    Key? key,
  }) : super(key: key ?? const ValueKey<String>('ScaffoldWithNavBar'));

  /// The navigation shell and container for the branch Navigators.
  final StatefulNavigationShell navigationShell;

  /// The children (branch Navigators) to display in a custom container
  /// ([AnimatedBranchContainer]).
  final List<Widget> children;

  @override
  Widget build(BuildContext context,WidgetRef ref) {
    final user = ref.watch(userProvider);
    final width = MediaQuery.of(context).size.width;
    final prefs = ref.read(sharedPreferencesServiceProvider);
    ref.listen(userProvider, (previous, next) {
      ref.read(userState.notifier).state = next.asData?.value ?? UserModel();
    });
    return width < 600 ? Scaffold(
      body: AnimatedBranchContainer(
        currentIndex: navigationShell.currentIndex,
        children: children,
      ),
      bottomNavigationBar: BottomNavigationBar(
        selectedItemColor: AppColors.onThemeColor,
        unselectedItemColor: Colors.black,
        backgroundColor: AppColors.themeColor,
        // Here, the items of BottomNavigationBar are hard coded. In a real
        // world scenario, the items would most likely be generated from the
        // branches of the shell route, which can be fetched using
        // `navigationShell.route.branches`.
        items:  <BottomNavigationBarItem>[
          BottomNavigationBarItem(
              icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
              icon: Icon(Icons.settings_ethernet_outlined), label: 'More'),
        ],
        currentIndex: navigationShell.currentIndex,
        onTap: (int index) => _onTap(context, index),
      ),
    ) : prefs.getSiteId()=="site200" ?
    TalkhuPage() : prefs.getSiteId() == "site7"
        ? PandubazarPage() : prefs.getSiteId() == "site201" ? KatunjePage() : SandBoxPage();
  }

  /// Navigate to the current location of the branch at the provided index when
  /// tapping an item in the BottomNavigationBar.
  void _onTap(BuildContext context, int index) {
    // When navigating to a new branch, it's recommended to use the goBranch
    // method, as doing so makes sure the last navigation state of the
    // Navigator for the branch is restored.
    navigationShell.goBranch(
      index,
      // A common pattern when using bottom navigation bars is to support
      // navigating to the initial location when tapping the item that is
      // already active. This example demonstrates how to support this behavior,
      // using the initialLocation parameter of goBranch.
      initialLocation: index == navigationShell.currentIndex,
    );
  }
}

