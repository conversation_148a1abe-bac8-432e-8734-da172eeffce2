// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'site_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SiteFireStoreModel _$SiteFireStoreModelFromJson(Map<String, dynamic> json) {
  return _SiteFireStoreModel.fromJson(json);
}

/// @nodoc
mixin _$SiteFireStoreModel {
  double? get t1 => throw _privateConstructorUsedError;
  double? get t2 => throw _privateConstructorUsedError;
  double? get t3 => throw _privateConstructorUsedError;
  double? get t4 => throw _privateConstructorUsedError;
  double? get t5 => throw _privateConstructorUsedError;
  double? get cl => throw _privateConstructorUsedError;
  double? get ph => throw _privateConstructorUsedError;
  double? get oh => throw _privateConstructorUsedError;
  double? get bw => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  int? get time1 => throw _privateConstructorUsedError;
  int? get time2 => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get timestamp => throw _privateConstructorUsedError;

  /// Serializes this SiteFireStoreModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SiteFireStoreModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SiteFireStoreModelCopyWith<SiteFireStoreModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SiteFireStoreModelCopyWith<$Res> {
  factory $SiteFireStoreModelCopyWith(
          SiteFireStoreModel value, $Res Function(SiteFireStoreModel) then) =
      _$SiteFireStoreModelCopyWithImpl<$Res, SiteFireStoreModel>;
  @useResult
  $Res call(
      {double? t1,
      double? t2,
      double? t3,
      double? t4,
      double? t5,
      double? cl,
      double? ph,
      double? oh,
      double? bw,
      String? id,
      int? time1,
      int? time2,
      @TimestampNullableConverter() DateTime? timestamp});
}

/// @nodoc
class _$SiteFireStoreModelCopyWithImpl<$Res, $Val extends SiteFireStoreModel>
    implements $SiteFireStoreModelCopyWith<$Res> {
  _$SiteFireStoreModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SiteFireStoreModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? t1 = freezed,
    Object? t2 = freezed,
    Object? t3 = freezed,
    Object? t4 = freezed,
    Object? t5 = freezed,
    Object? cl = freezed,
    Object? ph = freezed,
    Object? oh = freezed,
    Object? bw = freezed,
    Object? id = freezed,
    Object? time1 = freezed,
    Object? time2 = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      t1: freezed == t1
          ? _value.t1
          : t1 // ignore: cast_nullable_to_non_nullable
              as double?,
      t2: freezed == t2
          ? _value.t2
          : t2 // ignore: cast_nullable_to_non_nullable
              as double?,
      t3: freezed == t3
          ? _value.t3
          : t3 // ignore: cast_nullable_to_non_nullable
              as double?,
      t4: freezed == t4
          ? _value.t4
          : t4 // ignore: cast_nullable_to_non_nullable
              as double?,
      t5: freezed == t5
          ? _value.t5
          : t5 // ignore: cast_nullable_to_non_nullable
              as double?,
      cl: freezed == cl
          ? _value.cl
          : cl // ignore: cast_nullable_to_non_nullable
              as double?,
      ph: freezed == ph
          ? _value.ph
          : ph // ignore: cast_nullable_to_non_nullable
              as double?,
      oh: freezed == oh
          ? _value.oh
          : oh // ignore: cast_nullable_to_non_nullable
              as double?,
      bw: freezed == bw
          ? _value.bw
          : bw // ignore: cast_nullable_to_non_nullable
              as double?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      time1: freezed == time1
          ? _value.time1
          : time1 // ignore: cast_nullable_to_non_nullable
              as int?,
      time2: freezed == time2
          ? _value.time2
          : time2 // ignore: cast_nullable_to_non_nullable
              as int?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SiteFireStoreModelImplCopyWith<$Res>
    implements $SiteFireStoreModelCopyWith<$Res> {
  factory _$$SiteFireStoreModelImplCopyWith(_$SiteFireStoreModelImpl value,
          $Res Function(_$SiteFireStoreModelImpl) then) =
      __$$SiteFireStoreModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? t1,
      double? t2,
      double? t3,
      double? t4,
      double? t5,
      double? cl,
      double? ph,
      double? oh,
      double? bw,
      String? id,
      int? time1,
      int? time2,
      @TimestampNullableConverter() DateTime? timestamp});
}

/// @nodoc
class __$$SiteFireStoreModelImplCopyWithImpl<$Res>
    extends _$SiteFireStoreModelCopyWithImpl<$Res, _$SiteFireStoreModelImpl>
    implements _$$SiteFireStoreModelImplCopyWith<$Res> {
  __$$SiteFireStoreModelImplCopyWithImpl(_$SiteFireStoreModelImpl _value,
      $Res Function(_$SiteFireStoreModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SiteFireStoreModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? t1 = freezed,
    Object? t2 = freezed,
    Object? t3 = freezed,
    Object? t4 = freezed,
    Object? t5 = freezed,
    Object? cl = freezed,
    Object? ph = freezed,
    Object? oh = freezed,
    Object? bw = freezed,
    Object? id = freezed,
    Object? time1 = freezed,
    Object? time2 = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$SiteFireStoreModelImpl(
      t1: freezed == t1
          ? _value.t1
          : t1 // ignore: cast_nullable_to_non_nullable
              as double?,
      t2: freezed == t2
          ? _value.t2
          : t2 // ignore: cast_nullable_to_non_nullable
              as double?,
      t3: freezed == t3
          ? _value.t3
          : t3 // ignore: cast_nullable_to_non_nullable
              as double?,
      t4: freezed == t4
          ? _value.t4
          : t4 // ignore: cast_nullable_to_non_nullable
              as double?,
      t5: freezed == t5
          ? _value.t5
          : t5 // ignore: cast_nullable_to_non_nullable
              as double?,
      cl: freezed == cl
          ? _value.cl
          : cl // ignore: cast_nullable_to_non_nullable
              as double?,
      ph: freezed == ph
          ? _value.ph
          : ph // ignore: cast_nullable_to_non_nullable
              as double?,
      oh: freezed == oh
          ? _value.oh
          : oh // ignore: cast_nullable_to_non_nullable
              as double?,
      bw: freezed == bw
          ? _value.bw
          : bw // ignore: cast_nullable_to_non_nullable
              as double?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      time1: freezed == time1
          ? _value.time1
          : time1 // ignore: cast_nullable_to_non_nullable
              as int?,
      time2: freezed == time2
          ? _value.time2
          : time2 // ignore: cast_nullable_to_non_nullable
              as int?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SiteFireStoreModelImpl implements _SiteFireStoreModel {
  const _$SiteFireStoreModelImpl(
      {this.t1,
      this.t2,
      this.t3,
      this.t4,
      this.t5,
      this.cl,
      this.ph,
      this.oh,
      this.bw,
      this.id,
      this.time1,
      this.time2,
      @TimestampNullableConverter() this.timestamp});

  factory _$SiteFireStoreModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SiteFireStoreModelImplFromJson(json);

  @override
  final double? t1;
  @override
  final double? t2;
  @override
  final double? t3;
  @override
  final double? t4;
  @override
  final double? t5;
  @override
  final double? cl;
  @override
  final double? ph;
  @override
  final double? oh;
  @override
  final double? bw;
  @override
  final String? id;
  @override
  final int? time1;
  @override
  final int? time2;
  @override
  @TimestampNullableConverter()
  final DateTime? timestamp;

  @override
  String toString() {
    return 'SiteFireStoreModel(t1: $t1, t2: $t2, t3: $t3, t4: $t4, t5: $t5, cl: $cl, ph: $ph, oh: $oh, bw: $bw, id: $id, time1: $time1, time2: $time2, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SiteFireStoreModelImpl &&
            (identical(other.t1, t1) || other.t1 == t1) &&
            (identical(other.t2, t2) || other.t2 == t2) &&
            (identical(other.t3, t3) || other.t3 == t3) &&
            (identical(other.t4, t4) || other.t4 == t4) &&
            (identical(other.t5, t5) || other.t5 == t5) &&
            (identical(other.cl, cl) || other.cl == cl) &&
            (identical(other.ph, ph) || other.ph == ph) &&
            (identical(other.oh, oh) || other.oh == oh) &&
            (identical(other.bw, bw) || other.bw == bw) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.time1, time1) || other.time1 == time1) &&
            (identical(other.time2, time2) || other.time2 == time2) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, t1, t2, t3, t4, t5, cl, ph, oh,
      bw, id, time1, time2, timestamp);

  /// Create a copy of SiteFireStoreModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SiteFireStoreModelImplCopyWith<_$SiteFireStoreModelImpl> get copyWith =>
      __$$SiteFireStoreModelImplCopyWithImpl<_$SiteFireStoreModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SiteFireStoreModelImplToJson(
      this,
    );
  }
}

abstract class _SiteFireStoreModel implements SiteFireStoreModel {
  const factory _SiteFireStoreModel(
          {final double? t1,
          final double? t2,
          final double? t3,
          final double? t4,
          final double? t5,
          final double? cl,
          final double? ph,
          final double? oh,
          final double? bw,
          final String? id,
          final int? time1,
          final int? time2,
          @TimestampNullableConverter() final DateTime? timestamp}) =
      _$SiteFireStoreModelImpl;

  factory _SiteFireStoreModel.fromJson(Map<String, dynamic> json) =
      _$SiteFireStoreModelImpl.fromJson;

  @override
  double? get t1;
  @override
  double? get t2;
  @override
  double? get t3;
  @override
  double? get t4;
  @override
  double? get t5;
  @override
  double? get cl;
  @override
  double? get ph;
  @override
  double? get oh;
  @override
  double? get bw;
  @override
  String? get id;
  @override
  int? get time1;
  @override
  int? get time2;
  @override
  @TimestampNullableConverter()
  DateTime? get timestamp;

  /// Create a copy of SiteFireStoreModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SiteFireStoreModelImplCopyWith<_$SiteFireStoreModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SiteRtDbModel _$SiteRtDbModelFromJson(Map<String, dynamic> json) {
  return _SiteRtDbModel.fromJson(json);
}

/// @nodoc
mixin _$SiteRtDbModel {
  double? get Tub1 => throw _privateConstructorUsedError;
  double? get Tub2 => throw _privateConstructorUsedError;
  double? get Tub3 => throw _privateConstructorUsedError;
  double? get Tub4 => throw _privateConstructorUsedError;
  double? get Chlorine => throw _privateConstructorUsedError;
  double? get PH => throw _privateConstructorUsedError;
  double? get OHT => throw _privateConstructorUsedError;
  double? get Borewell => throw _privateConstructorUsedError;
  int? get RMU1_timestamp => throw _privateConstructorUsedError;
  int? get RMU2_timestamp => throw _privateConstructorUsedError;
  Sensor? get sensor => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get timestamp => throw _privateConstructorUsedError;

  /// Serializes this SiteRtDbModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SiteRtDbModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SiteRtDbModelCopyWith<SiteRtDbModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SiteRtDbModelCopyWith<$Res> {
  factory $SiteRtDbModelCopyWith(
          SiteRtDbModel value, $Res Function(SiteRtDbModel) then) =
      _$SiteRtDbModelCopyWithImpl<$Res, SiteRtDbModel>;
  @useResult
  $Res call(
      {double? Tub1,
      double? Tub2,
      double? Tub3,
      double? Tub4,
      double? Chlorine,
      double? PH,
      double? OHT,
      double? Borewell,
      int? RMU1_timestamp,
      int? RMU2_timestamp,
      Sensor? sensor,
      @TimestampNullableConverter() DateTime? timestamp});
}

/// @nodoc
class _$SiteRtDbModelCopyWithImpl<$Res, $Val extends SiteRtDbModel>
    implements $SiteRtDbModelCopyWith<$Res> {
  _$SiteRtDbModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SiteRtDbModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? Tub1 = freezed,
    Object? Tub2 = freezed,
    Object? Tub3 = freezed,
    Object? Tub4 = freezed,
    Object? Chlorine = freezed,
    Object? PH = freezed,
    Object? OHT = freezed,
    Object? Borewell = freezed,
    Object? RMU1_timestamp = freezed,
    Object? RMU2_timestamp = freezed,
    Object? sensor = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      Tub1: freezed == Tub1
          ? _value.Tub1
          : Tub1 // ignore: cast_nullable_to_non_nullable
              as double?,
      Tub2: freezed == Tub2
          ? _value.Tub2
          : Tub2 // ignore: cast_nullable_to_non_nullable
              as double?,
      Tub3: freezed == Tub3
          ? _value.Tub3
          : Tub3 // ignore: cast_nullable_to_non_nullable
              as double?,
      Tub4: freezed == Tub4
          ? _value.Tub4
          : Tub4 // ignore: cast_nullable_to_non_nullable
              as double?,
      Chlorine: freezed == Chlorine
          ? _value.Chlorine
          : Chlorine // ignore: cast_nullable_to_non_nullable
              as double?,
      PH: freezed == PH
          ? _value.PH
          : PH // ignore: cast_nullable_to_non_nullable
              as double?,
      OHT: freezed == OHT
          ? _value.OHT
          : OHT // ignore: cast_nullable_to_non_nullable
              as double?,
      Borewell: freezed == Borewell
          ? _value.Borewell
          : Borewell // ignore: cast_nullable_to_non_nullable
              as double?,
      RMU1_timestamp: freezed == RMU1_timestamp
          ? _value.RMU1_timestamp
          : RMU1_timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
      RMU2_timestamp: freezed == RMU2_timestamp
          ? _value.RMU2_timestamp
          : RMU2_timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
      sensor: freezed == sensor
          ? _value.sensor
          : sensor // ignore: cast_nullable_to_non_nullable
              as Sensor?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SiteRtDbModelImplCopyWith<$Res>
    implements $SiteRtDbModelCopyWith<$Res> {
  factory _$$SiteRtDbModelImplCopyWith(
          _$SiteRtDbModelImpl value, $Res Function(_$SiteRtDbModelImpl) then) =
      __$$SiteRtDbModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? Tub1,
      double? Tub2,
      double? Tub3,
      double? Tub4,
      double? Chlorine,
      double? PH,
      double? OHT,
      double? Borewell,
      int? RMU1_timestamp,
      int? RMU2_timestamp,
      Sensor? sensor,
      @TimestampNullableConverter() DateTime? timestamp});
}

/// @nodoc
class __$$SiteRtDbModelImplCopyWithImpl<$Res>
    extends _$SiteRtDbModelCopyWithImpl<$Res, _$SiteRtDbModelImpl>
    implements _$$SiteRtDbModelImplCopyWith<$Res> {
  __$$SiteRtDbModelImplCopyWithImpl(
      _$SiteRtDbModelImpl _value, $Res Function(_$SiteRtDbModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SiteRtDbModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? Tub1 = freezed,
    Object? Tub2 = freezed,
    Object? Tub3 = freezed,
    Object? Tub4 = freezed,
    Object? Chlorine = freezed,
    Object? PH = freezed,
    Object? OHT = freezed,
    Object? Borewell = freezed,
    Object? RMU1_timestamp = freezed,
    Object? RMU2_timestamp = freezed,
    Object? sensor = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$SiteRtDbModelImpl(
      Tub1: freezed == Tub1
          ? _value.Tub1
          : Tub1 // ignore: cast_nullable_to_non_nullable
              as double?,
      Tub2: freezed == Tub2
          ? _value.Tub2
          : Tub2 // ignore: cast_nullable_to_non_nullable
              as double?,
      Tub3: freezed == Tub3
          ? _value.Tub3
          : Tub3 // ignore: cast_nullable_to_non_nullable
              as double?,
      Tub4: freezed == Tub4
          ? _value.Tub4
          : Tub4 // ignore: cast_nullable_to_non_nullable
              as double?,
      Chlorine: freezed == Chlorine
          ? _value.Chlorine
          : Chlorine // ignore: cast_nullable_to_non_nullable
              as double?,
      PH: freezed == PH
          ? _value.PH
          : PH // ignore: cast_nullable_to_non_nullable
              as double?,
      OHT: freezed == OHT
          ? _value.OHT
          : OHT // ignore: cast_nullable_to_non_nullable
              as double?,
      Borewell: freezed == Borewell
          ? _value.Borewell
          : Borewell // ignore: cast_nullable_to_non_nullable
              as double?,
      RMU1_timestamp: freezed == RMU1_timestamp
          ? _value.RMU1_timestamp
          : RMU1_timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
      RMU2_timestamp: freezed == RMU2_timestamp
          ? _value.RMU2_timestamp
          : RMU2_timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
      sensor: freezed == sensor
          ? _value.sensor
          : sensor // ignore: cast_nullable_to_non_nullable
              as Sensor?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SiteRtDbModelImpl implements _SiteRtDbModel {
  const _$SiteRtDbModelImpl(
      {this.Tub1,
      this.Tub2,
      this.Tub3,
      this.Tub4,
      this.Chlorine,
      this.PH,
      this.OHT,
      this.Borewell,
      this.RMU1_timestamp,
      this.RMU2_timestamp,
      this.sensor,
      @TimestampNullableConverter() this.timestamp});

  factory _$SiteRtDbModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SiteRtDbModelImplFromJson(json);

  @override
  final double? Tub1;
  @override
  final double? Tub2;
  @override
  final double? Tub3;
  @override
  final double? Tub4;
  @override
  final double? Chlorine;
  @override
  final double? PH;
  @override
  final double? OHT;
  @override
  final double? Borewell;
  @override
  final int? RMU1_timestamp;
  @override
  final int? RMU2_timestamp;
  @override
  final Sensor? sensor;
  @override
  @TimestampNullableConverter()
  final DateTime? timestamp;

  @override
  String toString() {
    return 'SiteRtDbModel(Tub1: $Tub1, Tub2: $Tub2, Tub3: $Tub3, Tub4: $Tub4, Chlorine: $Chlorine, PH: $PH, OHT: $OHT, Borewell: $Borewell, RMU1_timestamp: $RMU1_timestamp, RMU2_timestamp: $RMU2_timestamp, sensor: $sensor, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SiteRtDbModelImpl &&
            (identical(other.Tub1, Tub1) || other.Tub1 == Tub1) &&
            (identical(other.Tub2, Tub2) || other.Tub2 == Tub2) &&
            (identical(other.Tub3, Tub3) || other.Tub3 == Tub3) &&
            (identical(other.Tub4, Tub4) || other.Tub4 == Tub4) &&
            (identical(other.Chlorine, Chlorine) ||
                other.Chlorine == Chlorine) &&
            (identical(other.PH, PH) || other.PH == PH) &&
            (identical(other.OHT, OHT) || other.OHT == OHT) &&
            (identical(other.Borewell, Borewell) ||
                other.Borewell == Borewell) &&
            (identical(other.RMU1_timestamp, RMU1_timestamp) ||
                other.RMU1_timestamp == RMU1_timestamp) &&
            (identical(other.RMU2_timestamp, RMU2_timestamp) ||
                other.RMU2_timestamp == RMU2_timestamp) &&
            (identical(other.sensor, sensor) || other.sensor == sensor) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, Tub1, Tub2, Tub3, Tub4, Chlorine,
      PH, OHT, Borewell, RMU1_timestamp, RMU2_timestamp, sensor, timestamp);

  /// Create a copy of SiteRtDbModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SiteRtDbModelImplCopyWith<_$SiteRtDbModelImpl> get copyWith =>
      __$$SiteRtDbModelImplCopyWithImpl<_$SiteRtDbModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SiteRtDbModelImplToJson(
      this,
    );
  }
}

abstract class _SiteRtDbModel implements SiteRtDbModel {
  const factory _SiteRtDbModel(
          {final double? Tub1,
          final double? Tub2,
          final double? Tub3,
          final double? Tub4,
          final double? Chlorine,
          final double? PH,
          final double? OHT,
          final double? Borewell,
          final int? RMU1_timestamp,
          final int? RMU2_timestamp,
          final Sensor? sensor,
          @TimestampNullableConverter() final DateTime? timestamp}) =
      _$SiteRtDbModelImpl;

  factory _SiteRtDbModel.fromJson(Map<String, dynamic> json) =
      _$SiteRtDbModelImpl.fromJson;

  @override
  double? get Tub1;
  @override
  double? get Tub2;
  @override
  double? get Tub3;
  @override
  double? get Tub4;
  @override
  double? get Chlorine;
  @override
  double? get PH;
  @override
  double? get OHT;
  @override
  double? get Borewell;
  @override
  int? get RMU1_timestamp;
  @override
  int? get RMU2_timestamp;
  @override
  Sensor? get sensor;
  @override
  @TimestampNullableConverter()
  DateTime? get timestamp;

  /// Create a copy of SiteRtDbModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SiteRtDbModelImplCopyWith<_$SiteRtDbModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SiteNameModel _$SiteNameModelFromJson(Map<String, dynamic> json) {
  return _SiteNameModel.fromJson(json);
}

/// @nodoc
mixin _$SiteNameModel {
  String get site1 => throw _privateConstructorUsedError;
  String get site2 => throw _privateConstructorUsedError;
  String get site3 => throw _privateConstructorUsedError;
  String get site4 => throw _privateConstructorUsedError;
  String get site5 => throw _privateConstructorUsedError;
  String get site6 => throw _privateConstructorUsedError;
  String get site7 => throw _privateConstructorUsedError;
  String get site8 => throw _privateConstructorUsedError;

  /// Serializes this SiteNameModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SiteNameModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SiteNameModelCopyWith<SiteNameModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SiteNameModelCopyWith<$Res> {
  factory $SiteNameModelCopyWith(
          SiteNameModel value, $Res Function(SiteNameModel) then) =
      _$SiteNameModelCopyWithImpl<$Res, SiteNameModel>;
  @useResult
  $Res call(
      {String site1,
      String site2,
      String site3,
      String site4,
      String site5,
      String site6,
      String site7,
      String site8});
}

/// @nodoc
class _$SiteNameModelCopyWithImpl<$Res, $Val extends SiteNameModel>
    implements $SiteNameModelCopyWith<$Res> {
  _$SiteNameModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SiteNameModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? site1 = null,
    Object? site2 = null,
    Object? site3 = null,
    Object? site4 = null,
    Object? site5 = null,
    Object? site6 = null,
    Object? site7 = null,
    Object? site8 = null,
  }) {
    return _then(_value.copyWith(
      site1: null == site1
          ? _value.site1
          : site1 // ignore: cast_nullable_to_non_nullable
              as String,
      site2: null == site2
          ? _value.site2
          : site2 // ignore: cast_nullable_to_non_nullable
              as String,
      site3: null == site3
          ? _value.site3
          : site3 // ignore: cast_nullable_to_non_nullable
              as String,
      site4: null == site4
          ? _value.site4
          : site4 // ignore: cast_nullable_to_non_nullable
              as String,
      site5: null == site5
          ? _value.site5
          : site5 // ignore: cast_nullable_to_non_nullable
              as String,
      site6: null == site6
          ? _value.site6
          : site6 // ignore: cast_nullable_to_non_nullable
              as String,
      site7: null == site7
          ? _value.site7
          : site7 // ignore: cast_nullable_to_non_nullable
              as String,
      site8: null == site8
          ? _value.site8
          : site8 // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SiteNameModelImplCopyWith<$Res>
    implements $SiteNameModelCopyWith<$Res> {
  factory _$$SiteNameModelImplCopyWith(
          _$SiteNameModelImpl value, $Res Function(_$SiteNameModelImpl) then) =
      __$$SiteNameModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String site1,
      String site2,
      String site3,
      String site4,
      String site5,
      String site6,
      String site7,
      String site8});
}

/// @nodoc
class __$$SiteNameModelImplCopyWithImpl<$Res>
    extends _$SiteNameModelCopyWithImpl<$Res, _$SiteNameModelImpl>
    implements _$$SiteNameModelImplCopyWith<$Res> {
  __$$SiteNameModelImplCopyWithImpl(
      _$SiteNameModelImpl _value, $Res Function(_$SiteNameModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SiteNameModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? site1 = null,
    Object? site2 = null,
    Object? site3 = null,
    Object? site4 = null,
    Object? site5 = null,
    Object? site6 = null,
    Object? site7 = null,
    Object? site8 = null,
  }) {
    return _then(_$SiteNameModelImpl(
      site1: null == site1
          ? _value.site1
          : site1 // ignore: cast_nullable_to_non_nullable
              as String,
      site2: null == site2
          ? _value.site2
          : site2 // ignore: cast_nullable_to_non_nullable
              as String,
      site3: null == site3
          ? _value.site3
          : site3 // ignore: cast_nullable_to_non_nullable
              as String,
      site4: null == site4
          ? _value.site4
          : site4 // ignore: cast_nullable_to_non_nullable
              as String,
      site5: null == site5
          ? _value.site5
          : site5 // ignore: cast_nullable_to_non_nullable
              as String,
      site6: null == site6
          ? _value.site6
          : site6 // ignore: cast_nullable_to_non_nullable
              as String,
      site7: null == site7
          ? _value.site7
          : site7 // ignore: cast_nullable_to_non_nullable
              as String,
      site8: null == site8
          ? _value.site8
          : site8 // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SiteNameModelImpl implements _SiteNameModel {
  const _$SiteNameModelImpl(
      {required this.site1,
      required this.site2,
      required this.site3,
      required this.site4,
      required this.site5,
      required this.site6,
      required this.site7,
      required this.site8});

  factory _$SiteNameModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SiteNameModelImplFromJson(json);

  @override
  final String site1;
  @override
  final String site2;
  @override
  final String site3;
  @override
  final String site4;
  @override
  final String site5;
  @override
  final String site6;
  @override
  final String site7;
  @override
  final String site8;

  @override
  String toString() {
    return 'SiteNameModel(site1: $site1, site2: $site2, site3: $site3, site4: $site4, site5: $site5, site6: $site6, site7: $site7, site8: $site8)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SiteNameModelImpl &&
            (identical(other.site1, site1) || other.site1 == site1) &&
            (identical(other.site2, site2) || other.site2 == site2) &&
            (identical(other.site3, site3) || other.site3 == site3) &&
            (identical(other.site4, site4) || other.site4 == site4) &&
            (identical(other.site5, site5) || other.site5 == site5) &&
            (identical(other.site6, site6) || other.site6 == site6) &&
            (identical(other.site7, site7) || other.site7 == site7) &&
            (identical(other.site8, site8) || other.site8 == site8));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, site1, site2, site3, site4, site5, site6, site7, site8);

  /// Create a copy of SiteNameModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SiteNameModelImplCopyWith<_$SiteNameModelImpl> get copyWith =>
      __$$SiteNameModelImplCopyWithImpl<_$SiteNameModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SiteNameModelImplToJson(
      this,
    );
  }
}

abstract class _SiteNameModel implements SiteNameModel {
  const factory _SiteNameModel(
      {required final String site1,
      required final String site2,
      required final String site3,
      required final String site4,
      required final String site5,
      required final String site6,
      required final String site7,
      required final String site8}) = _$SiteNameModelImpl;

  factory _SiteNameModel.fromJson(Map<String, dynamic> json) =
      _$SiteNameModelImpl.fromJson;

  @override
  String get site1;
  @override
  String get site2;
  @override
  String get site3;
  @override
  String get site4;
  @override
  String get site5;
  @override
  String get site6;
  @override
  String get site7;
  @override
  String get site8;

  /// Create a copy of SiteNameModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SiteNameModelImplCopyWith<_$SiteNameModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
