
class TalkhuAppDimension {

  double topPadding({required double x, required double y}){
    if(y>900) {
      return 20;
    }else{
      return 45;
    }
  }

  double wellHeight({required double x, required double y}){
    return 100;
  }

  double wellWidth({required double x, required double y}){
    return 100
    ;
  }

  double wellX({required double x, required double y}){
    return 48;
  }

  double wellY({required double x, required double y}){
    if(y<620){
      return 48;
    }else{
      return 82;
    }
    return 72;
  }

  double sumpwellHeight1({required double x, required double y}){
    return 170;
  }

  double sumpwellWidth1({required double x, required double y}){
    return 120;
  }

  double sumpwellX2({required double x, required double y}){
    return l6X(x: x, y: y)+l6Length(x: x, y: y)- 10;
  }

  double sumpwellY1({required double x, required double y}){
    return l3Y(x: x, y: y) - 50;
  }

  double sumpwellHeight2({required double x, required double y}){
    return 150;
  }

  double sumpwellWidth2({required double x, required double y}){
    return 100;
  }

  double sumpwellX1({required double x, required double y}){
    return l3X(x: x, y: y) + l3Length(x: x, y: y)-10;
  }


  double sumpwellY2({required double x, required double y}){
    return wellY(x: x, y: y) - topPadding(x: x, y: y) + 30;
  }

  double t10Y({required double x, required double y}){
    return l7Y(x: x, y: y) - 30;
  }

  double t10X({required double x, required double y}){
    return l8X(x: x, y: y) + l8Length(x: x, y: y)-3;
  }

  double t21Y({required double x, required double y}){
    return l11Y(x: x, y: y)-40;
  }

  double t21X({required double x, required double y}){
    return l14X(x: x, y: y) + l14Length(x: x, y: y);
  }

  double t31Y({required double x, required double y}){
    return t11Y(x: x, y: y)+20;
  }

  double t31X({required double x, required double y}){
    return l20X(x: x, y: y) + l20Length(x: x, y: y);
  }


  double t20Y({required double x, required double y}){
    return t21Y(x: x, y: y);
  }

  double t20X({required double x, required double y}){
    return t21X(x: x, y: y) + t20Width(x: x, y: y)+l15Length(x: x, y: y)-5;
  }

  double l1X({required double x, required double y}){
    return wellX(x: x, y: y)+30;
  }

  double l1Y({required double x, required double y}){
    return wellY(x: x, y: y)+wellHeight(x: x, y: y)-10;
  }

  double l1Length({required double x, required double y}){
    return 100;
  }

  double v21X({required double x, required double y}){
    return t21X(x: x, y: y) - valveWidth(x: x, y: y);
  }

  double v21Y({required double x, required double y}){
    return t21Y(x: x, y: y) + t21Height(x: x, y: y)*0.6;
  }

  double l2X({required double x, required double y}){
    return l1X(x: x, y: y);
  }

  double l2Y({required double x, required double y}){

    return l1Y(x: x, y: y)+l1Length(x: x, y: y);
  }

  double v1X({required double x, required double y}){
    return l2X(x: x, y: y)+l2Length(x: x, y: y)-3;
  }

  double v1Y({required double x, required double y}){
    return l2Y(x: x, y: y)-37;
  }

  double v5X({required double x, required double y}){
    return t21X(x: x, y: y)+t21Width(x: x, y: y);
  }

  double v5Y({required double x, required double y}){
    return t21Y(x: x, y: y)+t21Height(x: x, y: y) - t21Height(x: x, y: y)*0.5;
  }

  double v6X({required double x, required double y}){
    return t20X(x: x, y: y)-valveWidth(x: x, y: y);
  }

  double v6Y({required double x, required double y}){
    return t20Y(x: x, y: y)+t20Height(x: x, y: y)-t20Height(x: x, y: y)*0.6;
  }

  double l2Length({required double x, required double y}){
    return 80;
  }

  double l3Length({required double x, required double y}){
    return 70;
  }

  double l3X({required double x, required double y}){
    return v1X(x: x, y: y) + valveWidth(x: x, y: y);
  }

  double l3Y({required double x, required double y}){
    return l2Y(x: x, y: y);
  }


  double l4Length({required double x, required double y}){
    return 70;
  }

  double l4X({required double x, required double y}){
    return wellX(x: x, y: y)+ wellWidth(x: x, y: y);
  }

  double l4Y({required double x, required double y}){
    return wellY(x: x, y: y) + 50;
  }

  double l5Length({required double x, required double y}){
    return 70;
  }

  double l5X({required double x, required double y}){
    return wellX(x: x, y: y)+ wellWidth(x: x, y: y) - 5;
  }

  double l5Y({required double x, required double y}){
    return wellY(x: x, y: y) + 30;
  }

  double l6Y({required double x, required double y}){
    return l5Y(x: x, y: y);
  }

  double l6Length({required double x, required double y}){
    return 60;
  }

  double l6X({required double x, required double y}){
    return v2X(x: x, y: y)+ valveWidth(x: x, y: y);
  }

  double l7Y({required double x, required double y}){
    return sumpwellY2(x: x, y: y)+sumpwellHeight2(x: x, y: y) - 60;
  }

  double l7Length({required double x, required double y}){
   return 100;
  }

  double l7X({required double x, required double y}){
    return sumpwellX2(x: x, y: y)+ sumpwellWidth2(x: x, y: y) - 15;
  }

  double l8Y({required double x, required double y}){
    return l7Y(x: x, y: y);
  }

  double l8Length({required double x, required double y}){
    return 80;
  }

  double l8X({required double x, required double y}){
    return v3X(x: x, y: y)+ valveWidth(x: x, y: y) ;
  }

  double l9Y({required double x, required double y}){
    return sumpwellY1(x: x, y: y)+45;
  }

  double l9Length({required double x, required double y}){
    return 110;
  }

  double l9X({required double x, required double y}){
    return sumpwellX1(x: x, y: y)+ sumpwellWidth1(x: x, y: y) - 10;
  }

  double l10Y({required double x, required double y}){
    return l9Y(x: x, y: y);
  }

  double l10Length({required double x, required double y}){
    return 90;
  }

  double l11X({required double x, required double y}){
    return sumpwellX1(x: x, y: y)+ sumpwellWidth1(x: x, y: y)-15;
  }

  double l11Y({required double x, required double y}){
    return sumpwellY1(x: x, y: y) + 100;
  }

  double l11Length({required double x, required double y}){
    return l9Length(x: x, y: y)+valveWidth(x: x, y: y)+l10Length(x: x, y: y)+t10Width(x: x, y: y)+50+3;
  }

  double l12X({required double x, required double y}){

    return sumpwellX2(x: x, y: y)+ sumpwellWidth2(x: x, y: y)-10;
  }


  double l12Y({required double x, required double y}){
    return l5Y(x: x, y: y);
  }

  double l12Length({required double x, required double y}){
    return l7Length(x: x, y: y)+valveWidth(x: x, y: y)+l8Length(x: x, y: y)+t10Width(x: x, y: y)+40;
  }

  double l13X({required double x, required double y}){
    return l12X(x: x, y: y)+ l12Length(x: x, y: y);
  }


  double l13Y({required double x, required double y}){
    return l12Y(x: x, y: y);
  }

  double l13Length({required double x, required double y}){
    return t10Height(x: x, y: y)+55;
  }

  double l14X({required double x, required double y}){
    return l13X(x: x, y: y)+10;
  }


  double l14Y({required double x, required double y}){
    return l11Y(x: x, y: y);
  }

  double l14Length({required double x, required double y}){
    return 80;
  }

  double l15X({required double x, required double y}){
    return t21X(x: x, y: y)+t21Width(x: x, y: y)-3;
  }

  double l15Y({required double x, required double y}){
    return t21Y(x: x, y: y)+30;
  }

  double l15Length({required double x, required double y}){
    return valveWidth(x: x, y: y)+30+valveWidth(x: x, y: y);
  }

  double l16X({required double x, required double y}){
    return v6X(x: x, y: y)-12;
  }

  double l16Y({required double x, required double y}){
    return v6Y(x: x, y: y)+30;

  }

  double l16Length({required double x, required double y}){
    return 150;
  }

  double l17X({required double x, required double y}){
    return t21X(x: x, y: y) - l17Length(x: x, y: y);
  }

  double l17Y({required double x, required double y}){
    return t21Y(x: x, y: y) + t21Height(x: x, y: y) * 0.55;
  }

  double l17Length({required double x, required double y}){
    return 300;
  }

  double l19X({required double x, required double y}){
    return t11X(x: x, y: y)+t11Width(x: x, y: y);
  }

  double l19Y({required double x, required double y}){
    return v7Y(x: x, y: y)+33;
  }

  double l19Length({required double x, required double y}){
    return 100;
  }

  double l20X({required double x, required double y}){
    return l19X(x: x, y: y)+40+l19Length(x: x, y: y);
  }

  double l20Y({required double x, required double y}){
    return l19Y(x: x, y: y);
  }

  double l20Length({required double x, required double y}){
    return 100;
  }

  double l21X({required double x, required double y}){
    return v43X(x: x, y: y);
  }

  double l21Y({required double x, required double y}){
    return v43Y(x: x, y: y)+120;
  }

  double l21Length({required double x, required double y}){
    return 140;
  }

  double l24X({required double x, required double y}){
    return t21X(x: x, y: y)+15;
  }

  double l24Y({required double x, required double y}){
    return t21Y(x: x, y: y)-l24Length(x: x, y: y) + 17;
  }

  double l24Length({required double x, required double y}){
    return 80;
  }

  double l25X({required double x, required double y}){
    return l24X(x: x, y: y) + 30;
  }

  double l25Y({required double x, required double y}){
    return l24Y(x: x, y: y) - 27;
  }

  double m20X({required double x, required double y}){
    return l24X(x: x, y: y)-43;
  }

  double m20Y({required double x, required double y}){
    return l24Y(x: x, y: y) - 105;
  }

  double m20ControlX({required double x, required double y}){
    return m20X(x: x, y: y)-20;
  }

  double m20ControlY({required double x, required double y}){
    return l24Y(x: x, y: y) - 60;
  }

  double l25Length({required double x, required double y}){
     return 80;
  }

  double v42X({required double x, required double y}){
    return t12X(x: x, y: y)-valveWidth(x: x, y: y);
  }

  double v42Y({required double x, required double y}){
    return t12Y(x: x, y: y)  + t12Height(x: x, y: y) * 0.3;
  }

  double v40X({required double x, required double y}){
    return t12X(x: x, y: y)+t12Width(x: x, y: y);
  }

  double v40Y({required double x, required double y}){
    return t12Y(x: x, y: y) + t12Height(x: x, y: y)*0.6;
  }

  double v41X({required double x, required double y}){
    return t12X(x: x, y: y)-valveWidth(x: x, y: y);
  }

  double v41Y({required double x, required double y}){
    return t12Y(x: x, y: y) + t12Height(x: x, y: y)*0.7 ;
  }

  double v15X({required double x, required double y}){
    return t12X(x: x, y: y) + t12Width(x: x, y: y);
  }

  double v15Y({required double x, required double y}){
    return t12Y(x: x, y: y) - 25;
  }



  double t11X({required double x, required double y}){
    return l18X(x: x, y: y)-t11Width(x: x, y: y);
  }

  double t11Y({required double x, required double y}){
    return l18Y(x: x, y: y)-30;
  }

  double t11Height({required double x, required double y}){
    return 190;
  }

  double t11Width({required double x, required double y}){
    return 130;
  }

  double t12X({required double x, required double y}){
    return l17X(x: x, y: y)-t12Width(x: x, y: y);
  }

  double t12Y({required double x, required double y}){
    return l17Y(x: x, y: y)-25;
  }

  double t30X({required double x, required double y}){
    return l22X(x: x, y: y) - t30Width(x: x, y: y);
  }

  double t30Y({required double x, required double y}){
    return l22Y(x: x, y: y) - 30;
  }

  double t30Height({required double x, required double y}){
    return 230;
  }

  double t30Width({required double x, required double y}){
    return 130;
  }

  double t22X({required double x, required double y}){
    return l25X(x: x, y: y)+l25Length(x: x, y: y);
  }

  double t22Y({required double x, required double y}){
    return l25Y(x: x, y: y) - 60;
  }

  double t22Height({required double x, required double y}){
    return 140;
  }

  double t22Width({required double x, required double y}){
    return 150;
  }


  double t12Height({required double x, required double y}){
    return 290;
  }

  double t12Width({required double x, required double y}){
    return 160;
  }

  double l18X({required double x, required double y}){
    return l16X(x: x, y: y)-l18Length(x: x, y: y)+3;
  }

  double l18Y({required double x, required double y}){
    return l16Y(x: x, y: y) + l16Length(x: x, y: y)-13;
  }

  double l18Length({required double x, required double y}){
    return 210;
  }

  double l22X({required double x, required double y}){
    return t12X(x: x, y: y)-l22Length(x: x, y: y);
  }

  double l22Y({required double x, required double y}){
    return t12Y(x: x, y: y) + t12Height(x: x, y: y)*0.22;
  }

  double l22Length({required double x, required double y}){
    return 150;
  }

  double l10X({required double x, required double y}){
    return v4X(x: x, y: y)+ valveWidth(x: x, y: y);
  }

  double v4X({required double x, required double y}){
    return l9X(x: x, y: y)+l9Length(x: x, y: y) - 3;
  }

  double v4Y({required double x, required double y}){
    return l9Y(x: x, y: y) - 38;
  }

  double v7X({required double x, required double y}){
    return t11X(x: x, y: y)+t11Width(x: x, y: y);
  }

  double v7Y({required double x, required double y}){
    return t11Y(x: x, y: y) + t11Height(x: x, y: y)-t11Height(x: x, y: y)*0.45;
  }

  double v8X({required double x, required double y}){
    return t31X(x: x, y: y)+t31Width(x: x, y: y);
  }

  double v8Y({required double x, required double y}){
    return t31Y(x: x, y: y) - 15;
  }

  double v9X({required double x, required double y}){
    return t31X(x: x, y: y)+t31Width(x: x, y: y);
  }

  double v9Y({required double x, required double y}){
    return t31Y(x: x, y: y) + t31Height(x: x, y: y)-t31Height(x: x, y: y)*0.45;
  }

  double v16X({required double x, required double y}){
    return t22X(x: x, y: y)+t22Width(x: x, y: y);
  }

  double v16Y({required double x, required double y}){
    return t22Y(x: x, y: y) + t22Height(x: x, y: y)-t22Height(x: x, y: y)*0.7;
  }

  double v43X({required double x, required double y}){
    return t12X(x: x, y: y)+t12Width(x: x, y: y);
  }

  double v43Y({required double x, required double y}){
    return t12Y(x: x, y: y) + 40;
  }

  double v22X({required double x, required double y}){
    return t30X(x: x, y: y)-valveWidth(x: x, y: y);
  }

  double v22Y({required double x, required double y}){
    return t30Y(x: x, y: y) + t30Height(x: x, y: y)*0.1;
  }

  double v50X({required double x, required double y}){
    return v22X(x: x, y: y);
  }

  double v50Y({required double x, required double y}){
    return t30Y(x: x, y: y) + t30Height(x: x, y: y) * 0.7;
  }


  double v3X({required double x, required double y}){
    return l7X(x: x, y: y)+l7Length(x: x, y: y) - 3;
  }

  double v3Y({required double x, required double y}){
    return l7Y(x: x, y: y) - 38;
  }

  double m11X({required double x, required double y}){
    return sumpwellX2(x: x, y: y) + 24;
  }

  double m11Y({required double x, required double y}){
    return sumpwellY2(x: x, y: y) - 10;
  }

  double m10X({required double x, required double y}){
    return sumpwellX1(x: x, y: y) + 32;
  }

  double m10Y({required double x, required double y}){
    return sumpwellY1(x: x, y: y) - 10;
  }

  double m21X({required double x, required double y}){
    return l19X(x: x, y: y)+l19Length(x: x, y: y)-30;
  }

  double m21Y({required double x, required double y}){
    return l19Y(x: x, y: y) - 70;
  }


  double v2X({required double x, required double y}){
    return l5X(x: x, y: y) +  l5Length(x: x, y: y) - 3;
  }

  double v2Y({required double x, required double y}){
    return l5Y(x: x, y: y) - 35;
  }

  double t10Height({
    required double x,
    required double y,
  }) {
    return 170.0;
  }

  double t10Width({
    required double x,
    required double y,
  }) {
    return 130.0;
  }

  double t21Height({
    required double x,
    required double y,
  }) {
    return 160.0;
  }

  double t21Width({
    required double x,
    required double y,
  }) {
    return 120.0;
  }

  double t31Height({
    required double x,
    required double y,
  }) {
    return 170.0;
  }

  double t31Width({
    required double x,
    required double y,
  }) {
    return 100.0;
  }

  double t20Height({
    required double x,
    required double y,
  }) {
    return 140.0;
  }

  double t20Width({
    required double x,
    required double y,
  }) {
    return 120.0;
  }


  double valveHeight({
    required double x,
    required double y,
  }) {
    return 45.0;
  }

  double valveWidth({
    required double x,
    required double y,
  }) {
    return 45.0;
  }

  double heightMotor({
    required double x,
    required double y,
  }) {
    return 210.0;
  }

  double heightTank({
    required double x,
    required double y,
  }) {
    return 200.0;
  }

  double widthTank({
    required double x,
    required double y,
  }) {
    return 220.0;
  }

  double widthMotor({
    required double x,
    required double y,
  }) {
    return 160.0;
  }

  double d8X({required double x, required double y}){
    return v8X(x: x, y: y)+valveWidth(x: x, y:y);
  }

  double d8Y({required double x, required double y}){
    return v8Y(x: x, y: y)+13;
  }

  double d9X({required double x, required double y}){
    return v9X(x: x, y: y)+valveWidth(x: x, y:y);
  }

  double d9Y({required double x, required double y}){
    return v9Y(x: x, y: y)+13;
  }

  double d17X({required double x, required double y}){
    return v50X(x: x, y: y)+valveWidth(x: x, y:y);
  }

  double d17Y({required double x, required double y}){
    return v50Y(x: x, y: y)+17;
  }


  double d16X({required double x, required double y}){
    return v16X(x: x, y: y)+valveWidth(x: x, y:y);
  }

  double d16Y({required double x, required double y}){
    return v16Y(x: x, y: y)+25;
  }

  double d15X({required double x, required double y}){
    return v15X(x: x, y: y)+valveWidth(x: x, y:y);
  }

  double d15Y({required double x, required double y}){
    return v15Y(x: x, y: y)+13;
  }

  double d8Width({required double x, required double y}){
    return 120;
  }

  double d11X({required double x, required double y}){
    return v22X(x: x, y: y)-d11Width(x: x, y: y);
  }


  double d11Y({required double x, required double y}){
    return v22Y(x: x, y: y)+15;
  }

  double d11Height({required double x, required double y}){
    return 50;
  }

  double d11Width({required double x, required double y}){
    return 90;
  }

  double d12X({required double x, required double y}){
    return v42X(x: x, y: y)-d12Width(x: x, y: y);
  }


  double d12Y({required double x, required double y}){
    return v42Y(x: x, y: y)+17;
  }


  double d14X({required double x, required double y}){
    return v41X(x: x, y: y)-d12Width(x: x, y: y);
  }


  double d14Y({required double x, required double y}){
    return v41Y(x: x, y: y)+17;
  }

  double d12Height({required double x, required double y}){
    return 40;
  }

  double d12Width({required double x, required double y}){
    return 75;
  }


  double d8Height({required double x, required double y}){
    return 60;
  }


}