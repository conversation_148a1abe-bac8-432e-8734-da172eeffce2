import 'dart:io';


import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/provider/auth_provider.dart';

import '../../../common/widgets/custom_social_icon.dart';

import '../../constants/app_sizes.dart';
import '../provider/login_provider.dart';

class SocialLoginSection extends HookConsumerWidget {
  const SocialLoginSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {


    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [

        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            children: [
              Expanded(
                  child: Padding(
                padding: const EdgeInsets.only(left: 48.0, right: 8),
                child: Divider(thickness: 1, color: context.onBackgroundColor.withOpacity(0.5)),
              )),
              const SizedBox(width: 10),
            /*  Text("Continue With", style: TextStyle(fontSize: 16)),
              const SizedBox(width: 10),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8.0, right: 48),
                  child: Divider(
                    thickness: 1,
                    color: context.onBackgroundColor.withOpacity(0.5),
                  ),
                ),
              ),*/
            ],
          ),
        ),
        //gapH12,
      /*  Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: CustomSocialIcon(
                onTap: () async {

                },
                // assetImagePath: AppAssets.instance.appleIcon,
                iconData: FontAwesomeIcons.google,
              ),
            ),
          ],
        ),*/
      ],
    );
  }
}
