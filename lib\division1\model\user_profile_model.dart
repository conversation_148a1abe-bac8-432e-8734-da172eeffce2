import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';



part 'user_profile_model.freezed.dart';
part 'user_profile_model.g.dart';

@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    //! -------------------------------------------------------------------------------------------| BIO
    @Default('') String id,
    @Default('') String email,
    @Default('') String firstName,
    @Default('') String lastName,
    @Default('') String address,
    @Default('') String phoneNumber,
    @Default('') String notificationToken,
    @Default(false) send_notification,
    @Default(false) is_active,
    @Default(false) is_admin,
    @Default(1) role_type

  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
}

// 0- inactive, 1-active, 2- admin, 3 - super_admin
