import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../widgets/inverter_widget.dart';

class MobileSolarPumpPage extends HookConsumerWidget {
  final DivisionSettingModel? setting;

  const MobileSolarPumpPage(this.setting, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final width = MediaQuery.of(context).size.width;
    return SizedBox(
      width: double.infinity,
      child: Stack(
        children: [
          Image.asset("assets/icons/sun.webp"),
          Positioned(
            top: 64,
            left: 64,
            child: Image.asset("assets/icons/solar.webp"),
          ),
          const Positioned(
            top: 80,
            left: 20,
            child: InverterWidget(inverterId: "10"),
          ),
          Positioned(
            top: 300,
            height: 180,
            width: width,
            child: Container(
              color: Colors.grey.shade400,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.asset(
                    "assets/icons/boring.webp",
                  ),
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text(
                      "Well",
                      style: TextStyle(fontSize: 14),
                    ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
