

import 'dart:ui';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/utils/dimensions/talkhu_dimension.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../widgets/distribution_widget.dart';

class TalkhuValveWidget extends HookConsumerWidget{

  TalkhuValveWidget({required this.size, required this.appDimension, required this.name,
    this.dName = "",
    required this.valveId, required this.role,  this.showDistribution = false, this.isRight = false});

  final Size size;

  final TalkhuAppDimension appDimension;

  final String name;

  final String dName;

  final String valveId;

  final int role;

  final bool? showDistribution;

  final bool? isRight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final setting = ref.read(localStorageProvider).getSettings();
    final valveState = useState(MotorModel());
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final role = prefs.getUserRole();

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider(valveId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valveState.value = MotorModel(
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }

          if (valveState.value.device_status == valveState.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    final motorOn =  valveState.value?.device_status==3;
    return  Container(
      height: 90,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if((showDistribution ?? false) && (!(isRight ?? false)))
            Center(
              child: Column(
                children: [
                  gapH24,
                  RotatedDistributionWidget(
                    name: dName,
                    turn: 2,
                    isActive: valveState.value.device_status == 3,
                    height: appDimension.d12Height(x: size.width, y: size.height),
                    width: appDimension.d12Width(x: size.width, y: size.height),
                  ),
                ],
              ),
            ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  child: Text(name, style: TextStyle(fontSize: 10),)
              ),
              Image.asset(
                valveState?.value.mobile_status==3 && valveState?.value.device_status==3 ?
                "assets/icons/valve_on.png" : valveState?.value.mobile_status==3 && valveState?.value.device_status==2 ?
                "assets/icons/valve_loading.png" : valveState?.value.mobile_status==2  && valveState?.value.device_status == 3?
                "assets/icons/valve_loading.png" : "assets/icons/valve.png",
                height: appDimension.valveHeight(
                    x: size.width, y: size.height),
                width: appDimension.valveWidth(
                    x: size.width, y: size.height),
              ),
              gapH4,
              Consumer(
                  builder: (context,ref,child) {
                    final prefs = ref.read(sharedPreferencesServiceProvider);

                    final userActive = ref
                        .watch(userProfileProvider)
                        .asData
                        ?.value
                        ?.role ??
                        5;
                    return FlutterSwitch(
                        width: 40.0,
                        height: 15.0,
                        activeColor: Colors.green,
                        inactiveColor: Colors.grey,
                        valueFontSize: 8.0,
                        toggleSize: 10.0,
                        value: valveState?.value.device_status == 3 && valveState?.value.mobile_status == 3,
                        borderRadius: 24.0,
                        padding: 2.0,
                        showOnOff: true,
                        onToggle: (val) async {
                          EasyLoading.show();
                          if (userActive == 0) {
                            EasyLoading.showError(
                                'Something wrong with your device.');
                          } else if (userActive == 1 ) {
                            EasyLoading.showError(
                                'You do not have enough permission. Please contact concern admins for activating the user.');
                          } else if (userActive == 2) {
                            if (valveState?.value.device_status != 3) {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .startValve(valveId);
                              await ref.read(divisionRepositoryProvider).
                              addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                            } else {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .stopValve(valveId);
                            }
                            await ref.read(divisionRepositoryProvider).
                            addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                          } else if (userActive >=3) {
                            if (valveState?.value.device_status != 3) {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .startValve(valveId);
                              await ref.read(divisionRepositoryProvider).
                              addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                            } else {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .stopValve(valveId);
                              await ref.read(divisionRepositoryProvider).
                              addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                            }
                          }else{
                            EasyLoading.showError(
                                'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                          }
                        });
                  }
              ),
            ],
          ),
          if((showDistribution ?? false) && ((isRight ?? false)))
            Center(
              child: Column(
                children: [
                  gapH24,
                  RotatedDistributionWidget(
                    name: dName,
                    isActive: valveState.value.device_status == 3,
                    turn: 0,
                    height: appDimension.d12Height(x: size.width, y: size.height),
                    width: appDimension.d12Width(x: size.width, y: size.height),
                  ),
                ],
              ),
            )

        ],
      ),
    );
  }

}
