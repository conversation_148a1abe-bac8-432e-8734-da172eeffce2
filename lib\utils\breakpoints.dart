import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class Breakpoints {
  static const desktop = 1060;
  static const tablet = 834;
  // static const mobile = 425;
  static const mobile = 500;

  static const twoColLayoutMinWidth = 640;
  static const threeColLayoutMinWidth = 900;
}


double horizontalPadding(double screenWidth) {
  if (screenWidth > Breakpoints.desktop) {
    return 0;
  } else if (screenWidth > Breakpoints.mobile) {
    return 0;
  } else {
    return 0;
  }
}

double sliverHorizontalPadding(double screenWidth) {
  if (screenWidth > Breakpoints.desktop) {
    return (screenWidth - Breakpoints.desktop) / 2;
  } else if (screenWidth > Breakpoints.mobile) {
    return 0;
  } else {
    return 0;
  }
}

double sliverHorizontalPaddingForCancelPayment(double screenWidth) {
  if (screenWidth > Breakpoints.desktop) {
    return (screenWidth - Breakpoints.desktop) / 2;
  } else if (screenWidth > Breakpoints.mobile) {
    return (screenWidth - Breakpoints.mobile) / 2;
  } else {
    return 10;
  }
}

double sliverTabletHorizontalPadding(double screenWidth) {
  if (screenWidth > Breakpoints.tablet) {
    return (screenWidth - Breakpoints.tablet) / 2;
  } else if (screenWidth > Breakpoints.mobile) {
    return 0;
  } else {
    return 0;
  }
}

double sliverFullScreenHorizontalPadding(double screenWidth) {
  if (screenWidth > 1250) {
    return (screenWidth - 1250) / 2;
  } else if (screenWidth > Breakpoints.mobile) {
    return 16;
  } else {
    return 0;
  }
}

double sliverFullScreenHorizontalPaddingForCancelPayment(double screenWidth) {
  if (screenWidth > 1250) {
    return (screenWidth - 1250) / 2;
  } else if (screenWidth > Breakpoints.mobile) {
    return (screenWidth - Breakpoints.tablet) / 2;
  } else {
    return 0;
  }
}

double homePageWebPadding(double screenWidth) {
  if (screenWidth > 1250) {
    return (screenWidth - 1250) / 2;
  } else if ((screenWidth > Breakpoints.mobile) && !kIsWeb) {
    return 16;
  } else {
    return 0;
  }
}

