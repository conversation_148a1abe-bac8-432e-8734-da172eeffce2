import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:si/utils/constants.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../common/widgets/widgets.dart';
import '../../provider/auth_provider.dart';
import '../../utils/app_colors.dart';

class DivisionSettingPage extends HookConsumerWidget {
  const DivisionSettingPage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _auth = ref.watch(authStateNotifierProviderAuth);
    final siteList = ref.watch(futureSiteListProvider);
    final roleType = ref.read(userState).role ?? 1;
    final pref = ref.read(sharedPreferencesServiceProvider);
    final settingRepo = ref.read(divisionRepositoryProvider);
    final rtdbRepo = ref.read(divisionRtdbServiceProvider);
    final bwId = ref.read(localStorageProvider).getSettings().bw_id ?? [];
    whatsapp() async {
      var contact = "+9779762267743";
      var androidUrl =
          "whatsapp://send?phone=$contact&text=${ref.read(sharedPreferencesServiceProvider).getSiteName()},";

      try {
        await launchUrl(Uri.parse(androidUrl));
      } on Exception {
        EasyLoading.showError('WhatsApp is not installed.');
      }
    }

    return BaseScaffold(
      appbarText: 'Settings',
      showLeftIcon: false,
      showFab: roleType > 4 ? true : false,
      fabColor: AppColors.themeColor,
      onFabColor: AppColors.contentColorWhite,
      onFabClick: () async {
        final navigate = await context.push<bool>(
          '/settings/addSite',
        );
        /* await settingRepo.addNewSite("site4", [10,11,12,13], [11,12,13]);
        await rtdbRepo.addMotorTank("site4", [10,11,12,13], [11,12,13]);*/
      },
      isCircularFab: true,
      child: roleType >= 3
          ? Column(
              children: [
                Expanded(
                  child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ListView(
                        children: [
                          if (roleType >= 3)
                            ListTile(
                                tileColor: Colors.black,
                                iconColor: Colors.black,
                                textColor: Colors.black,
                                leading: Icon(Icons.logout),
                                title: Text('History (Tank)'),
                                onTap: () async {
                                  context.push('/history',
                                      extra: pref.getSiteId());
                                  //context.push("/selectSite",extra: SelectSiteFrom.siteHistory);
                                }),
                          if (roleType >= 3)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          if (roleType >= 3)
                            ListTile(
                                tileColor: Colors.black,
                                iconColor: Colors.black,
                                textColor: Colors.black,
                                leading: Icon(Icons.logout),
                                title: Text('History (Sensor)'),
                                onTap: () async {
                                  context.push('/sensorHistory',
                                      extra: pref.getSiteId());
                                  //context.push("/selectSite",extra: SelectSiteFrom.siteHistory);
                                }),
                          if (roleType >= 3 && bwId.isNotEmpty)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          if (roleType >= 3 && bwId.isNotEmpty)
                            ListTile(
                                tileColor: Colors.black,
                                iconColor: Colors.black,
                                textColor: Colors.black,
                                leading: Icon(Icons.logout),
                                title: Text('History (Borewell)'),
                                onTap: () async {
                                  context.push('/bwHistory',
                                      extra: pref.getSiteId());
                                  //context.push("/selectSite",extra: SelectSiteFrom.siteHistory);
                                }),
                          if (roleType >= 3)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          if (roleType > 3)
                            ListTile(
                                tileColor: Colors.black,
                                iconColor: Colors.black,
                                textColor: Colors.black,
                                leading: Icon(Icons.logout),
                                title: Text('Logs'),
                                onTap: () async {
                                  context.push('/logs',
                                      extra: pref.getSiteId());
                                  // context.push("/selectSite",extra: SelectSiteFrom.siteHistory);
                                }),
                          if (roleType >= 3)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          if (roleType >= 3)
                            ListTile(
                                tileColor: Colors.black,
                                iconColor: Colors.black,
                                textColor: Colors.black,
                                leading: Icon(Icons.logout),
                                title: Text('Switch Site'),
                                onTap: () async {
                                  ref
                                      .read(sharedPreferencesServiceProvider)
                                      .setSiteId("");
                                  ref
                                      .read(sharedPreferencesServiceProvider)
                                      .setSite(hasSite: false);
                                  context.go("/password");
                                }),
                          if (roleType >= 3)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ListTile(
                            iconColor: Colors.black,
                            textColor: Colors.black,
                            leading: Icon(Icons.info_outline),
                            title: Text('About Us'),
                            onTap: () async {
                              launchUrl(Uri.parse(
                                  "https://swodeshiinnovation.com.np/"));
                            },
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 16.0),
                            child: SizedBox(
                              height: 0.5,
                              child: Container(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          ListTile(
                            iconColor: Colors.black,
                            textColor: Colors.black,
                            leading: Icon(Icons.support_agent_outlined),
                            title: Text('Support'),
                            onTap: () async {
                              whatsapp();
                            },
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 16.0),
                            child: SizedBox(
                              height: 0.5,
                              child: Container(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          if (roleType >= 3)
                            ListTile(
                              iconColor: Colors.black,
                              textColor: Colors.black,
                              leading: Icon(Icons.account_circle_outlined),
                              title: Text('Users'),
                              onTap: () async {
                                context.push('/users', extra: pref.getSiteId());
                              },
                            ),
                          if (roleType >= 3)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          if (roleType > 4)
                            ListTile(
                              iconColor: Colors.black,
                              textColor: Colors.black,
                              leading: Icon(Icons.delete_outline),
                              title: Text('Delete Data'),
                              onTap: () async {
                                context.push('/deleteData');
                              },
                            ),
                          if (roleType >= 4)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SizedBox(
                                height: 0.5,
                                child: Container(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ListTile(
                            tileColor: Colors.black,
                            iconColor: Colors.black,
                            textColor: Colors.black,
                            leading: Icon(Icons.logout),
                            title: Text('Logout'),
                            onTap: () async {
                              final bool didRequestSignOut =
                                  await showAlertDialog(
                                        context: context,
                                        title: 'Logout',
                                        content:
                                            'Are you sure you want to logout?',
                                        cancelActionText: 'Cancel',
                                        defaultActionText: 'Yes',
                                      ) ??
                                      false;
                              if (didRequestSignOut == true) {
                                try {
                                  EasyLoading.show(status: 'Logging Out');
                                  await ref
                                      .read(authRepositoryProvider)
                                      .updateToken(
                                          FirebaseAuth
                                                  .instance.currentUser?.uid ??
                                              '',
                                          '');
                                  await ref
                                      .read(authRepositoryProvider)
                                      .signOut();
                                  // MyApp.of(context).authService.authenticated = true;
                                  // onLoginCallback?.call(true);
                                  // AutoRouter.of(context).push(const DashboardRouter());
                                  EasyLoading.dismiss();
                                  context.replace('/login');
                                } catch (err) {
                                  print(err);
                                  // debugPrint("Error :$err");
                                }
                              }
                            },
                          ),
                        ],
                      )),
                ),
                Text(
                  'Powered by',
                  style: TextStyle(fontSize: 16, color: AppColors.themeColor),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Swodeshi Innovation',
                    style: TextStyle(
                        fontSize: 20,
                        color: AppColors.themeColor,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            )
          : Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      ListTile(
                        iconColor: Colors.black,
                        textColor: Colors.black,
                        leading: Icon(Icons.info_outline),
                        title: Text('About Us'),
                        onTap: () async {
                          launchUrl(
                              Uri.parse("https://swodeshiinnovation.com.np/"));
                        },
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          height: 0.5,
                          child: Container(
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      ListTile(
                        iconColor: Colors.black,
                        textColor: Colors.black,
                        leading: Icon(Icons.support_agent_outlined),
                        title: Text('Support'),
                        onTap: () async {
                          whatsapp();
                        },
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          height: 0.5,
                          child: Container(
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      ListTile(
                        tileColor: Colors.black,
                        iconColor: Colors.black,
                        textColor: Colors.black,
                        leading: Icon(Icons.logout),
                        title: Text('Logout'),
                        onTap: () async {
                          final bool didRequestSignOut = await showAlertDialog(
                                context: context,
                                title: 'Logout',
                                content: 'Are you sure you want to logout?',
                                cancelActionText: 'Cancel',
                                defaultActionText: 'Yes',
                              ) ??
                              false;
                          if (didRequestSignOut == true) {
                            try {
                              EasyLoading.show(status: 'Logging Out');
                              await ref
                                  .read(authRepositoryProvider)
                                  .setUserLoginInfo(false);
                              await ref
                                  .read(authRepositoryProvider)
                                  .updateToken(_auth!.uid, '');
                              await ref.read(authRepositoryProvider).signOut();
                              // MyApp.of(context).authService.authenticated = true;
                              // onLoginCallback?.call(true);
                              // AutoRouter.of(context).push(const DashboardRouter());
                              EasyLoading.dismiss();
                              context.replace('/login');
                            } catch (err) {
                              // debugPrint("Error :$err");
                            }
                          }
                        },
                      ),
                    ],
                  ),
                ),
                Text(
                  'Powered by',
                  style: TextStyle(fontSize: 16, color: AppColors.themeColor),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Swodeshi Innovation',
                    style: TextStyle(
                        fontSize: 20,
                        color: AppColors.themeColor,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
    );
  }
}
