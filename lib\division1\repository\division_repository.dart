import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/history/model/history_model.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../services/firestore_path.dart';
import '../../services/firestore_services.dart';
import '../model/level_history_model.dart';
import '../model/motor_setting_model.dart';
import '../model/tank_setting_model.dart';

abstract class IDivisionRepository {
  Future<DivisionSettingModel?> getSiteId(String password);

  Future<List<DivisionSettingModel?>> getSiteList();

  Stream<UserModel?> getUserProfile(String userId);

  Future<TankSettingModel?> getTankSetting(String tankId);

  Stream<TankSettingModel?> getStreamTankSetting(String tankId);

  Future<MotorSettingModel?> getMotorSetting(String motorId);

  Future<DivisionSettingModel?> getSiteSetting(String siteId);

  Future<int> getLength({required String siteId, required int tankId,DateTime dateTime});

  Future<int> getFlowHistoryLength({required String siteId, required int flowId,DateTime dateTime});

  Future<List<FlowHistoryModel?>> getFlowHistory({required String siteId, required int tankId,DateTime? date, DateTime? startAfterDate});

  Future<void> updateActiveStatus(String userId, bool isActive);

  Future<void> setTankSetting({required String siteId,required String tankId,
    required String tankName,
    required int actualHeight,
    required int maxHeight,
    required int minHeight,
    required bool floatEnable,
    required bool levelSensorEnable,
    required bool isActive});

  //Future<void> setDivisionSetting(DivisionSettingModel divisionSettingModel);

  Future<void> updateTankActive(String tankId, bool active);

  Future<void> setMotorSetting(String motorId, String motorName,
      double noLoadCurrent, double overloadCurrent, String power);

  Future<List<DivisionLevelHistorymodel?>> getSiteHistoryData({required String siteId, required int tankId,DateTime? date, DateTime? startAfterDate});

}

class DivisionRepository implements IDivisionRepository {
  final dbInstance = FirestoreService.instance;

  @override
  Future<DivisionSettingModel?> getSiteId(String password) async {
    final siteId = await dbInstance.collectionFuture<DivisionSettingModel>(
      path: FirestorePath.settingCollection,
      queryBuilder: (query) =>
          query.where('password', isEqualTo: password).limit(1),
      builder: (data, documentID) {
        return DivisionSettingModel.fromJson(data
          ..addAll({
            'id': documentID,
          }));
      },
    );
    return siteId.first;
  }


  @override
  Future<void> updateActiveStatus(String userId, bool isActive) async {
    try {
      if (await dbInstance.pathExistsFuture(
          path: FirestorePath.userDoc(userId))) {
        await dbInstance.updateDoc(
          path: FirestorePath.userDoc(userId),
          data: {
            'role': isActive ? 2 : 1,
          },
        );
      }
    } catch (e) {}
  }

  @override
  Stream<UserModel?> getUserProfile(String userId) =>
      dbInstance.documentStream<UserModel?>(
        path: FirestorePath.userDoc(userId),
        builder: (data, documentID) {
          // print("called getUserProfile");
          if (data == null) {
            return const UserModel();
          }

          return UserModel.fromJson(
            data
              ..addAll(
                {
                  'id': documentID,
                },
              ),
          );
        },
      );

  @override
  Future<MotorSettingModel?> getMotorSetting(String motorId) async {
    return await dbInstance.documentFuture<MotorSettingModel?>(
      path: FirestorePath.tankSettingDoc(motorId,""),
      builder: (data, documentID) =>
      data == null
          ? MotorSettingModel()
          : MotorSettingModel.fromJson(
        data
          ..addAll(
            {
              'id': documentID,
            },
          ),
      ),
    );
  }

  @override
  Future<TankSettingModel?> getTankSetting(String tankId) async {
    return await dbInstance.documentFuture<TankSettingModel?>(
      path: FirestorePath.tankSettingDoc(tankId,""),
      builder: (data, documentID) =>
      data == null
          ? TankSettingModel()
          : TankSettingModel.fromJson(
        data
          ..addAll(
            {
              'id': documentID,
            },
          ),
      ),
    );
  }

  @override
  Future<void> setMotorSetting(String motorId, String motorName,
      double noloadCurrent, double overloadCurrent, String power) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.motorSettingDoc(motorId),
        data: {
          'name': motorName,
          'no_load_current': noloadCurrent,
          'overload_current': overloadCurrent,
          'power': power
        },
      );
    } catch (e) {}
  }

  @override
  Future<void> setTankSetting({required String siteId,required String tankId,
      required String tankName,
      required int actualHeight,
      required int maxHeight,
      required int minHeight,
      required bool floatEnable,
      required bool levelSensorEnable,
      required bool isActive}) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.tankSettingDoc(siteId,tankId),
        data: {
          tankId : {
            'name': tankName,
            'actual_height': actualHeight,
            'float_enable': floatEnable,
            'max_height': maxHeight,
            'min_height': minHeight,
            'is_active': isActive,
          }
        },
      );
    } catch (e) {}
  }

  @override
  Stream<TankSettingModel?> getStreamTankSetting(String tankId) {
    return dbInstance.documentStream<TankSettingModel?>(
      path: FirestorePath.tankSettingDoc(tankId,""),
      builder: (data, documentID) =>
      data == null
          ? TankSettingModel()
          : TankSettingModel.fromJson(
        data
          ..addAll(
            {
              'id': documentID,
            },
          ),
      ),
    );
  }

  @override
  Future<void> updateTankActive(String tankId, bool active) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.tankSettingDoc(tankId,""),
        data: {
          'is_active': active,
        },
      );
    } catch (e) {}
  }

  @override
  Future<DivisionSettingModel?> getSiteSetting(String siteId) async {
    return await dbInstance.documentFuture<DivisionSettingModel?>(
      path: FirestorePath.tankSettingDoc(siteId,""),
      builder: (data, documentID) =>
      data == null
          ? DivisionSettingModel()
          : DivisionSettingModel.fromJson(
        data
          ..addAll(
            {
              'id': documentID,
            },
          ),
      ),
    );
  }

  @override
  Future<List<DivisionSettingModel?>> getSiteList() {
    return dbInstance.collectionFuture<DivisionSettingModel>(
      path: FirestorePath.settingCollection,
      queryBuilder: (query) => query.where("is_active", isEqualTo: true),
      builder: (data, documentID) {
        return DivisionSettingModel.fromJson(data
          ..addAll({
            'id': documentID,
          }));
      },
    );
  }

  Future<DocumentSnapshot?> getSchemaModel(String siteId,
      DateTime dateTime,
      int tankId,) async {
    final CollectionReference reference = FirebaseFirestore.instance
        .collection(siteId);
    final snapshot = await reference.limit(1).where("time", isEqualTo: dateTime)
        .where("tankId", isEqualTo: tankId).get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  Future<DocumentSnapshot?> getFlowHistoryModel(String siteId,
      DateTime dateTime,
      int tankId,) async {
    final CollectionReference reference = FirebaseFirestore.instance
        .collection(siteId);
    final snapshot = await reference.limit(1).where("time", isEqualTo: dateTime)
        .get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  @override
  Future<List<DivisionLevelHistorymodel?>> getSiteHistoryData(
      {required String siteId, required int tankId, DateTime? date, DateTime? startAfterDate}) async {
    final finalDate = date ?? DateTime.now();
    if (startAfterDate != null) {
      final schemaModel = await getSchemaModel(siteId, startAfterDate, tankId);
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      return dbInstance.collectionFuture(
          path: siteId ?? FirestorePath.schemaOne,
          limit: 15,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time', isGreaterThan: beforeDay)
                .where('time', isLessThan: afterDay)
                .where('tankId', isEqualTo: tankId)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            return DivisionLevelHistorymodel.fromJson(
                data..addAll({'id': docId}));
          });
    } else {
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      try {
        final model = await dbInstance.collectionFuture(
            path: siteId,
            limit: 15,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time', isGreaterThan: beforeDay)
                  .where('time', isLessThan: afterDay)
                  .where('tankId', isEqualTo: tankId)
              ;
            },
            builder: (data, docId) {
              return DivisionLevelHistorymodel.fromJson(
                  data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        rethrow;
      }
    }
  }

  @override
  Future<int> getLength(
      {required String siteId, required int tankId, DateTime? dateTime}) async {
    final day = dateTime ?? DateTime.now();
    final collectionRef = FirebaseFirestore.instance.collection(siteId).orderBy(
        'time', descending: true).
    where('time',
        isGreaterThan: DateTime(day.year, day.month, day.day - 1, 23, 59, 59))
        .where(
        'time', isLessThan: DateTime(day.year, day.month, day.day + 1, 0, 0, 0))
        .where('tankId', isEqualTo: tankId);
    final snapshot = await collectionRef.count().get();
    return snapshot.count ?? 0;
  }

/*  @override
  Future<void> setDivisionSetting(
      DivisionSettingModel divisionSettingModel) async {
    try {
      await dbInstance.updateDoc(
          path: FirestorePath.tankSettingDoc(sharedPreferencesServiceProvider.getSiteId(),divisionSettingModel.id),
          data: {
            'name': divisionSettingModel.name,
            'tank1': {
              'name': divisionSettingModel.tank10!.name,
              'actual_height': divisionSettingModel.tank10!.actual_height,
              'float_enable': divisionSettingModel.tank10!.float_enable,
              'level_sensor_enable': divisionSettingModel.tank10!
                  .level_sensor_enable,
              'max_height': divisionSettingModel.tank10!.max_height,
              'min_height': divisionSettingModel.tank10!.min_height,
              'is_active': divisionSettingModel.tank10!.is_active,
            },
            'motor1': {
              'name': divisionSettingModel.motor10!.name,
              'no_load_current': divisionSettingModel.motor10!.no_load_current,
              'overload_current': divisionSettingModel.motor10!.overload_current,
              'power': divisionSettingModel.motor10!.power,
            },

          }
      );
    } catch (e) {
      print(e);
    }
  }*/

  @override
  Future<int> getFlowHistoryLength(
      {required String siteId, required int flowId, DateTime? dateTime}) async {
    try{
      final day = dateTime ?? DateTime.now();

      final collectionRef = FirebaseFirestore.instance.collection(siteId)
          .orderBy('time', descending: true).
      where('time', isGreaterThan: DateTime(getBeforeYear(day.year, day.month), getMonth(day.month - 1),getDaysInMonth(day.year, day.month-1),11,59,59))
          .where('time', isLessThan: DateTime(getAfterYear(day.year, day.month),getMonth(day.month + 1),1,0,0,0));
      final snapshot = await collectionRef.count().get();
      return snapshot.count ?? 0;
    }catch(e){
      log(e.toString());
      return 0;
    }

  }

  int getDaysInMonth(int year, int month) {
    try{
      if (month == DateTime.february) {
        final bool isLeapYear = (year % 4 == 0) && (year % 100 != 0) || (year % 400 == 0);
        return isLeapYear ? 29 : 28;
      }
      const List<int> daysInMonth = <int>[31, -1, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
      if(month==0){
        return daysInMonth[11];
      }else if(month==13){
        return daysInMonth[0];
      }else{
        return daysInMonth[month - 1];

      }
    }catch(e){
      print(e);
      return month;
    }

  }

  int getBeforeYear(int year, int month){
     if(month<=1){
      return year-1;
    }else{
      return year;
    }
  }

  int getAfterYear(int year, int month){
    if(month>=12){
      return year+1;
    }else{
      return year;
    }
  }


  int getMonth(int month){
    if(month>12){
      return 1;
    }else if(month<1){
      return 12;
    }else{
      return month;
    }
  }

  @override
  Future<List<FlowHistoryModel?>> getFlowHistory(
      {required String siteId, required int tankId, DateTime? date, DateTime? startAfterDate}) async {
    final finalDate = date ?? DateTime.now();
    if (startAfterDate != null) {
      final schemaModel = await getFlowHistoryModel(
          siteId, startAfterDate, tankId);
      final beforeDay = DateTime(getBeforeYear(finalDate.year, finalDate.month), getMonth(finalDate.month - 1),getDaysInMonth(finalDate.year, finalDate.month-1),11,59,59);
      final afterDay = DateTime(getAfterYear(finalDate.year, finalDate.month), getMonth(finalDate.month + 1),1,0,0,0);
      return dbInstance.collectionFuture(
          path: siteId ?? FirestorePath.schemaOne,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time', isGreaterThan: beforeDay)
                .where('time', isLessThan: afterDay)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            return FlowHistoryModel.fromJson(data..addAll({'id': docId}));
          });
    } else {
      try {
        final beforeDay = DateTime(getBeforeYear(finalDate.year, finalDate.month), getMonth(finalDate.month - 1),getDaysInMonth(finalDate.year, finalDate.month-1),11,59,59);
        final afterDay = DateTime(getAfterYear(finalDate.year, finalDate.month), getMonth(finalDate.month + 1),1,0,0,0);
        final model = await dbInstance.collectionFuture(
            path: siteId,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time', isGreaterThan: beforeDay)
                  .where('time', isLessThan: afterDay)
              ;
            },
            builder: (data, docId) {
              return FlowHistoryModel.fromJson(data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        rethrow;
      }
    }
  }


}

