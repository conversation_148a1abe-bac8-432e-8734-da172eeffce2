

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:responsive_grid/responsive_grid.dart';
import 'package:si/kawosoti/schema/block_diagram/site_one_block_diagram.dart';
import 'package:si/kawosoti/history/widgets/site_one_table.dart';

import '../block_diagram/site_two_block_diagram.dart';
import '../model/site_model.dart';
import '../site_one/provider/site_one_provider.dart';
import '../widgets/chlorine_sensor.dart';
import '../widgets/ph_sensor.dart';
import '../widgets/turbidy_sensor.dart';

class SiteTwoScreen extends HookConsumerWidget{
  const SiteTwoScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final siteOneState = useState<SiteRtDbModel>(SiteRtDbModel());
    final width = MediaQuery.of(context).size.width;

    ref.listen<AsyncValue<DatabaseEvent>>(siteDataProvider('site2'), (previous, next) {
      final datasnapshot = next.asData?.value.snapshot.value as Map;
      siteOneState.value = SiteRtDbModel(
          Tub1: double.parse(datasnapshot['Tub1'].toString()),
          Tub2: double.parse(datasnapshot['Tub2'].toString()),
          Tub3: double.parse(datasnapshot['Tub3'].toString()),
          Tub4: 0.0,
          Chlorine: double.parse(datasnapshot['Chlorine'].toString()),
          PH: double.parse(datasnapshot['PH'].toString()),
          RMU1_timestamp: datasnapshot['RMU1_timestamp'],
          Borewell: double.parse(datasnapshot['Borewell'].toString()),
          RMU2_timestamp: datasnapshot['RMU2_timestamp']);

    });

    return SingleChildScrollView(
      child: Column(
        children: [
          SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SiteTwoBlockDiagram(siteRtDbModel: siteOneState.value,)
          ),
          Center(
            child: Container(
              child: ResponsiveGridList(
                  shrinkWrap: true,
                  desiredItemWidth: 200,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    TurbidySensor(sensorName: "Turbidity 1",
                      sensorValue:siteOneState.value.Tub1 ?? 0.0,
                      timeStamp: (siteOneState.value.RMU2_timestamp) ?? DateTime.now().millisecondsSinceEpoch,),
                    TurbidySensor(sensorName: "Turbidity 2",
                      sensorValue:siteOneState.value.Tub2 ?? 0.0,
                      timeStamp: siteOneState.value.RMU1_timestamp ?? DateTime.now().millisecondsSinceEpoch,),
                    TurbidySensor(sensorName: "Turbidity 3",
                      sensorValue:siteOneState.value.Tub3 ?? 0.0,
                      timeStamp: siteOneState.value.RMU1_timestamp ?? DateTime.now().millisecondsSinceEpoch,),
                  ]
              ),
            ),
          ),
          width > 800 ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ChlorineSensor(sensorValue: siteOneState.value.Chlorine ?? 0.0,
                  timeStamp: siteOneState.value.RMU2_timestamp ?? DateTime.now().millisecondsSinceEpoch),
              PHSensor(timeStamp: siteOneState.value.RMU2_timestamp ?? DateTime.now().millisecondsSinceEpoch,
                sensorValue: siteOneState.value.PH ?? 7.0,)
            ],
          ) :
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ChlorineSensor(sensorValue: siteOneState.value.Chlorine ?? 0.0,
                      timeStamp: siteOneState.value.RMU2_timestamp ?? DateTime.now().millisecondsSinceEpoch),
                  PHSensor(timeStamp: siteOneState.value.RMU2_timestamp ?? DateTime.now().millisecondsSinceEpoch,
                    sensorValue: siteOneState.value.PH ?? 7.0,)
                ],
              )

        ],
      ),
    );
  }
}