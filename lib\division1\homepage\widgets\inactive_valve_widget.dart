

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../constants/app_sizes.dart';

class InactiveValveWidget extends HookConsumerWidget{

  InactiveValveWidget({ required this.name,
    required this.ratio});

  final String name;
  final double ratio;

  @override
  Widget build(BuildContext context, WidgetRef ref) {


    return GestureDetector(
      child:  Container(
        height: 150,
        width: MediaQuery.of(context).size.width / ratio ,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(color:Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child:  Text(
                name,
                style: TextStyle(color: Colors.grey, fontSize: 16),
              ),
            ),

            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Image.asset("assets/icons/valve.png", height: 60, width: 60,),
            ),
            gapH8,
            Consumer(builder: (context, WidgetRef ref, child) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FlutterSwitch(
                      width: 60.0,
                      height: 25.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 25.0,
                      value: false ,
                      borderRadius: 30.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {

                      }),
                ),
              );
            }),
            gapH8,

          ],
        ),
      ),
    );

  }

}
