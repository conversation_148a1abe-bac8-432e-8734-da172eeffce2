

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../utils/timestamp_converter.dart';

part 'dashboard_model.freezed.dart';
part 'dashboard_model.g.dart';

@freezed
class DashboardModel with _$DashboardModel {
  factory DashboardModel({
    int? float_status,
    @TimestampNullableConverter() DateTime? float_updated_at,
    int? motor_status,
    @TimestampNullableConverter() DateTime? motor_updated_at,
    int? timer_status,
    @TimestampNullableConverter() DateTime? timer_updated_at,
    int? level,
    @TimestampNullableConverter() DateTime? level_updated_at,
    int? maxHeight,
    int? minHeight,
    int? actualHeight,
    int? timer,
    int? mode,
    double? current,
    @TimestampNullableConverter() DateTime? current_updated_at,


  }) = _DashboardModel;

  factory DashboardModel.fromJson(Map<String, dynamic> json) => _$DashboardModelFromJson(json);

}
