

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../constants/app_sizes.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';

class PandubazarSiteTwoPage extends HookConsumerWidget{


  const PandubazarSiteTwoPage(this.setting);

  final DivisionSettingModel setting;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor30State = useState<MotorModel>(MotorModel());
    final motor31State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("30"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor30State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor30State.value.device_status == motor30State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("31"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor31State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor31State.value.device_status == motor31State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return CustomScrollView(
      slivers: [
        const SliverToBoxAdapter(
          child: gapH16,
        ),
        SliverToBoxAdapter(
          child:Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                children: [
                  NewMotorUnit(
                    ratio: 3,
                    motorId: "30",
                    motorSettingModel: setting.motor30,
                    motorModel: motor30State.value,
                  ),
                  Container(
                    width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                  )
                ],
              ),
              Column(
                children: [
                  NewMotorUnit(
                    ratio: 3,
                    motorId: "31",
                    motorSettingModel: setting.motor31,
                    motorModel: motor31State.value,
                  ),
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  )
                ],
              ),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            height: 20,
            width: double.infinity,
            color: Colors.blueAccent,
          ),
        ),
        SliverToBoxAdapter(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  ),
                  Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            NewTankUnit(3, "30", setting.tank30!, 200, 150, setting: setting),
                            Container(
                              height: 15,
                              width: 20,
                              color : Colors.blueAccent,
                            ),
                            NewTankUnit(3, "21", setting.tank21!, 200, 150, sensorId: "21",
                            hasTurbidity: true, setting: setting,),

                          ],
                        ),
                      )),
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  )
                ],
              ),
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  ),
                  Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            NewTankUnit(3, "31", setting.tank31!, 200, 150,
                            setting: setting,),
                            Container(
                              height: 15,
                              width: 20,
                              color : Colors.blueAccent,
                            ),
                            NewTankUnit(3, "32", setting.tank32!, 200, 150,
                            setting: setting,),
                          ],
                        ),
                      )),
                  Container(
                    width: 20,
                    height: 40,
                    color: Colors.blueAccent,
                  )
                ],
              ),

            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            height: 60,
            color: Colors.blueAccent,
              child: const Center(
                child: Text(
                  "Distribution",
                  style: TextStyle(color: Colors.white,fontSize: 18),
                ),
              ),
          ),
        )
      ],
    );
  }

}