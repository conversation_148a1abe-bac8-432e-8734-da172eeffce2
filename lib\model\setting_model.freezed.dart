// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingModel _$SettingModelFromJson(Map<String, dynamic> json) {
  return _SettingModel.fromJson(json);
}

/// @nodoc
mixin _$SettingModel {
  int? get maxHeight => throw _privateConstructorUsedError;
  int? get minHeight => throw _privateConstructorUsedError;
  double? get maxAmps => throw _privateConstructorUsedError;
  double? get minAmps => throw _privateConstructorUsedError;
  int? get actualHeight => throw _privateConstructorUsedError;
  int? get timer => throw _privateConstructorUsedError;
  int? get mode => throw _privateConstructorUsedError;

  /// Serializes this SettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SettingModelCopyWith<SettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingModelCopyWith<$Res> {
  factory $SettingModelCopyWith(
          SettingModel value, $Res Function(SettingModel) then) =
      _$SettingModelCopyWithImpl<$Res, SettingModel>;
  @useResult
  $Res call(
      {int? maxHeight,
      int? minHeight,
      double? maxAmps,
      double? minAmps,
      int? actualHeight,
      int? timer,
      int? mode});
}

/// @nodoc
class _$SettingModelCopyWithImpl<$Res, $Val extends SettingModel>
    implements $SettingModelCopyWith<$Res> {
  _$SettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxHeight = freezed,
    Object? minHeight = freezed,
    Object? maxAmps = freezed,
    Object? minAmps = freezed,
    Object? actualHeight = freezed,
    Object? timer = freezed,
    Object? mode = freezed,
  }) {
    return _then(_value.copyWith(
      maxHeight: freezed == maxHeight
          ? _value.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      minHeight: freezed == minHeight
          ? _value.minHeight
          : minHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      maxAmps: freezed == maxAmps
          ? _value.maxAmps
          : maxAmps // ignore: cast_nullable_to_non_nullable
              as double?,
      minAmps: freezed == minAmps
          ? _value.minAmps
          : minAmps // ignore: cast_nullable_to_non_nullable
              as double?,
      actualHeight: freezed == actualHeight
          ? _value.actualHeight
          : actualHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      timer: freezed == timer
          ? _value.timer
          : timer // ignore: cast_nullable_to_non_nullable
              as int?,
      mode: freezed == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingModelImplCopyWith<$Res>
    implements $SettingModelCopyWith<$Res> {
  factory _$$SettingModelImplCopyWith(
          _$SettingModelImpl value, $Res Function(_$SettingModelImpl) then) =
      __$$SettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? maxHeight,
      int? minHeight,
      double? maxAmps,
      double? minAmps,
      int? actualHeight,
      int? timer,
      int? mode});
}

/// @nodoc
class __$$SettingModelImplCopyWithImpl<$Res>
    extends _$SettingModelCopyWithImpl<$Res, _$SettingModelImpl>
    implements _$$SettingModelImplCopyWith<$Res> {
  __$$SettingModelImplCopyWithImpl(
      _$SettingModelImpl _value, $Res Function(_$SettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxHeight = freezed,
    Object? minHeight = freezed,
    Object? maxAmps = freezed,
    Object? minAmps = freezed,
    Object? actualHeight = freezed,
    Object? timer = freezed,
    Object? mode = freezed,
  }) {
    return _then(_$SettingModelImpl(
      maxHeight: freezed == maxHeight
          ? _value.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      minHeight: freezed == minHeight
          ? _value.minHeight
          : minHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      maxAmps: freezed == maxAmps
          ? _value.maxAmps
          : maxAmps // ignore: cast_nullable_to_non_nullable
              as double?,
      minAmps: freezed == minAmps
          ? _value.minAmps
          : minAmps // ignore: cast_nullable_to_non_nullable
              as double?,
      actualHeight: freezed == actualHeight
          ? _value.actualHeight
          : actualHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      timer: freezed == timer
          ? _value.timer
          : timer // ignore: cast_nullable_to_non_nullable
              as int?,
      mode: freezed == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$SettingModelImpl implements _SettingModel {
  _$SettingModelImpl(
      {this.maxHeight,
      this.minHeight,
      this.maxAmps,
      this.minAmps,
      this.actualHeight,
      this.timer,
      this.mode});

  factory _$SettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingModelImplFromJson(json);

  @override
  final int? maxHeight;
  @override
  final int? minHeight;
  @override
  final double? maxAmps;
  @override
  final double? minAmps;
  @override
  final int? actualHeight;
  @override
  final int? timer;
  @override
  final int? mode;

  @override
  String toString() {
    return 'SettingModel(maxHeight: $maxHeight, minHeight: $minHeight, maxAmps: $maxAmps, minAmps: $minAmps, actualHeight: $actualHeight, timer: $timer, mode: $mode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingModelImpl &&
            (identical(other.maxHeight, maxHeight) ||
                other.maxHeight == maxHeight) &&
            (identical(other.minHeight, minHeight) ||
                other.minHeight == minHeight) &&
            (identical(other.maxAmps, maxAmps) || other.maxAmps == maxAmps) &&
            (identical(other.minAmps, minAmps) || other.minAmps == minAmps) &&
            (identical(other.actualHeight, actualHeight) ||
                other.actualHeight == actualHeight) &&
            (identical(other.timer, timer) || other.timer == timer) &&
            (identical(other.mode, mode) || other.mode == mode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, maxHeight, minHeight, maxAmps,
      minAmps, actualHeight, timer, mode);

  /// Create a copy of SettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingModelImplCopyWith<_$SettingModelImpl> get copyWith =>
      __$$SettingModelImplCopyWithImpl<_$SettingModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingModelImplToJson(
      this,
    );
  }
}

abstract class _SettingModel implements SettingModel {
  factory _SettingModel(
      {final int? maxHeight,
      final int? minHeight,
      final double? maxAmps,
      final double? minAmps,
      final int? actualHeight,
      final int? timer,
      final int? mode}) = _$SettingModelImpl;

  factory _SettingModel.fromJson(Map<String, dynamic> json) =
      _$SettingModelImpl.fromJson;

  @override
  int? get maxHeight;
  @override
  int? get minHeight;
  @override
  double? get maxAmps;
  @override
  double? get minAmps;
  @override
  int? get actualHeight;
  @override
  int? get timer;
  @override
  int? get mode;

  /// Create a copy of SettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SettingModelImplCopyWith<_$SettingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
