import 'dart:io';

import 'package:digital_lcd_number/digital_lcd_number.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:progress_stepper/progress_stepper.dart';
import 'package:si/division1/homepage/collection_water_volume.dart';
import 'package:si/division1/homepage/flow_reading_box.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/motor_model.dart';
import 'package:si/division1/model/motor_setting_model.dart';

import '../../constants/app_sizes.dart';
import '../provider/division_provider.dart';
import 'motor_moving.dart';

class FilterDisplayaWidget extends HookConsumerWidget {
  FilterDisplayaWidget(
      {this.filterName = 'Filter',
        this.filterId = 'filter1',
        this.roleType = 1, this.motorSettingModel});

  final String filterName;

  final String filterId;

  final int roleType;

  final MotorSettingModel? motorSettingModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter1State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('filter1'), (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        filter1State.value = MotorModel(
            device_status: datasnapshot['device_status'],mobile_status: datasnapshot['mobile_status']);
      }
      if(filter1State.value.device_status==filter1State.value.mobile_status){
        EasyLoading.dismiss();
      }
    });
    return Padding(
        padding:
        const EdgeInsets.symmetric(horizontal: 8.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.all(Radius.circular(8)),
            border: Border.all(color: Colors.black)
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 8,
              ),
              gapH8,
              Text(
                filterName ?? 'Filter',
                style: TextStyle(color: filter1State.value.device_status ==1 ?  Colors.green :Colors.grey, fontSize: 14),
              ),
              gapH16,
              Consumer(builder: (context, WidgetRef ref, child) {
                final userActive = ref
                    .watch(userProfileProvider)
                    .asData
                    ?.value
                    ?.role ??
                    5;
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: FlutterSwitch(
                        width: 100.0,
                        height: 40.0,
                        activeColor: Colors.green,
                        inactiveColor: Colors.grey,
                        valueFontSize: 16.0,
                        toggleSize: 30.0,
                        value: filter1State.value.device_status == 1 && filter1State.value.mobile_status == 1,
                        borderRadius: 30.0,
                        padding: 8.0,
                        showOnOff: true,
                        onToggle: (val) async {
                          EasyLoading.show();
                          if (await hasNetwork()) {
                            if (userActive == 0) {
                              EasyLoading.showError(
                                  'The user is inactive. Please contact concern admins for activating the user.');
                            } else if (userActive == 1 ) {
                              EasyLoading.showError(
                                  'You do not have enough permission. Please contact concern admins for activating the user.');
                            } else if (userActive == 2) {
                              if (filter1State.value.device_status != 1) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startFilter(filterId);
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopFilter(filterId);
                              }
                            } else if (userActive == 3 || userActive == 4) {
                              if (filter1State.value.device_status != 1) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startFilter(filterId);
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopFilter(filterId);
                              }
                            }else{
                              EasyLoading.showError(
                                  'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                            }
                          } else {
                            EasyLoading.showError(
                                'No internet Connection');
                          }
                        }),
                  ),
                );
              }),
              gapH8,
            ],
          ),
        ));

  }

  Future<bool> hasNetwork() async {
    try {
      final result = await InternetAddress.lookup('example.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
