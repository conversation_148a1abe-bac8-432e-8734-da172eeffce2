// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'motor_setting_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MotorSettingModel _$MotorSettingModelFromJson(Map<String, dynamic> json) {
  return _MotorSettingModel.fromJson(json);
}

/// @nodoc
mixin _$MotorSettingModel {
  String get name => throw _privateConstructorUsedError;
  String get hrs => throw _privateConstructorUsedError;
  String get mins => throw _privateConstructorUsedError;
  String get power => throw _privateConstructorUsedError;
  int get CF => throw _privateConstructorUsedError;
  bool get has_voltage => throw _privateConstructorUsedError;
  double get TH_Amps => throw _privateConstructorUsedError;
  double get max_current => throw _privateConstructorUsedError;
  double get min_current => throw _privateConstructorUsedError;
  bool get is_active => throw _privateConstructorUsedError;
  int get upload_time => throw _privateConstructorUsedError;
  int get mode => throw _privateConstructorUsedError;
  double get current_threshold => throw _privateConstructorUsedError;

  /// Serializes this MotorSettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MotorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MotorSettingModelCopyWith<MotorSettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MotorSettingModelCopyWith<$Res> {
  factory $MotorSettingModelCopyWith(
          MotorSettingModel value, $Res Function(MotorSettingModel) then) =
      _$MotorSettingModelCopyWithImpl<$Res, MotorSettingModel>;
  @useResult
  $Res call(
      {String name,
      String hrs,
      String mins,
      String power,
      int CF,
      bool has_voltage,
      double TH_Amps,
      double max_current,
      double min_current,
      bool is_active,
      int upload_time,
      int mode,
      double current_threshold});
}

/// @nodoc
class _$MotorSettingModelCopyWithImpl<$Res, $Val extends MotorSettingModel>
    implements $MotorSettingModelCopyWith<$Res> {
  _$MotorSettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MotorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? hrs = null,
    Object? mins = null,
    Object? power = null,
    Object? CF = null,
    Object? has_voltage = null,
    Object? TH_Amps = null,
    Object? max_current = null,
    Object? min_current = null,
    Object? is_active = null,
    Object? upload_time = null,
    Object? mode = null,
    Object? current_threshold = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      hrs: null == hrs
          ? _value.hrs
          : hrs // ignore: cast_nullable_to_non_nullable
              as String,
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as String,
      power: null == power
          ? _value.power
          : power // ignore: cast_nullable_to_non_nullable
              as String,
      CF: null == CF
          ? _value.CF
          : CF // ignore: cast_nullable_to_non_nullable
              as int,
      has_voltage: null == has_voltage
          ? _value.has_voltage
          : has_voltage // ignore: cast_nullable_to_non_nullable
              as bool,
      TH_Amps: null == TH_Amps
          ? _value.TH_Amps
          : TH_Amps // ignore: cast_nullable_to_non_nullable
              as double,
      max_current: null == max_current
          ? _value.max_current
          : max_current // ignore: cast_nullable_to_non_nullable
              as double,
      min_current: null == min_current
          ? _value.min_current
          : min_current // ignore: cast_nullable_to_non_nullable
              as double,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: null == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int,
      current_threshold: null == current_threshold
          ? _value.current_threshold
          : current_threshold // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MotorSettingModelImplCopyWith<$Res>
    implements $MotorSettingModelCopyWith<$Res> {
  factory _$$MotorSettingModelImplCopyWith(_$MotorSettingModelImpl value,
          $Res Function(_$MotorSettingModelImpl) then) =
      __$$MotorSettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String hrs,
      String mins,
      String power,
      int CF,
      bool has_voltage,
      double TH_Amps,
      double max_current,
      double min_current,
      bool is_active,
      int upload_time,
      int mode,
      double current_threshold});
}

/// @nodoc
class __$$MotorSettingModelImplCopyWithImpl<$Res>
    extends _$MotorSettingModelCopyWithImpl<$Res, _$MotorSettingModelImpl>
    implements _$$MotorSettingModelImplCopyWith<$Res> {
  __$$MotorSettingModelImplCopyWithImpl(_$MotorSettingModelImpl _value,
      $Res Function(_$MotorSettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of MotorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? hrs = null,
    Object? mins = null,
    Object? power = null,
    Object? CF = null,
    Object? has_voltage = null,
    Object? TH_Amps = null,
    Object? max_current = null,
    Object? min_current = null,
    Object? is_active = null,
    Object? upload_time = null,
    Object? mode = null,
    Object? current_threshold = null,
  }) {
    return _then(_$MotorSettingModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      hrs: null == hrs
          ? _value.hrs
          : hrs // ignore: cast_nullable_to_non_nullable
              as String,
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as String,
      power: null == power
          ? _value.power
          : power // ignore: cast_nullable_to_non_nullable
              as String,
      CF: null == CF
          ? _value.CF
          : CF // ignore: cast_nullable_to_non_nullable
              as int,
      has_voltage: null == has_voltage
          ? _value.has_voltage
          : has_voltage // ignore: cast_nullable_to_non_nullable
              as bool,
      TH_Amps: null == TH_Amps
          ? _value.TH_Amps
          : TH_Amps // ignore: cast_nullable_to_non_nullable
              as double,
      max_current: null == max_current
          ? _value.max_current
          : max_current // ignore: cast_nullable_to_non_nullable
              as double,
      min_current: null == min_current
          ? _value.min_current
          : min_current // ignore: cast_nullable_to_non_nullable
              as double,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: null == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int,
      current_threshold: null == current_threshold
          ? _value.current_threshold
          : current_threshold // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MotorSettingModelImpl implements _MotorSettingModel {
  const _$MotorSettingModelImpl(
      {this.name = '',
      this.hrs = '',
      this.mins = '',
      this.power = '',
      this.CF = -39,
      this.has_voltage = false,
      this.TH_Amps = 0.0,
      this.max_current = 0.0,
      this.min_current = 0.0,
      this.is_active = true,
      this.upload_time = 15,
      this.mode = 1,
      this.current_threshold = 0.0});

  factory _$MotorSettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MotorSettingModelImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String hrs;
  @override
  @JsonKey()
  final String mins;
  @override
  @JsonKey()
  final String power;
  @override
  @JsonKey()
  final int CF;
  @override
  @JsonKey()
  final bool has_voltage;
  @override
  @JsonKey()
  final double TH_Amps;
  @override
  @JsonKey()
  final double max_current;
  @override
  @JsonKey()
  final double min_current;
  @override
  @JsonKey()
  final bool is_active;
  @override
  @JsonKey()
  final int upload_time;
  @override
  @JsonKey()
  final int mode;
  @override
  @JsonKey()
  final double current_threshold;

  @override
  String toString() {
    return 'MotorSettingModel(name: $name, hrs: $hrs, mins: $mins, power: $power, CF: $CF, has_voltage: $has_voltage, TH_Amps: $TH_Amps, max_current: $max_current, min_current: $min_current, is_active: $is_active, upload_time: $upload_time, mode: $mode, current_threshold: $current_threshold)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MotorSettingModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.hrs, hrs) || other.hrs == hrs) &&
            (identical(other.mins, mins) || other.mins == mins) &&
            (identical(other.power, power) || other.power == power) &&
            (identical(other.CF, CF) || other.CF == CF) &&
            (identical(other.has_voltage, has_voltage) ||
                other.has_voltage == has_voltage) &&
            (identical(other.TH_Amps, TH_Amps) || other.TH_Amps == TH_Amps) &&
            (identical(other.max_current, max_current) ||
                other.max_current == max_current) &&
            (identical(other.min_current, min_current) ||
                other.min_current == min_current) &&
            (identical(other.is_active, is_active) ||
                other.is_active == is_active) &&
            (identical(other.upload_time, upload_time) ||
                other.upload_time == upload_time) &&
            (identical(other.mode, mode) || other.mode == mode) &&
            (identical(other.current_threshold, current_threshold) ||
                other.current_threshold == current_threshold));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      hrs,
      mins,
      power,
      CF,
      has_voltage,
      TH_Amps,
      max_current,
      min_current,
      is_active,
      upload_time,
      mode,
      current_threshold);

  /// Create a copy of MotorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MotorSettingModelImplCopyWith<_$MotorSettingModelImpl> get copyWith =>
      __$$MotorSettingModelImplCopyWithImpl<_$MotorSettingModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MotorSettingModelImplToJson(
      this,
    );
  }
}

abstract class _MotorSettingModel implements MotorSettingModel {
  const factory _MotorSettingModel(
      {final String name,
      final String hrs,
      final String mins,
      final String power,
      final int CF,
      final bool has_voltage,
      final double TH_Amps,
      final double max_current,
      final double min_current,
      final bool is_active,
      final int upload_time,
      final int mode,
      final double current_threshold}) = _$MotorSettingModelImpl;

  factory _MotorSettingModel.fromJson(Map<String, dynamic> json) =
      _$MotorSettingModelImpl.fromJson;

  @override
  String get name;
  @override
  String get hrs;
  @override
  String get mins;
  @override
  String get power;
  @override
  int get CF;
  @override
  bool get has_voltage;
  @override
  double get TH_Amps;
  @override
  double get max_current;
  @override
  double get min_current;
  @override
  bool get is_active;
  @override
  int get upload_time;
  @override
  int get mode;
  @override
  double get current_threshold;

  /// Create a copy of MotorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MotorSettingModelImplCopyWith<_$MotorSettingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ValveSettingModel _$ValveSettingModelFromJson(Map<String, dynamic> json) {
  return _ValveSettingModel.fromJson(json);
}

/// @nodoc
mixin _$ValveSettingModel {
  String get name => throw _privateConstructorUsedError;
  bool get is_active => throw _privateConstructorUsedError;
  int get upload_time => throw _privateConstructorUsedError;
  int get mode => throw _privateConstructorUsedError;
  String get hrs => throw _privateConstructorUsedError;
  String get mins => throw _privateConstructorUsedError;
  String get days => throw _privateConstructorUsedError;

  /// Serializes this ValveSettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ValveSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ValveSettingModelCopyWith<ValveSettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ValveSettingModelCopyWith<$Res> {
  factory $ValveSettingModelCopyWith(
          ValveSettingModel value, $Res Function(ValveSettingModel) then) =
      _$ValveSettingModelCopyWithImpl<$Res, ValveSettingModel>;
  @useResult
  $Res call(
      {String name,
      bool is_active,
      int upload_time,
      int mode,
      String hrs,
      String mins,
      String days});
}

/// @nodoc
class _$ValveSettingModelCopyWithImpl<$Res, $Val extends ValveSettingModel>
    implements $ValveSettingModelCopyWith<$Res> {
  _$ValveSettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ValveSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? is_active = null,
    Object? upload_time = null,
    Object? mode = null,
    Object? hrs = null,
    Object? mins = null,
    Object? days = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: null == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int,
      hrs: null == hrs
          ? _value.hrs
          : hrs // ignore: cast_nullable_to_non_nullable
              as String,
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as String,
      days: null == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ValveSettingModelImplCopyWith<$Res>
    implements $ValveSettingModelCopyWith<$Res> {
  factory _$$ValveSettingModelImplCopyWith(_$ValveSettingModelImpl value,
          $Res Function(_$ValveSettingModelImpl) then) =
      __$$ValveSettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      bool is_active,
      int upload_time,
      int mode,
      String hrs,
      String mins,
      String days});
}

/// @nodoc
class __$$ValveSettingModelImplCopyWithImpl<$Res>
    extends _$ValveSettingModelCopyWithImpl<$Res, _$ValveSettingModelImpl>
    implements _$$ValveSettingModelImplCopyWith<$Res> {
  __$$ValveSettingModelImplCopyWithImpl(_$ValveSettingModelImpl _value,
      $Res Function(_$ValveSettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValveSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? is_active = null,
    Object? upload_time = null,
    Object? mode = null,
    Object? hrs = null,
    Object? mins = null,
    Object? days = null,
  }) {
    return _then(_$ValveSettingModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: null == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int,
      hrs: null == hrs
          ? _value.hrs
          : hrs // ignore: cast_nullable_to_non_nullable
              as String,
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as String,
      days: null == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ValveSettingModelImpl implements _ValveSettingModel {
  const _$ValveSettingModelImpl(
      {this.name = '',
      this.is_active = true,
      this.upload_time = 15,
      this.mode = 1,
      this.hrs = "",
      this.mins = "",
      this.days = ""});

  factory _$ValveSettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ValveSettingModelImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final bool is_active;
  @override
  @JsonKey()
  final int upload_time;
  @override
  @JsonKey()
  final int mode;
  @override
  @JsonKey()
  final String hrs;
  @override
  @JsonKey()
  final String mins;
  @override
  @JsonKey()
  final String days;

  @override
  String toString() {
    return 'ValveSettingModel(name: $name, is_active: $is_active, upload_time: $upload_time, mode: $mode, hrs: $hrs, mins: $mins, days: $days)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValveSettingModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.is_active, is_active) ||
                other.is_active == is_active) &&
            (identical(other.upload_time, upload_time) ||
                other.upload_time == upload_time) &&
            (identical(other.mode, mode) || other.mode == mode) &&
            (identical(other.hrs, hrs) || other.hrs == hrs) &&
            (identical(other.mins, mins) || other.mins == mins) &&
            (identical(other.days, days) || other.days == days));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, is_active, upload_time, mode, hrs, mins, days);

  /// Create a copy of ValveSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValveSettingModelImplCopyWith<_$ValveSettingModelImpl> get copyWith =>
      __$$ValveSettingModelImplCopyWithImpl<_$ValveSettingModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ValveSettingModelImplToJson(
      this,
    );
  }
}

abstract class _ValveSettingModel implements ValveSettingModel {
  const factory _ValveSettingModel(
      {final String name,
      final bool is_active,
      final int upload_time,
      final int mode,
      final String hrs,
      final String mins,
      final String days}) = _$ValveSettingModelImpl;

  factory _ValveSettingModel.fromJson(Map<String, dynamic> json) =
      _$ValveSettingModelImpl.fromJson;

  @override
  String get name;
  @override
  bool get is_active;
  @override
  int get upload_time;
  @override
  int get mode;
  @override
  String get hrs;
  @override
  String get mins;
  @override
  String get days;

  /// Create a copy of ValveSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValveSettingModelImplCopyWith<_$ValveSettingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SensorSettingModel _$SensorSettingModelFromJson(Map<String, dynamic> json) {
  return _SensorSettingModel.fromJson(json);
}

/// @nodoc
mixin _$SensorSettingModel {
  bool get is_active => throw _privateConstructorUsedError;
  double get tos => throw _privateConstructorUsedError;
  double get clos => throw _privateConstructorUsedError;
  double get phos => throw _privateConstructorUsedError;
  int get tscale => throw _privateConstructorUsedError;
  int get clscale => throw _privateConstructorUsedError;
  int get phscale => throw _privateConstructorUsedError;

  /// Serializes this SensorSettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SensorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SensorSettingModelCopyWith<SensorSettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SensorSettingModelCopyWith<$Res> {
  factory $SensorSettingModelCopyWith(
          SensorSettingModel value, $Res Function(SensorSettingModel) then) =
      _$SensorSettingModelCopyWithImpl<$Res, SensorSettingModel>;
  @useResult
  $Res call(
      {bool is_active,
      double tos,
      double clos,
      double phos,
      int tscale,
      int clscale,
      int phscale});
}

/// @nodoc
class _$SensorSettingModelCopyWithImpl<$Res, $Val extends SensorSettingModel>
    implements $SensorSettingModelCopyWith<$Res> {
  _$SensorSettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SensorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? is_active = null,
    Object? tos = null,
    Object? clos = null,
    Object? phos = null,
    Object? tscale = null,
    Object? clscale = null,
    Object? phscale = null,
  }) {
    return _then(_value.copyWith(
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      tos: null == tos
          ? _value.tos
          : tos // ignore: cast_nullable_to_non_nullable
              as double,
      clos: null == clos
          ? _value.clos
          : clos // ignore: cast_nullable_to_non_nullable
              as double,
      phos: null == phos
          ? _value.phos
          : phos // ignore: cast_nullable_to_non_nullable
              as double,
      tscale: null == tscale
          ? _value.tscale
          : tscale // ignore: cast_nullable_to_non_nullable
              as int,
      clscale: null == clscale
          ? _value.clscale
          : clscale // ignore: cast_nullable_to_non_nullable
              as int,
      phscale: null == phscale
          ? _value.phscale
          : phscale // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SensorSettingModelImplCopyWith<$Res>
    implements $SensorSettingModelCopyWith<$Res> {
  factory _$$SensorSettingModelImplCopyWith(_$SensorSettingModelImpl value,
          $Res Function(_$SensorSettingModelImpl) then) =
      __$$SensorSettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool is_active,
      double tos,
      double clos,
      double phos,
      int tscale,
      int clscale,
      int phscale});
}

/// @nodoc
class __$$SensorSettingModelImplCopyWithImpl<$Res>
    extends _$SensorSettingModelCopyWithImpl<$Res, _$SensorSettingModelImpl>
    implements _$$SensorSettingModelImplCopyWith<$Res> {
  __$$SensorSettingModelImplCopyWithImpl(_$SensorSettingModelImpl _value,
      $Res Function(_$SensorSettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SensorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? is_active = null,
    Object? tos = null,
    Object? clos = null,
    Object? phos = null,
    Object? tscale = null,
    Object? clscale = null,
    Object? phscale = null,
  }) {
    return _then(_$SensorSettingModelImpl(
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      tos: null == tos
          ? _value.tos
          : tos // ignore: cast_nullable_to_non_nullable
              as double,
      clos: null == clos
          ? _value.clos
          : clos // ignore: cast_nullable_to_non_nullable
              as double,
      phos: null == phos
          ? _value.phos
          : phos // ignore: cast_nullable_to_non_nullable
              as double,
      tscale: null == tscale
          ? _value.tscale
          : tscale // ignore: cast_nullable_to_non_nullable
              as int,
      clscale: null == clscale
          ? _value.clscale
          : clscale // ignore: cast_nullable_to_non_nullable
              as int,
      phscale: null == phscale
          ? _value.phscale
          : phscale // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SensorSettingModelImpl implements _SensorSettingModel {
  const _$SensorSettingModelImpl(
      {this.is_active = true,
      this.tos = 0.0,
      this.clos = 0.0,
      this.phos = 0.0,
      this.tscale = 1,
      this.clscale = 1,
      this.phscale = 1});

  factory _$SensorSettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SensorSettingModelImplFromJson(json);

  @override
  @JsonKey()
  final bool is_active;
  @override
  @JsonKey()
  final double tos;
  @override
  @JsonKey()
  final double clos;
  @override
  @JsonKey()
  final double phos;
  @override
  @JsonKey()
  final int tscale;
  @override
  @JsonKey()
  final int clscale;
  @override
  @JsonKey()
  final int phscale;

  @override
  String toString() {
    return 'SensorSettingModel(is_active: $is_active, tos: $tos, clos: $clos, phos: $phos, tscale: $tscale, clscale: $clscale, phscale: $phscale)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SensorSettingModelImpl &&
            (identical(other.is_active, is_active) ||
                other.is_active == is_active) &&
            (identical(other.tos, tos) || other.tos == tos) &&
            (identical(other.clos, clos) || other.clos == clos) &&
            (identical(other.phos, phos) || other.phos == phos) &&
            (identical(other.tscale, tscale) || other.tscale == tscale) &&
            (identical(other.clscale, clscale) || other.clscale == clscale) &&
            (identical(other.phscale, phscale) || other.phscale == phscale));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, is_active, tos, clos, phos, tscale, clscale, phscale);

  /// Create a copy of SensorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SensorSettingModelImplCopyWith<_$SensorSettingModelImpl> get copyWith =>
      __$$SensorSettingModelImplCopyWithImpl<_$SensorSettingModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SensorSettingModelImplToJson(
      this,
    );
  }
}

abstract class _SensorSettingModel implements SensorSettingModel {
  const factory _SensorSettingModel(
      {final bool is_active,
      final double tos,
      final double clos,
      final double phos,
      final int tscale,
      final int clscale,
      final int phscale}) = _$SensorSettingModelImpl;

  factory _SensorSettingModel.fromJson(Map<String, dynamic> json) =
      _$SensorSettingModelImpl.fromJson;

  @override
  bool get is_active;
  @override
  double get tos;
  @override
  double get clos;
  @override
  double get phos;
  @override
  int get tscale;
  @override
  int get clscale;
  @override
  int get phscale;

  /// Create a copy of SensorSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SensorSettingModelImplCopyWith<_$SensorSettingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleModel _$ScheduleModelFromJson(Map<String, dynamic> json) {
  return _ScheduleModel.fromJson(json);
}

/// @nodoc
mixin _$ScheduleModel {
  String get hrs => throw _privateConstructorUsedError;
  String get mins => throw _privateConstructorUsedError;

  /// Serializes this ScheduleModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ScheduleModelCopyWith<ScheduleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleModelCopyWith<$Res> {
  factory $ScheduleModelCopyWith(
          ScheduleModel value, $Res Function(ScheduleModel) then) =
      _$ScheduleModelCopyWithImpl<$Res, ScheduleModel>;
  @useResult
  $Res call({String hrs, String mins});
}

/// @nodoc
class _$ScheduleModelCopyWithImpl<$Res, $Val extends ScheduleModel>
    implements $ScheduleModelCopyWith<$Res> {
  _$ScheduleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hrs = null,
    Object? mins = null,
  }) {
    return _then(_value.copyWith(
      hrs: null == hrs
          ? _value.hrs
          : hrs // ignore: cast_nullable_to_non_nullable
              as String,
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ScheduleModelImplCopyWith<$Res>
    implements $ScheduleModelCopyWith<$Res> {
  factory _$$ScheduleModelImplCopyWith(
          _$ScheduleModelImpl value, $Res Function(_$ScheduleModelImpl) then) =
      __$$ScheduleModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String hrs, String mins});
}

/// @nodoc
class __$$ScheduleModelImplCopyWithImpl<$Res>
    extends _$ScheduleModelCopyWithImpl<$Res, _$ScheduleModelImpl>
    implements _$$ScheduleModelImplCopyWith<$Res> {
  __$$ScheduleModelImplCopyWithImpl(
      _$ScheduleModelImpl _value, $Res Function(_$ScheduleModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hrs = null,
    Object? mins = null,
  }) {
    return _then(_$ScheduleModelImpl(
      hrs: null == hrs
          ? _value.hrs
          : hrs // ignore: cast_nullable_to_non_nullable
              as String,
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ScheduleModelImpl implements _ScheduleModel {
  const _$ScheduleModelImpl({this.hrs = "", this.mins = ""});

  factory _$ScheduleModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ScheduleModelImplFromJson(json);

  @override
  @JsonKey()
  final String hrs;
  @override
  @JsonKey()
  final String mins;

  @override
  String toString() {
    return 'ScheduleModel(hrs: $hrs, mins: $mins)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleModelImpl &&
            (identical(other.hrs, hrs) || other.hrs == hrs) &&
            (identical(other.mins, mins) || other.mins == mins));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, hrs, mins);

  /// Create a copy of ScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleModelImplCopyWith<_$ScheduleModelImpl> get copyWith =>
      __$$ScheduleModelImplCopyWithImpl<_$ScheduleModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ScheduleModelImplToJson(
      this,
    );
  }
}

abstract class _ScheduleModel implements ScheduleModel {
  const factory _ScheduleModel({final String hrs, final String mins}) =
      _$ScheduleModelImpl;

  factory _ScheduleModel.fromJson(Map<String, dynamic> json) =
      _$ScheduleModelImpl.fromJson;

  @override
  String get hrs;
  @override
  String get mins;

  /// Create a copy of ScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleModelImplCopyWith<_$ScheduleModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
