

import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:si/division1/model/level_history_model.dart';
import '../../services/firestore_services.dart';

abstract class IBorewellRepository {

  Future<List<BorewellHistoryModel?>> getBwHistory({required String siteId, required int id, int? date, int? startAfterDate});
  Future<int> getLength({required String siteId, required int id,int dateTime});

}

class BorewellRepository implements IBorewellRepository {
  final dbInstance = FirestoreService.instance;

  @override
  Future<List<BorewellHistoryModel?>> getBwHistory({required String siteId, required int id, int? date, int? startAfterDate}) async {
    final finalDate = DateTime.fromMillisecondsSinceEpoch(date ?? DateTime.now().millisecondsSinceEpoch) ?? DateTime.now();
    log(siteId);
    log(id.toString());
    if (startAfterDate != null) {
      final schemaModel = await getBwModel(siteId, startAfterDate, id);
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      log(beforeDay.toString());
      log(afterDay.toString());
      return dbInstance.collectionFuture(
          path: "bwHistory",
          limit: 15,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time', isGreaterThan: beforeDay.millisecondsSinceEpoch)

                .where('time', isLessThan: afterDay)
                .where('siteId',isEqualTo: int.parse(siteId.replaceAll("site", "").trim()))
            .where('bwId',isEqualTo: id)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            log(data.toString());
            return BorewellHistoryModel.fromJson(
                data..addAll({'id': docId}));
          });
    } else {
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      log(beforeDay.toString());
      log(afterDay.toString());
      log(siteId);
      log(id.toString());
      try {
        final model = await dbInstance.collectionFuture(
            path: "bwHistory",
            limit: 15,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time', isGreaterThan: (beforeDay.millisecondsSinceEpoch/1000).ceil())
                  .where('time', isLessThan: (afterDay.millisecondsSinceEpoch/1000).ceil())
                  .where('siteId', isEqualTo: int.parse(siteId.replaceAll("site", "").trim()))
                .where('bwId',isEqualTo: id)
              ;
            },
            builder: (data, docId) {
              return BorewellHistoryModel.fromJson(
                  data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        log(e.toString());
        rethrow;
      }
    }
  }

  Future<DocumentSnapshot?> getBwModel(String siteId,
      int dateTime,
      int id) async {
    final CollectionReference reference = FirebaseFirestore.instance
        .collection("bwHistory");
    final snapshot = await reference.limit(1).where("time", isEqualTo: dateTime)
        .where('siteId',isEqualTo: int.parse(siteId.replaceAll("site", "").trim()))
    .where('bwId',isEqualTo: id)
        .get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  @override
  Future<int> getLength({required String siteId, required int id, int? dateTime}) async {
    try{
      final day = DateTime.fromMillisecondsSinceEpoch(dateTime ?? DateTime.now().millisecondsSinceEpoch);
      final collectionRef = FirebaseFirestore.instance.collection("bwHistory").orderBy(
          'time', descending: true).
      where('time',
          isGreaterThan: DateTime(day.year, day.month, day.day - 1, 23, 59, 59).millisecondsSinceEpoch/1000)
          .where(
          'time', isLessThan: DateTime(day.year, day.month, day.day + 1, 0, 0, 0).millisecondsSinceEpoch/1000)
          .where('siteId', isEqualTo: int.parse(siteId.replaceAll("site", "").trim())).where('bwId', isEqualTo: id);
      final snapshot = await collectionRef.count().get();
      return snapshot.count ?? 0;
    }catch(e){
      log(e.toString());
      rethrow;
    }


  }


}
