import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:si/routing/kawasoki_app_router.dart';

import '../firebase_options.dart';
import '../services/locator.dart';
import '../services/shared_preferences_service.dart';
import '../url_strategy/nonweb_url_strategy.dart'
    if (dart.library.html) 'web_url_strategy.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (kIsWeb) {
    configureUrl();
  }
  await Firebase.initializeApp();

  setupLocator();
  final sharedPreferences = await SharedPreferences.getInstance();

  runApp(ProviderScope(overrides: [
    sharedPreferencesServiceProvider.overrideWithValue(
      SharedPreferencesService(sharedPreferences),
    ),
  ], child: MyApp()));
}

class MyApp extends StatefulHookConsumerWidget {
  MyApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => AppState();
}

class AppState extends ConsumerState<MyApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      builder: EasyLoading.init(),
      routerConfig: ref.read(gokawosokiRouterProvider),
    );
  }
}

/// A page that fades in an out.
class KawosokiFadeTransitionPage extends CustomTransitionPage<void> {
  /// Creates a [FadeTransitionPage].
  KawosokiFadeTransitionPage({
    required LocalKey super.key,
    required super.child,
  }) : super(
            transitionsBuilder: (BuildContext context,
                    Animation<double> animation,
                    Animation<double> secondaryAnimation,
                    Widget child) =>
                FadeTransition(
                  opacity: animation.drive(_curveTween),
                  child: child,
                ));

  static final CurveTween _curveTween = CurveTween(curve: Curves.easeIn);
}
