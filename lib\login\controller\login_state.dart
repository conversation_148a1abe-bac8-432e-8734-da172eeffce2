import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/string_validators.dart';



/// Mixin class to be used for client-side email & password validation
mixin LoginValidators {
  final StringValidator emailSubmitValidator = EmailSubmitRegexValidator();
  final StringValidator passwordRegisterSubmitValidator =
  MinLengthStringValidator(8);
  final StringValidator passwordSignInSubmitValidator =
  NonEmptyStringValidator();
}

/// State class for the email & password form.
class LoginState with LoginValidators {
  LoginState({
    this.value = const AsyncValue.data(null),
  });

  final AsyncValue<void> value;

  bool get isLoading => value.isLoading;

  LoginState copyWith({
    AsyncValue<void>? value,
  }) {
    return LoginState(
      value: value ?? this.value,
    );
  }

  @override
  String toString() =>
      'EmailPasswordSignInState( value: $value)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LoginState &&
        other.value == value;
  }

  @override
  int get hashCode =>  value.hashCode;
}

class PasswordState with LoginValidators {
  PasswordState({
    this.value = const AsyncValue.data(null),
  });

  final AsyncValue<void> value;

  bool get isLoading => value.isLoading;

  LoginState copyWith({
    AsyncValue<void>? value,
  }) {
    return LoginState(
      value: value ?? this.value,
    );
  }

  @override
  String toString() =>
      'PasswordState( value: $value)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LoginState &&
        other.value == value;
  }

  @override
  int get hashCode =>  value.hashCode;
}

extension LoginStateX on LoginState {


  bool canSubmitEmail(String email) {
    return emailSubmitValidator.isValid(email);
  }

  bool canSubmitPassword(String password) {
    return passwordSignInSubmitValidator.isValid(password);
  }

  String? emailErrorText(String email) {
    final bool showErrorText = !canSubmitEmail(email);
    final String errorText = email.isEmpty
        ? "Email is required"
        : "Email is invalid";
    return showErrorText ? errorText : null;
  }

  String? passwordErrorText(String password) {
    final bool showErrorText = !canSubmitPassword(password);
    final String errorText = password.isEmpty
        ? "Password is required"
        : "Short password";
    return showErrorText ? errorText : null;
  }
}
