import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:equatable/equatable.dart';

import '../../utils/string_validators.dart';


mixin SignupValidators {
  final StringValidator emailSubmitValidator = EmailSubmitRegexValidator();
  final StringValidator passwordMinLengthSubmitValidator = MinLengthStringValidator(8);

  final StringValidator passwordSubmitValidatorForSpecialCase = PasswordSubmitRegexValidatorForSpecialCase();
  final StringValidator phoneMinLengthSubmitValidator = MinMaxLengthStringValidator(10,12);

  final StringValidator passwordSignInSubmitValidator = NonEmptyStringValidator();
  final StringValidator firstNameSignInSubmitValidator = NonEmptyStringValidator();
  final StringValidator lastNameSignInSubmitValidator = NonEmptyStringValidator();
}

class SignupState extends Equatable with SignupValidators {
  SignupState({
    this.value = const AsyncValue.data(null),
  });
  final AsyncValue<void> value;

  bool get isLoading => value.isLoading;

  SignupState copyWith({
    AsyncValue<void>? value,
  }) {
    return SignupState(
      value: value ?? this.value,
    );
  }

  @override
  String toString() {
    return 'SignupState{value: $value}';
  }

  @override
  List<Object?> get props => [value];
}

extension SignupStateX on SignupState {
  bool canSubmitFirstName(String email) {
    return firstNameSignInSubmitValidator.isValid(email);
  }

  bool canSubmitLastName(String email) {
    return lastNameSignInSubmitValidator.isValid(email);
  }

  bool canSubmitEmail(String email) {
    return emailSubmitValidator.isValid(email);
  }

  bool canSubmitPhoneNumber(String phone) {
    return phoneMinLengthSubmitValidator.isValid(phone);
  }

  bool canSubmitPassword(String password) {
    return passwordSignInSubmitValidator.isValid(password) && passwordMinLengthSubmitValidator.isValid(password);
  }

  bool canSubmitPasswordWithCustomRegexValidator(String password) {
    return passwordSubmitValidatorForSpecialCase.isValid(password);
  }

  String? firstNameErrorText(String firstName) {
    final bool showErrorText = !canSubmitFirstName(firstName);
    final String? errorText = firstName.trim().isEmpty ? "First name is required" : null;
    return showErrorText ? errorText : null;
  }

  String? lastNameErrorText(String lastName) {
    final bool showErrorText = !canSubmitLastName(lastName);
    final String? errorText = lastName.trim().isEmpty ? "Last name is required" : null;
    return showErrorText ? errorText : null;
  }

  String? phoneErrorText(String phoneNumber) {
    if(phoneNumber.isEmpty){
      return null;
    }else{
      if(canSubmitPhoneNumber(phoneNumber)){
        return null;
      }else{
        return "Invalid contact number";
      }

    }
  }


  String? emailErrorText(String email) {
    final bool showErrorText = !canSubmitEmail(email);
    final String errorText = email.isEmpty ? "Email is required" : "Invalid Email";
    return showErrorText ? errorText : null;
  }

  String? passwordErrorText(String password) {
    final bool showBasicErrorText = !canSubmitPassword(password);
    final bool showSpecialErrorText = !canSubmitPasswordWithCustomRegexValidator(password);
    final String basicErrorText =
        password.isEmpty ? "Password is required" : "Please enter a valid password";
    final String specialErrorText =
        "Password is required";
    if (showBasicErrorText) {
      return basicErrorText;
    } else {
      return showSpecialErrorText ? specialErrorText : null;
    }
  }
}
