

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

class TimestampConverter implements JsonConverter<DateTime, Timestamp> {
  const TimestampConverter();

  @override
  DateTime fromJson(Timestamp json) => json.toDate();

  @override
  Timestamp toJson(DateTime object) => Timestamp.fromDate(object);
}

class DateToTimestampConverter implements JsonConverter<Timestamp?, DateTime?> {
  const DateToTimestampConverter();

  @override
  DateTime? toJson(Timestamp? timestamp) {
    if (timestamp == null) return null;
    return timestamp.toDate();
  }

  @override
  Timestamp? fromJson(DateTime? date) {
    if (date == null) return null;
    return Timestamp.fromDate(date);
  }
}

class TimestampNullableConverter implements JsonConverter<DateTime?, Timestamp?> {
  const TimestampNullableConverter();

  @override
  DateTime? from<PERSON>son(Timestamp? json) => json?.toDate();

  @override
  Timestamp? toJson(DateTime? object) => object == null ? null : Timestamp.fromDate(object);
}