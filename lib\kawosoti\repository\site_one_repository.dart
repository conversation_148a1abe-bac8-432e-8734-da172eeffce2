import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';

import '../../../../services/firestore_path.dart';
import '../../../../services/firestore_services.dart';
import 'abstract_site_repository.dart';



class SiteOneFirestoreRepository implements ISiteFirestoreRepository{

  SiteOneFirestoreRepository();

  final dbInstance = FirestoreService.instance;

  final DatabaseReference databaseReference = FirebaseDatabase.instance.ref();


  Stream<DatabaseEvent> readSite(String siteId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(siteId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  @override
  Future<SiteNameModel> getNames() async {
    final ref = databaseReference.child("siteNames");
    final  snapshot = ((await ref.once()).snapshot.value as Map) ;
    final siteName =  SiteNameModel(
        site1: snapshot["site1"],
        site2: snapshot["site2"],
        site3: snapshot["site3"],
        site4: snapshot["site4"],
        site5: snapshot["site5"],
        site6: snapshot["site6"],
        site7: snapshot["site7"],
        site8: snapshot["site8"]);
    return siteName;

  }


  @override
  Future<List<SiteFireStoreModel?>> getSiteHistoryData({required String siteId,DateTime? date, DateTime? startAfterDate}) async{
    final finalDate = date ?? DateTime.now();
    if (startAfterDate!=null) {
      final schemaModel = await getSchemaModel(siteId,startAfterDate);
      final beforeDay = DateTime(finalDate.year, finalDate.month, finalDate.day-1,23,59,59);
      final afterDay = DateTime(finalDate.year, finalDate.month, finalDate.day+1,0,0,0);
      return dbInstance.collectionFuture(
          path: siteId ?? FirestorePath.schemaOne,
          limit: 15,
          queryBuilder: (query) {
            return query
                .orderBy('timestamp', descending: true)
                .where('timestamp', isGreaterThan: beforeDay)
                .where('timestamp', isLessThan: afterDay)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            return SiteFireStoreModel.fromJson(data..addAll({'id': docId}));
          });
    } else {
      final beforeDay = DateTime(finalDate.year, finalDate.month, finalDate.day-2,23,59,59);
      final afterDay = DateTime(finalDate.year, finalDate.month, finalDate.day+1,0,0,0);
      try{
        final model = await dbInstance.collectionFuture(
            path: siteId,
            limit: 15,
            queryBuilder: (query) {
              return query
                  .orderBy('timestamp', descending: true)
                  .where('timestamp', isGreaterThan: beforeDay)
                  .where('timestamp', isLessThan: afterDay);
            },
            builder: (data, docId) {
              return SiteFireStoreModel.fromJson(data..addAll({'id': docId}));
            });
        return model;
      }catch(e){
        rethrow;
      }
    }
  }


  Future<DocumentSnapshot?> getSchemaModel(
      String siteId,
      DateTime dateTime,
      ) async {
    final CollectionReference reference = FirebaseFirestore.instance
        .collection(siteId);
      final snapshot = await reference.limit(1).where("timestamp", isEqualTo: dateTime).get().then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  @override
  Future<int> getLength({ required String siteId,DateTime? dateTime}) async  {
    final day = dateTime ?? DateTime.now();
    final  collectionRef = FirebaseFirestore.instance.collection(siteId).where('timestamp', isGreaterThan: DateTime(day.year, day.month, day.day-1,23,59,59))
        .where('timestamp', isLessThan: DateTime(day.year, day.month, day.day+1,0,0,0));
    final  snapshot = await collectionRef.count().get();
    return snapshot.count ?? 0;
  }

  @override
  Future<List<SiteFireStoreModel?>> getChartData({required String siteId,DateTime? date}) async {
    final finalDate = date ?? DateTime.now();
      final beforeDay = DateTime(finalDate.year, finalDate.month, finalDate.day-1,23,59,59);
      final afterDay = DateTime(finalDate.year, finalDate.month, finalDate.day+1,0,0,0);
      try{
        final response =  dbInstance.collectionFuture(
            path: siteId,
            queryBuilder: (query) {
              return query
                  .orderBy('timestamp', descending: false)
                  .where('timestamp', isGreaterThan: beforeDay)
                  .where('timestamp', isLessThan: afterDay);
            },
            builder: (data, docId) {
              return SiteFireStoreModel.fromJson(data..addAll({'id': docId}));
            });
        return response;
      }catch(e){
        return [];
      }

  }

  @override
  Future<UserModel> getUsers(String userId) {
    // TODO: implement getUsers
    throw UnimplementedError();
  }


}