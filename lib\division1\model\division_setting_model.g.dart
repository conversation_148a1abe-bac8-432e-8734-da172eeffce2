// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'division_setting_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DivisionSettingModelImpl _$$DivisionSettingModelImplFromJson(
        Map<String, dynamic> json) =>
    _$DivisionSettingModelImpl(
      motor_number: (json['motor_number'] as num?)?.toInt() ?? 0,
      tank_number: (json['tank_number'] as num?)?.toInt() ?? 0,
      name: json['name'] as String? ?? "",
      password: json['password'] as String? ?? "",
      id: json['id'] as String? ?? "",
      tank10: json['tank10'] == null
          ? null
          : TankSettingModel.fromJson(json['tank10'] as Map<String, dynamic>),
      bw10: json['bw10'] == null
          ? null
          : BorewellSettingModel.fromJson(json['bw10'] as Map<String, dynamic>),
      tank12: json['tank12'] == null
          ? null
          : TankSettingModel.fromJson(json['tank12'] as Map<String, dynamic>),
      tank13: json['tank13'] == null
          ? null
          : TankSettingModel.fromJson(json['tank13'] as Map<String, dynamic>),
      tank14: json['tank14'] == null
          ? null
          : TankSettingModel.fromJson(json['tank14'] as Map<String, dynamic>),
      sensor10: json['sensor10'] == null
          ? null
          : SensorSettingModel.fromJson(
              json['sensor10'] as Map<String, dynamic>),
      sensor20: json['sensor20'] == null
          ? null
          : SensorSettingModel.fromJson(
              json['sensor20'] as Map<String, dynamic>),
      sensor21: json['sensor21'] == null
          ? null
          : SensorSettingModel.fromJson(
              json['sensor21'] as Map<String, dynamic>),
      sensor11: json['sensor11'] == null
          ? null
          : SensorSettingModel.fromJson(
              json['sensor11'] as Map<String, dynamic>),
      sensor32: json['sensor32'] == null
          ? null
          : SensorSettingModel.fromJson(
              json['sensor32'] as Map<String, dynamic>),
      tank_id: (json['tank_id'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      motor_id: (json['motor_id'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      bw_id: (json['bw_id'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      filter_id: (json['filter_id'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      valve_id: (json['valve_id'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      tank11: json['tank11'] == null
          ? null
          : TankSettingModel.fromJson(json['tank11'] as Map<String, dynamic>),
      motor10: json['motor10'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor10'] as Map<String, dynamic>),
      motor11: json['motor11'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor11'] as Map<String, dynamic>),
      motor12: json['motor12'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor12'] as Map<String, dynamic>),
      motor13: json['motor13'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor13'] as Map<String, dynamic>),
      motor14: json['motor14'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor14'] as Map<String, dynamic>),
      valve10: json['valve10'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve10'] as Map<String, dynamic>),
      valve11: json['valve11'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve11'] as Map<String, dynamic>),
      valve12: json['valve12'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve12'] as Map<String, dynamic>),
      valve13: json['valve13'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve13'] as Map<String, dynamic>),
      valve14: json['valve14'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve14'] as Map<String, dynamic>),
      valve20: json['valve20'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve20'] as Map<String, dynamic>),
      valve21: json['valve21'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve21'] as Map<String, dynamic>),
      valve22: json['valve22'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve22'] as Map<String, dynamic>),
      valve30: json['valve30'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve30'] as Map<String, dynamic>),
      valve31: json['valve31'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve31'] as Map<String, dynamic>),
      valve32: json['valve32'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve32'] as Map<String, dynamic>),
      valve33: json['valve33'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve33'] as Map<String, dynamic>),
      valve40: json['valve40'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve40'] as Map<String, dynamic>),
      valve41: json['valve41'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve41'] as Map<String, dynamic>),
      valve42: json['valve42'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve42'] as Map<String, dynamic>),
      valve43: json['valve43'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve43'] as Map<String, dynamic>),
      valve44: json['valve44'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve44'] as Map<String, dynamic>),
      valve50: json['valve50'] == null
          ? null
          : ValveSettingModel.fromJson(json['valve50'] as Map<String, dynamic>),
      filter10: json['filter10'] == null
          ? null
          : MotorSettingModel.fromJson(
              json['filter10'] as Map<String, dynamic>),
      filter11: json['filter11'] == null
          ? null
          : MotorSettingModel.fromJson(
              json['filter11'] as Map<String, dynamic>),
      filter20: json['filter20'] == null
          ? null
          : MotorSettingModel.fromJson(
              json['filter20'] as Map<String, dynamic>),
      filter30: json['filter30'] == null
          ? null
          : MotorSettingModel.fromJson(
              json['filter30'] as Map<String, dynamic>),
      tank20: json['tank20'] == null
          ? null
          : TankSettingModel.fromJson(json['tank20'] as Map<String, dynamic>),
      tank21: json['tank21'] == null
          ? null
          : TankSettingModel.fromJson(json['tank21'] as Map<String, dynamic>),
      tank22: json['tank22'] == null
          ? null
          : TankSettingModel.fromJson(json['tank22'] as Map<String, dynamic>),
      valveNumber: (json['valveNumber'] as num?)?.toInt(),
      has_valve: json['has_valve'] as bool?,
      motor20: json['motor20'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor20'] as Map<String, dynamic>),
      motor21: json['motor21'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor21'] as Map<String, dynamic>),
      motor22: json['motor22'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor22'] as Map<String, dynamic>),
      tank30: json['tank30'] == null
          ? null
          : TankSettingModel.fromJson(json['tank30'] as Map<String, dynamic>),
      tank31: json['tank31'] == null
          ? null
          : TankSettingModel.fromJson(json['tank31'] as Map<String, dynamic>),
      tank32: json['tank32'] == null
          ? null
          : TankSettingModel.fromJson(json['tank32'] as Map<String, dynamic>),
      tank40: json['tank40'] == null
          ? null
          : TankSettingModel.fromJson(json['tank40'] as Map<String, dynamic>),
      tank41: json['tank41'] == null
          ? null
          : TankSettingModel.fromJson(json['tank41'] as Map<String, dynamic>),
      tank50: json['tank50'] == null
          ? null
          : TankSettingModel.fromJson(json['tank50'] as Map<String, dynamic>),
      tank51: json['tank51'] == null
          ? null
          : TankSettingModel.fromJson(json['tank51'] as Map<String, dynamic>),
      motor30: json['motor30'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor30'] as Map<String, dynamic>),
      motor31: json['motor31'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor31'] as Map<String, dynamic>),
      motor40: json['motor40'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor40'] as Map<String, dynamic>),
      motor41: json['motor41'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor41'] as Map<String, dynamic>),
      motor50: json['motor50'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor50'] as Map<String, dynamic>),
      motor51: json['motor51'] == null
          ? null
          : MotorSettingModel.fromJson(json['motor51'] as Map<String, dynamic>),
      location: json['location'] == null
          ? null
          : LocationModel.fromJson(json['location'] as Map<String, dynamic>),
      installed_at: json['installed_at'] as String?,
      bom: json['bom'] as String?,
      is_customized: json['is_customized'] as bool?,
    );

Map<String, dynamic> _$$DivisionSettingModelImplToJson(
        _$DivisionSettingModelImpl instance) =>
    <String, dynamic>{
      'motor_number': instance.motor_number,
      'tank_number': instance.tank_number,
      'name': instance.name,
      'password': instance.password,
      'id': instance.id,
      'tank10': instance.tank10,
      'bw10': instance.bw10,
      'tank12': instance.tank12,
      'tank13': instance.tank13,
      'tank14': instance.tank14,
      'sensor10': instance.sensor10,
      'sensor20': instance.sensor20,
      'sensor21': instance.sensor21,
      'sensor11': instance.sensor11,
      'sensor32': instance.sensor32,
      'tank_id': instance.tank_id,
      'motor_id': instance.motor_id,
      'bw_id': instance.bw_id,
      'filter_id': instance.filter_id,
      'valve_id': instance.valve_id,
      'tank11': instance.tank11,
      'motor10': instance.motor10,
      'motor11': instance.motor11,
      'motor12': instance.motor12,
      'motor13': instance.motor13,
      'motor14': instance.motor14,
      'valve10': instance.valve10,
      'valve11': instance.valve11,
      'valve12': instance.valve12,
      'valve13': instance.valve13,
      'valve14': instance.valve14,
      'valve20': instance.valve20,
      'valve21': instance.valve21,
      'valve22': instance.valve22,
      'valve30': instance.valve30,
      'valve31': instance.valve31,
      'valve32': instance.valve32,
      'valve33': instance.valve33,
      'valve40': instance.valve40,
      'valve41': instance.valve41,
      'valve42': instance.valve42,
      'valve43': instance.valve43,
      'valve44': instance.valve44,
      'valve50': instance.valve50,
      'filter10': instance.filter10,
      'filter11': instance.filter11,
      'filter20': instance.filter20,
      'filter30': instance.filter30,
      'tank20': instance.tank20,
      'tank21': instance.tank21,
      'tank22': instance.tank22,
      'valveNumber': instance.valveNumber,
      'has_valve': instance.has_valve,
      'motor20': instance.motor20,
      'motor21': instance.motor21,
      'motor22': instance.motor22,
      'tank30': instance.tank30,
      'tank31': instance.tank31,
      'tank32': instance.tank32,
      'tank40': instance.tank40,
      'tank41': instance.tank41,
      'tank50': instance.tank50,
      'tank51': instance.tank51,
      'motor30': instance.motor30,
      'motor31': instance.motor31,
      'motor40': instance.motor40,
      'motor41': instance.motor41,
      'motor50': instance.motor50,
      'motor51': instance.motor51,
      'location': instance.location,
      'installed_at': instance.installed_at,
      'bom': instance.bom,
      'is_customized': instance.is_customized,
    };

_$LocationModelImpl _$$LocationModelImplFromJson(Map<String, dynamic> json) =>
    _$LocationModelImpl(
      latitude: (json['latitude'] as num?)?.toDouble() ?? 27.325,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 85.045,
    );

Map<String, dynamic> _$$LocationModelImplToJson(_$LocationModelImpl instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
