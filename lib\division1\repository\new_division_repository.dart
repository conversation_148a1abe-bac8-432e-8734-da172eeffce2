import 'dart:collection';
import 'dart:developer';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';

import '../../services/firestore_path.dart';
import '../../services/firestore_services.dart';
import '../../utils/format.dart';
import '../model/level_history_model.dart';
import '../model/motor_setting_model.dart';
import '../model/tank_setting_model.dart';

abstract class INewDivisionRepository {
  Future<DivisionSettingModel?> getSiteId(String password);

  Future<List<DivisionSettingModel?>> getSiteList();

  Stream<UserModel?> getUserProfile(String userId);

  Future<TankSettingModel?> getTankSetting(String tankId);

  Stream<TankSettingModel?> getStreamTankSetting(String tankId);

  Future<MotorSettingModel?> getMotorSetting(String motorId);

  Future<bool?> addNewSite(String siteId, String siteName, List<int> tankId,
      List<int> motorId, List<int> borewellId, List<int> valveId);

  Future<bool?> addMotorHistory(String siteId, String motorName, int motorId,
      String userName, String remark);

  Future<bool?> addValveHistory(String siteId, String motorName, int motorId,
      String userName, String remark);

  Future<List<LogsModel?>> getSiteLogs(
      {required String siteId,
      required String id,
      required int type,
      DateTime? date,
      DateTime? startAfterDate});

  Future<int> getLogsLength(
      {required String siteId,
      required String id,
      required int type,
      DateTime dateTime});

  Future<DivisionSettingModel?> getSiteSetting(String siteId);

  Future<void> updateScheduleSetting(String siteId, String motorId, String hrs,
      String mins, DivisionSettingModel setting);

  Future<void> updateNewScheduleSetting(String siteId, String id, String hrs,
      String mins, String days, DivisionSettingModel setting);

  Future<int> getLength(
      {required String siteId, required int tankId, int dateTime});

  Future<int> getFlowHistoryLength(
      {required String siteId, required int flowId, int dateTime});

  Future<List<FlowHistoryModel?>> getFlowHistory(
      {required String siteId,
      required int tankId,
      int? date,
      int? startAfterDate});

  Future<void> updateActiveStatus(String userId, bool isActive);

  Future<void> updateNotificationStatus(String userId, bool sendNotification);

  Future<void> updateInstallDateLocation(
      String siteId, String? installDate, LocationModel location);

  Future<void> setTankSetting(
      {required String siteId,
      required String tankId,
      required String tankName,
      required int actualHeight,
      required int maxHeight,
      required int minHeight,
      required int uploadTime,
      required int offset,
      required bool floatEnable,
      required bool isBw,
      required bool levelSensorEnable,
      required bool isActive});

  Future<void> setDivisionSetting(DivisionSettingModel divisionSettingModel);

  Future<void> updateTankActive(String tankId, bool active);

  Future<void> setMotorSetting({
    required String siteId,
    required String motorId,
    required String motorName,
    required double noLoadCurrent,
    required double overloadCurrent,
    required bool hasVoltage,
    required int mode,
    required int CF,
    required double currentThreshold,
    required double thAmps,
    required int uploadTime,
    String? hrs,
    String? mins,
  });

  Future<void> setValveSetting(
      {required String siteId,
      required String valveId,
      required String valveName,
      required int mode,
      String? hrs,
      String? mins,
      String? days});

  Future<List<TankHistoryModel?>> getSiteHistoryData(
      {required String siteId,
      required int tankId,
      int? date,
      int? startAfterDate});
}

class NewDivisionRepository implements INewDivisionRepository {
  final dbInstance = FirestoreService.instance;

  @override
  Future<DivisionSettingModel?> getSiteId(String password) async {
    final siteId = await dbInstance.collectionFuture<DivisionSettingModel>(
      path: FirestorePath.settingCollection,
      queryBuilder: (query) =>
          query.where('password', isEqualTo: password).limit(1),
      builder: (data, documentID) {
        return DivisionSettingModel.fromJson(data
          ..addAll({
            'id': documentID,
          }));
      },
    );
    return siteId.first;
  }

  @override
  Future<void> updateActiveStatus(String userId, bool isActive) async {
    try {
      if (await dbInstance.pathExistsFuture(
          path: FirestorePath.userDoc(userId))) {
        await dbInstance.updateDoc(
          path: FirestorePath.userDoc(userId),
          data: {
            'role': isActive ? 2 : 1,
          },
        );
      }
    } catch (e) {}
  }

  @override
  Stream<UserModel?> getUserProfile(String userId) =>
      dbInstance.documentStream<UserModel?>(
        path: FirestorePath.userDoc(userId),
        builder: (data, documentID) {
          // print("called getUserProfile");
          if (data == null) {
            return const UserModel();
          }

          return UserModel.fromJson(
            data
              ..addAll(
                {
                  'id': documentID,
                },
              ),
          );
        },
      );

  @override
  Future<MotorSettingModel?> getMotorSetting(String motorId) async {
    return await dbInstance.documentFuture<MotorSettingModel?>(
      path: FirestorePath.tankSettingDoc(motorId, ""),
      builder: (data, documentID) => data == null
          ? MotorSettingModel()
          : MotorSettingModel.fromJson(
              data
                ..addAll(
                  {
                    'id': documentID,
                  },
                ),
            ),
    );
  }

  @override
  Future<TankSettingModel?> getTankSetting(String tankId) async {
    return await dbInstance.documentFuture<TankSettingModel?>(
      path: FirestorePath.tankSettingDoc(tankId, ""),
      builder: (data, documentID) => data == null
          ? TankSettingModel()
          : TankSettingModel.fromJson(
              data
                ..addAll(
                  {
                    'id': documentID,
                  },
                ),
            ),
    );
  }

  @override
  Stream<TankSettingModel?> getStreamTankSetting(String tankId) {
    return dbInstance.documentStream<TankSettingModel?>(
      path: FirestorePath.tankSettingDoc(tankId, ""),
      builder: (data, documentID) => data == null
          ? TankSettingModel()
          : TankSettingModel.fromJson(
              data
                ..addAll(
                  {
                    'id': documentID,
                  },
                ),
            ),
    );
  }

  @override
  Future<void> updateTankActive(String tankId, bool active) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.tankSettingDoc(tankId, ""),
        data: {
          'is_active': active,
        },
      );
    } catch (e) {}
  }

  @override
  Future<DivisionSettingModel?> getSiteSetting(String siteId) async {
    return await dbInstance.documentFuture<DivisionSettingModel?>(
      path: FirestorePath.tankSettingDoc(siteId, ""),
      builder: (data, documentID) => data == null
          ? DivisionSettingModel()
          : DivisionSettingModel.fromJson(
              data
                ..addAll(
                  {
                    'id': documentID,
                  },
                ),
            ),
    );
  }

  @override
  Future<List<DivisionSettingModel?>> getSiteList() {
    return dbInstance.collectionFuture<DivisionSettingModel>(
      path: FirestorePath.settingCollection,
      queryBuilder: (query) => query.where("is_active", isEqualTo: true),
      builder: (data, documentID) {
        return DivisionSettingModel.fromJson(data
          ..addAll({
            'id': documentID,
          }));
      },
    );
  }

  Future<DocumentSnapshot?> getSchemaModel(
    String siteId,
    int dateTime,
    int tankId,
  ) async {
    final CollectionReference reference =
        FirebaseFirestore.instance.collection(siteId);
    final snapshot = await reference
        .limit(1)
        .where("time", isEqualTo: dateTime)
        .where("tankId", isEqualTo: tankId)
        .get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  Future<DocumentSnapshot?> getFlowHistoryModel(
    String siteId,
    int dateTime,
    int tankId,
  ) async {
    final CollectionReference reference =
        FirebaseFirestore.instance.collection(siteId);
    final snapshot = await reference
        .limit(1)
        .where("time", isEqualTo: dateTime)
        .get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  @override
  Future<List<TankHistoryModel?>> getSiteHistoryData(
      {required String siteId,
      required int tankId,
      int? date,
      int? startAfterDate}) async {
    final finalDate = DateTime.fromMillisecondsSinceEpoch(
            date ?? DateTime.now().millisecondsSinceEpoch) ??
        DateTime.now();
    if (startAfterDate != null) {
      log(startAfterDate.toString());

      final schemaModel = await getSchemaModel(siteId, startAfterDate, tankId);
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay =
          DateTime(finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      log(beforeDay.toString());
      log(afterDay.toString());
      log(schemaModel.toString());
      return dbInstance.collectionFuture(
          path: siteId ?? FirestorePath.schemaOne,
          limit: 15,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time',
                    isGreaterThan:
                        (beforeDay.millisecondsSinceEpoch / 1000).ceil())
                .where('time',
                    isLessThan: (afterDay.millisecondsSinceEpoch / 1000).ceil())
                .where('tankId', isEqualTo: tankId)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            log(data.toString());
            return TankHistoryModel.fromJson(data..addAll({'id': docId}));
          });
    } else {
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay =
          DateTime(finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      try {
        final model = await dbInstance.collectionFuture(
            path: siteId,
            limit: 15,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time',
                      isGreaterThan:
                          (beforeDay.millisecondsSinceEpoch / 1000).ceil())
                  .where('time',
                      isLessThan:
                          (afterDay.millisecondsSinceEpoch / 1000).ceil())
                  .where('tankId', isEqualTo: tankId);
            },
            builder: (data, docId) {
              return TankHistoryModel.fromJson(data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        log(e.toString());
        rethrow;
      }
    }
  }

  @override
  Future<int> getLength(
      {required String siteId, required int tankId, int? dateTime}) async {
    final day = DateTime.fromMillisecondsSinceEpoch(
        dateTime ?? DateTime.now().millisecondsSinceEpoch);
    final collectionRef = FirebaseFirestore.instance
        .collection(siteId)
        .orderBy('time', descending: true)
        .where('time',
            isGreaterThan:
                DateTime(day.year, day.month, day.day - 1, 23, 59, 59)
                        .millisecondsSinceEpoch /
                    1000)
        .where('time',
            isLessThan: DateTime(day.year, day.month, day.day + 1, 0, 0, 0)
                    .millisecondsSinceEpoch /
                1000)
        .where('tankId', isEqualTo: tankId);
    final snapshot = await collectionRef.count().get();
    return snapshot.count ?? 0;
  }

  @override
  Future<void> setDivisionSetting(
      DivisionSettingModel divisionSettingModel) async {
    try {
      await dbInstance.updateDoc(
          path: FirestorePath.tankSettingDoc(divisionSettingModel.id, ""),
          data: {
            'name': divisionSettingModel.name,
            'tank1': {
              'name': divisionSettingModel.tank10!.name,
              'actual_height': divisionSettingModel.tank10!.actual_height,
              'float_enable': divisionSettingModel.tank10!.float_enable,
              'level_sensor_enable':
                  divisionSettingModel.tank10!.level_sensor_enable,
              'max_height': divisionSettingModel.tank10!.max_height,
              'min_height': divisionSettingModel.tank10!.min_height,
              'is_active': divisionSettingModel.tank10!.is_active,
            },
            'motor1': {
              'name': divisionSettingModel.motor10!.name,
              'no_load_current': divisionSettingModel.motor10!.min_current,
              'overload_current': divisionSettingModel.motor10!.max_current,
              'power': divisionSettingModel.motor10!.power,
            },
          });
    } catch (e) {
      print(e);
    }
  }

  @override
  Future<int> getFlowHistoryLength(
      {required String siteId, required int flowId, int? dateTime}) async {
    try {
      final day = DateTime.fromMillisecondsSinceEpoch(
          dateTime ?? DateTime.now().millisecondsSinceEpoch);

      final collectionRef = FirebaseFirestore.instance
          .collection(siteId)
          .orderBy('time', descending: true)
          .where('time',
              isGreaterThan: DateTime(
                  getBeforeYear(day.year, day.month),
                  getMonth(day.month - 1),
                  getDaysInMonth(day.year, day.month - 1),
                  11,
                  59,
                  59))
          .where('time',
              isLessThan: DateTime(getAfterYear(day.year, day.month),
                  getMonth(day.month + 1), 1, 0, 0, 0));
      final snapshot = await collectionRef.count().get();
      return snapshot.count ?? 0;
    } catch (e) {
      log(e.toString());
      return 0;
    }
  }

  int getDaysInMonth(int year, int month) {
    try {
      if (month == DateTime.february) {
        final bool isLeapYear =
            (year % 4 == 0) && (year % 100 != 0) || (year % 400 == 0);
        return isLeapYear ? 29 : 28;
      }
      const List<int> daysInMonth = <int>[
        31,
        -1,
        31,
        30,
        31,
        30,
        31,
        31,
        30,
        31,
        30,
        31
      ];
      if (month == 0) {
        return daysInMonth[11];
      } else if (month == 13) {
        return daysInMonth[0];
      } else {
        return daysInMonth[month - 1];
      }
    } catch (e) {
      print(e);
      return month;
    }
  }

  int getBeforeYear(int year, int month) {
    if (month <= 1) {
      return year - 1;
    } else {
      return year;
    }
  }

  int getAfterYear(int year, int month) {
    if (month >= 12) {
      return year + 1;
    } else {
      return year;
    }
  }

  int getMonth(int month) {
    if (month > 12) {
      return 1;
    } else if (month < 1) {
      return 12;
    } else {
      return month;
    }
  }

  @override
  Future<List<FlowHistoryModel?>> getFlowHistory(
      {required String siteId,
      required int tankId,
      int? date,
      int? startAfterDate}) async {
    final finalDate = DateTime.fromMillisecondsSinceEpoch(
        date ?? DateTime.now().millisecondsSinceEpoch);
    if (startAfterDate != null) {
      final schemaModel =
          await getFlowHistoryModel(siteId, startAfterDate, tankId);
      final beforeDay = DateTime(
          getBeforeYear(finalDate.year, finalDate.month),
          getMonth(finalDate.month - 1),
          getDaysInMonth(finalDate.year, finalDate.month - 1),
          11,
          59,
          59);
      final afterDay = DateTime(getAfterYear(finalDate.year, finalDate.month),
          getMonth(finalDate.month + 1), 1, 0, 0, 0);
      return dbInstance.collectionFuture(
          path: siteId ?? FirestorePath.schemaOne,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time', isGreaterThan: beforeDay)
                .where('time', isLessThan: afterDay)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            return FlowHistoryModel.fromJson(data..addAll({'id': docId}));
          });
    } else {
      try {
        final beforeDay = DateTime(
            getBeforeYear(finalDate.year, finalDate.month),
            getMonth(finalDate.month - 1),
            getDaysInMonth(finalDate.year, finalDate.month - 1),
            11,
            59,
            59);
        final afterDay = DateTime(getAfterYear(finalDate.year, finalDate.month),
            getMonth(finalDate.month + 1), 1, 0, 0, 0);
        final model = await dbInstance.collectionFuture(
            path: siteId,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time', isGreaterThan: beforeDay)
                  .where('time', isLessThan: afterDay);
            },
            builder: (data, docId) {
              return FlowHistoryModel.fromJson(data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        rethrow;
      }
    }
  }

  String generatePassword() {
    final random = math.Random();
    const length = 8;
    const characters =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz#%^&*(!@}{_+?><L:[]`!';
    return String.fromCharCodes(Iterable.generate(
      length,
      (_) => characters.codeUnitAt(random.nextInt(characters.length)),
    ));
  }

  @override
  Future<bool?> addNewSite(String siteId, String name, List<int> tankId,
      List<int> motorId, List<int> borewellId, List<int> valveIds) async {
    try {
      await dbInstance
          .setData(path: FirestorePath.siteDoc(siteId.toString()), data: {
        'name': name,
        'password': generatePassword(),
        'is_active': true,
        'tank_id': tankId,
        'motor_id': motorId,
        'borewell_id': borewellId,
        'valve_id': valveIds,
        'installed_at': Format.date(DateTime.now().toLocal())
      });
      for (var id in tankId) {
        await dbInstance.setData(path: FirestorePath.siteDoc(siteId), data: {
          'tank' + id.toString(): {
            'name': 'Tank ' + id.toString(),
            'actual_height': 350,
            'max_height': 300,
            'min_height': 100,
            'is_active': true,
            'upload_time': 15
          }
        });
      }
      for (var id in borewellId) {
        await dbInstance.setData(path: FirestorePath.siteDoc(siteId), data: {
          'bw' + id.toString(): {
            'name': 'Borewell ' + id.toString(),
            'height': 90,
            'is_active': true,
            'upload_time': 15
          }
        });
      }
      for (var id in valveIds) {
        await dbInstance.setData(path: FirestorePath.siteDoc(siteId), data: {
          'valve' + id.toString(): {
            'name': 'Valve ' + id.toString(),
            'height': 90,
            'is_active': true,
            'upload_time': 15,
            'mode': 1,
            'hrs': '',
            'mins': '',
          }
        });
      }
      for (var id in motorId) {
        await dbInstance.updateDoc(path: FirestorePath.siteDoc(siteId), data: {
          'motor' + id.toString(): {
            'name': 'Motor ' + id.toString(),
            'min_current': 0.01,
            'max_current': 0.01,
            'CF': 0,
            'mode': 1,
            'hrs': '',
            'mins': '',
            'has_voltage': true,
            'is_active': true,
            'upload_time': 15
          }
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setMotorSetting(
      {required String siteId,
      required String motorId,
      required String motorName,
      required double noLoadCurrent,
      required double overloadCurrent,
      required bool hasVoltage,
      required int mode,
      required int CF,
      required double currentThreshold,
      required double thAmps,
      required int uploadTime,
      String? hrs,
      String? mins}) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.motorSettingDoc(siteId),
        data: {
          motorId: {
            'name': motorName,
            'upload_time': uploadTime,
            'current_threshold': currentThreshold,
            'has_voltage': hasVoltage,
            'mode': mode,
            'hrs': hrs,
            'mins': mins,
            'TH_Amps': thAmps,
            'min_current': noLoadCurrent,
            'max_current': overloadCurrent,
            'CF': CF,
            'is_active': true,
          }
        },
      );
    } catch (e) {}
  }

  @override
  Future<void> setTankSetting(
      {required String siteId,
      required String tankId,
      required String tankName,
      required int actualHeight,
      required int maxHeight,
      required int minHeight,
      required int uploadTime,
      required int offset,
      required bool floatEnable,
      required bool isBw,
      required bool levelSensorEnable,
      required bool isActive}) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.tankSettingDoc(siteId, tankId),
        data: {
          tankId: {
            'name': tankName,
            'actual_height': actualHeight,
            'float_enable': floatEnable,
            'max_height': maxHeight,
            'upload_time': uploadTime,
            'offset': offset,
            'min_height': minHeight,
            'is_bw': isBw,
            'is_active': isActive,
          }
        },
      );
    } catch (e) {}
  }

  @override
  Future<void> updateInstallDateLocation(
      String siteId, String? installDate, LocationModel location) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.siteDoc(siteId),
        data: {
          'installed_at': installDate,
          'location': {
            'latitude': location.latitude,
            'longitude': location.longitude
          }
        },
      );
    } catch (e) {
      log(e.toString());
    }
  }

  @override
  Future<bool?> addMotorHistory(String siteId, String motorName, int motorId,
      String userName, String remark) async {
    try {
      await dbInstance.addDoc(path: FirestorePath.logsCollection, data: {
        'name': motorName,
        'type': 2,
        'device_id': motorId,
        'remark': remark,
        'time': DateTime.now(),
        'siteId': siteId
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<int> getLogsLength(
      {required String siteId,
      required String id,
      required int type,
      DateTime? dateTime}) async {
    final day = dateTime ?? DateTime.now();
    final collectionRef = FirebaseFirestore.instance
        .collection("logs")
        .orderBy('time', descending: true)
        .where('time',
            isGreaterThan:
                DateTime(day.year, day.month, day.day - 1, 23, 59, 59))
        .where('time',
            isLessThan: DateTime(day.year, day.month, day.day + 1, 0, 0, 0))
        .where('siteId', isEqualTo: siteId);
    final snapshot = await collectionRef.count().get();
    return snapshot.count ?? 0;
  }

  Future<DocumentSnapshot?> getLogModel(
      String siteId, DateTime dateTime, String id, int type) async {
    final CollectionReference reference =
        FirebaseFirestore.instance.collection("logs");
    final snapshot = await reference
        .limit(1)
        .where("time", isEqualTo: dateTime)
        .where('siteId', isEqualTo: siteId)
        .get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  @override
  Future<List<LogsModel?>> getSiteLogs(
      {required String siteId,
      required String id,
      required int type,
      DateTime? date,
      DateTime? startAfterDate}) async {
    final finalDate = date ?? DateTime.now();
    if (startAfterDate != null) {
      final schemaModel = await getLogModel(siteId, startAfterDate, id, type);
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay =
          DateTime(finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      return dbInstance.collectionFuture(
          path: "logs" ?? FirestorePath.schemaOne,
          limit: 15,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time', isGreaterThan: beforeDay)
                .where('time', isLessThan: afterDay)
                .where('siteId', isEqualTo: siteId)
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            log(data.toString());
            return LogsModel.fromJson(data..addAll({'id': docId}));
          });
    } else {
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay =
          DateTime(finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      try {
        final model = await dbInstance.collectionFuture(
            path: "logs",
            limit: 15,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time', isGreaterThan: beforeDay)
                  .where('time', isLessThan: afterDay)
                  .where('siteId', isEqualTo: siteId);
            },
            builder: (data, docId) {
              return LogsModel.fromJson(data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        rethrow;
      }
    }
  }

  @override
  Future<void> updateScheduleSetting(String siteId, String motorId, String hrs,
      String mins, DivisionSettingModel setting) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.motorSettingDoc(siteId),
        data: {
          motorId: {
            'schedule': {"hrs": hrs, "mins": mins},
          }
        },
      );
    } catch (e) {}
  }

  @override
  Future<bool?> addValveHistory(String siteId, String motorName, int motorId,
      String userName, String remark) async {
    try {
      await dbInstance.addDoc(path: FirestorePath.logsCollection, data: {
        'name': motorName,
        'type': 4,
        'device_id': motorId,
        'remark': remark,
        'time': DateTime.now(),
        'siteId': siteId
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setValveSetting(
      {required String siteId,
      required String valveId,
      required String valveName,
      required int mode,
      hrs,
      String? mins,
      String? days}) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.motorSettingDoc(siteId),
        data: {
          valveId: {
            'name': valveName,
            'mode': mode,
            'hrs': hrs,
            'mins': mins,
            'days': days,
            'is_active': true,
          }
        },
      );
    } catch (e) {}
  }

  @override
  Future<void> updateNewScheduleSetting(String siteId, String id, String hrs,
      String mins, String days, DivisionSettingModel setting) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.scheduleDoc(siteId),
        data: {
          id: {
            'schedule': {"hrs": hrs, "mins": mins, "days": days},
          }
        },
      );
    } catch (e) {}
  }

  @override
  Future<void> updateNotificationStatus(String userId, bool sendNotification) async {
    try {
      if (await dbInstance.pathExistsFuture(
          path: FirestorePath.userDoc(userId))) {
        await dbInstance.updateDoc(
          path: FirestorePath.userDoc(userId),
          data: {
            'send_notification': sendNotification ? true : false,
          },
        );
      }
    } catch (e) {}
  }
}
