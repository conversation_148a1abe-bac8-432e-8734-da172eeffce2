

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class HorizontalPipeline extends HookConsumerWidget{

  HorizontalPipeline({
    this.length = 100,
    this.name =  "",
    this.isFlowing = true,
    this.isTop = true,
    this.isRight = true,
    this.isBottom = true,
    this.isLeft = true,
    this.leftFlow = false,
    this.singleFlow = true,

  });

  final double length;
  final String name;
  final bool isFlowing;
  final bool isLeft;
  final bool isRight;
  final bool isTop;
  final bool singleFlow;
  final bool isBottom;
  final bool leftFlow;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: length,
      height: 12,
      decoration:  BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF000000),      // Minimal black (Light Gray)
             Colors.white,           // White color
            Color(0xFF000000),      // Minimal black (Light Gray)
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        border: Border(
        top: isTop ? BorderSide(color: Colors.black, width: 2) : BorderSide.none,
        left: isLeft ? BorderSide(color: Colors.black, width: 2) :BorderSide.none,
        bottom: isBottom ? BorderSide(color: Colors.black, width: 2): BorderSide.none,
        right: isRight ?  BorderSide(color: Colors.black, width: 2) : BorderSide.none, // No border on the right
      ),
      ),
      child: Center(child:
      Row(
        mainAxisAlignment: singleFlow ? MainAxisAlignment.center : MainAxisAlignment.spaceEvenly,
        children: [
          if(isFlowing)
          RotatedBox(
            quarterTurns: leftFlow ? 0 : 2,
            child: LoadingAnimationWidget.prograssiveDots(
              color: Colors.lightBlueAccent,
              size: 30,
            ),
          ),
          Text(name,style: const TextStyle(color: Colors.black, fontSize: 8),),
          if(!singleFlow && isFlowing)
          RotatedBox(
            quarterTurns: leftFlow ? 0 : 2,
            child: LoadingAnimationWidget.prograssiveDots(
              color: Colors.lightBlueAccent,
              size: 30,
            ),
          ),
        ],
      )),

    );
  }

}


class VerticalPipeline extends HookConsumerWidget{

  VerticalPipeline({
    this.length = 100,
    this.name =  "",
    this.isFlowing = true,
    this.isTop = true,
    this.isRight = true,
    this.isBottom = true,
    this.isLeft = true,
    this.topFlow = false,
    this.singleFlow = true,
  });

  final double length;
  final String name;
  final bool isFlowing;
  final bool isLeft;
  final bool isRight;
  final bool isTop;
  final bool isBottom;
  final bool topFlow;
  final bool singleFlow;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: length,
      width: 12,
      decoration:  BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF000000),      // Minimal black (Light Gray)
            Colors.white,           // White color
            Color(0xFF000000),      // Minimal black (Light Gray)
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        border:  Border(
          top: isTop ? BorderSide(color: Colors.black, width: 2) : BorderSide.none,
          left: isLeft ? BorderSide(color: Colors.black, width: 1) :BorderSide.none,
          bottom: isBottom ? BorderSide(color: Colors.black, width: 2): BorderSide.none,
          right: isRight ?  BorderSide(color: Colors.black, width: 1) : BorderSide.none, // No borright
        ),
      ),
      child: RotatedBox(
        quarterTurns: topFlow ? 1 : 3,
        child: Center(child:
        Row(
          mainAxisAlignment: singleFlow ? MainAxisAlignment.center : MainAxisAlignment.spaceEvenly,
          children: [
            if(isFlowing)
            RotatedBox(
              quarterTurns: 0,
              child: LoadingAnimationWidget.prograssiveDots(
                color: Colors.lightBlueAccent,
                size: 30,
              ),
            ),
            Text(name,style: TextStyle(color: Colors.black, fontSize: 8),),
            if(!singleFlow && isFlowing)
              RotatedBox(
                quarterTurns: 0,
                child: LoadingAnimationWidget.prograssiveDots(
                  color: Colors.lightBlueAccent,
                  size: 30,
                ),
              ),
          ],
        )),
      ),

    );
  }

}