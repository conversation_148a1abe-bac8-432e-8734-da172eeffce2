

import 'package:freezed_annotation/freezed_annotation.dart';
part 'resource.freezed.dart';

@freezed
abstract class Resource<T> with _$Resource<T>{
  const factory Resource.success(T? data,String? message) = _ResourceData;
  const factory Resource.unInitialized() = _ResourceUnInitialized;
  const factory Resource.error(String? errorText) = _ResourceError;
  const factory Resource.loading() = _ResourceLoading;

}
