

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/level_history_model.dart';

import '../../services/firestore_path.dart';
import '../../services/firestore_services.dart';


class SensorHistoryRepository{

  final dbInstance = FirestoreService.instance;

  Future<DocumentSnapshot?> getSchemaModel(String siteId,
      DateTime dateTime,
      int tankId,) async {
    final CollectionReference reference = FirebaseFirestore.instance
        .collection("sensor");
    final snapshot = await reference.limit(1).where("time", isEqualTo: dateTime)
        .where('siteId', isEqualTo: int.parse(siteId)).get()
        .then((value) => value.docs.firstOrNull);
    return snapshot;
  }

  Future<int> getLength(
      {required String siteId, required int tankId, DateTime? dateTime}) async {
    final day = dateTime ?? DateTime.now();
    final collectionRef = FirebaseFirestore.instance.collection("sensor").orderBy(
        'time', descending: true).
    where('time',
        isGreaterThan: DateTime(day.year, day.month, day.day - 1, 23, 59, 59))
        .where(
        'time', isLessThan: DateTime(day.year, day.month, day.day + 1, 0, 0, 0))
        .where('siteId', isEqualTo: int.parse(siteId));
    final snapshot = await collectionRef.count().get();
    return snapshot.count ?? 0;
  }

  Future<List<SensorHistoryModel?>> getSensorHistoryData(
      {required String siteId, required int tankId, DateTime? date, DateTime? startAfterDate}) async {
    final finalDate = date ?? DateTime.now();
    if (startAfterDate != null) {
      final schemaModel = await getSchemaModel(siteId, startAfterDate, tankId);
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      return dbInstance.collectionFuture(
          path: "sensor" ?? FirestorePath.schemaOne,
          limit: 15,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: true)
                .where('time', isGreaterThan: beforeDay)
                .where('time', isLessThan: afterDay)
                .where('siteId', isEqualTo: int.parse(siteId))
                .startAfterDocument(schemaModel!);
          },
          builder: (data, docId) {
            return SensorHistoryModel.fromJson(
                data..addAll({'id': docId}));
          });
    } else {
      final beforeDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day - 1, 23, 59, 59);
      final afterDay = DateTime(
          finalDate.year, finalDate.month, finalDate.day + 1, 0, 0, 0);
      try {
        final model = await dbInstance.collectionFuture(
            path: "sensor",
            limit: 15,
            queryBuilder: (query) {
              return query
                  .orderBy('time', descending: true)
                  .where('time', isGreaterThan: beforeDay)
                  .where('time', isLessThan: afterDay)
                  .where('siteId', isEqualTo: int.parse(siteId))

              ;
            },
            builder: (data, docId) {
              return SensorHistoryModel.fromJson(
                  data..addAll({'id': docId}));
            });
        return model;
      } catch (e) {
        rethrow;
      }
    }
  }

}