import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/levelHistory/logs_data_source.dart';
import 'package:si/division1/model/level_history_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/firestore_path.dart';
import 'package:si/services/firestore_services.dart';

import '../../datatable/data_table_2.dart';
import '../../datatable/paginated_data_table_2.dart';
import '../../kawosoti/schema/widgets/date_forward_backward.dart';
import '../../utils/app_colors.dart';
import '../../utils/format.dart';

/// Route options are used to configure certain features of
/// the given example
String getCurrentRouteOption(BuildContext context) {
  var isEmpty = ModalRoute.of(context) != null &&
          ModalRoute.of(context)!.settings.arguments != null &&
          ModalRoute.of(context)!.settings.arguments is String
      ? ModalRoute.of(context)!.settings.arguments as String
      : '';

  return isEmpty;
}

// Route options
const dflt = 'Default';
const noData = 'No data';
const autoRows = 'Auto rows';
const showBordersWithZebraStripes = 'Borders with Zebra';
const custPager = 'Custom pager';
const defaultSorting = 'Default sorting';
const selectAllPage = 'Select all at page';
const rowTaps = 'Row Taps';
const rowHeightOverrides = 'Row height overrides';
const fixedColumnWidth = 'Fixed column width';

class LogsHistoryPage extends StatefulHookConsumerWidget {
  const LogsHistoryPage({Key? key, this.siteId = "site1"}) : super(key: key);

  final String siteId;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LogsHistoryState();
}

class _LogsHistoryState extends ConsumerState<LogsHistoryPage> {
  LogsHistorySiteDataSource? siteDataSource;
  final PaginatorController _controller = PaginatorController();
  final FirestoreService _firestoreService = FirestoreService.instance;
  String deviceId = "10";

  // Text controllers for editing
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();
  final TextEditingController _deviceIdController = TextEditingController();
  DateTime? _selectedDateTime;

  List<DataColumn> get _columns {
    return [
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Date',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Type',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Name',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Device Id',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text(
          'Remarks',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: const Text(
          'Actions',
          style: TextStyle(fontSize: 14, color: Colors.black),
        ),
      ),
    ];
  }

  @override
  void initState() {
    siteDataSource = LogsHistorySiteDataSource(
      userType: ref.read(userState).role ?? 1,
      onEdit: _handleEditLog,
      onDelete: _handleDeleteLog,
    );
    siteDataSource?.SiteId = widget.siteId;
    siteDataSource?.deviceId = "10";
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _remarkController.dispose();
    _deviceIdController.dispose();
    super.dispose();
  }

  // Method to handle editing a log
  void _handleEditLog(LogsModel log) {
    _nameController.text = log.name;
    _remarkController.text = log.remark;
    _deviceIdController.text = log.device_id.toString();
    _selectedDateTime = log.time;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Log'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Name'),
              ),
              TextField(
                controller: _deviceIdController,
                decoration: const InputDecoration(labelText: 'Device ID'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: _remarkController,
                decoration: const InputDecoration(labelText: 'Remark'),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              ListTile(
                title: Text(_selectedDateTime != null
                    ? Format.date(_selectedDateTime!)
                    : 'Select Date and Time'),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final DateTime? pickedDate = await showDatePicker(
                    context: context,
                    initialDate: _selectedDateTime ?? DateTime.now(),
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2030),
                  );

                  if (pickedDate != null && mounted) {
                    // Store the context before the async gap
                    final currentContext = context;
                    if (mounted) {
                      final TimeOfDay? pickedTime = await showTimePicker(
                        context: currentContext,
                        initialTime: TimeOfDay.fromDateTime(
                            _selectedDateTime ?? DateTime.now()),
                      );

                      if (pickedTime != null && mounted) {
                        setState(() {
                          _selectedDateTime = DateTime(
                            pickedDate.year,
                            pickedDate.month,
                            pickedDate.day,
                            pickedTime.hour,
                            pickedTime.minute,
                          );
                        });
                      }
                    }
                  }
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _updateLog(log);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Method to update a log in Firestore
  Future<void> _updateLog(LogsModel log) async {
    try {
      // Check if the log has a document ID
      if (log.id.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Error: Log ID not found')),
          );
        }
        return;
      }

      // Update the log using its document ID
      await _firestoreService.updateDoc(
        path: '${FirestorePath.logsCollection}/${log.id}',
        data: {
          'name': _nameController.text,
          'device_id': int.tryParse(_deviceIdController.text) ?? log.device_id,
          'remark': _remarkController.text,
          'time': _selectedDateTime,
        },
      );

      // Refresh the data
      setState(() {
        siteDataSource?.startDate = siteDataSource?.startSiteDate;
        _controller.goToFirstPage();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Log updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating log: $e')),
        );
      }
    }
  }

  // Method to handle deleting a log
  void _handleDeleteLog(LogsModel log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Log'),
        content: const Text('Are you sure you want to delete this log?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _deleteLog(log);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // Method to delete a log from Firestore
  Future<void> _deleteLog(LogsModel log) async {
    try {
      // Check if the log has a document ID
      if (log.id.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Error: Log ID not found')),
          );
        }
        return;
      }

      // Delete the log using its document ID
      await _firestoreService.deleteDoc(
        path: '${FirestorePath.logsCollection}/${log.id}',
      );

      // Refresh the data
      setState(() {
        siteDataSource?.startDate = siteDataSource?.startSiteDate;
        _controller.goToFirstPage();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Log deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting log: $e')),
        );
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    final List<int> idList =
        ref.read(localStorageProvider).getSettings().tank_id!;
    log(idList.toString());

    return BaseScaffold(
      showAppBar: false,
      appbar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: AppColors.themeColor,
        centerTitle: true,
        title: Text(
          "Logs",
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          /*PopupMenuButton<String>(
            onSelected: handleClick,
            itemBuilder: (BuildContext context) {
              return idList.map((int choice) {
                return PopupMenuItem<String>(
                  value: choice.toString(),
                  child: Text(choice.toString()),
                );
              }).toList();
            },
          )*/
        ],
      ),
      child: Column(
        children: [
          DateForwardBackward(onDateChange: (data) {
            siteDataSource?.startDate = data;
            _controller.goToFirstPage();
          }),
          SizedBox(
            height: 20,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                AsyncPaginatedDataTable2(
                    showCheckboxColumn: false,
                    horizontalMargin: 16,
                    columnSpacing: 0,
                    wrapInCard: false,
                    rowsPerPage: 15,
                    minWidth: 700,
                    availableRowsPerPage: [15],
                    autoRowsToHeight:
                        getCurrentRouteOption(context) == autoRows,
                    // Default - do nothing, autoRows - goToLast, other - goToFirst
                    pageSyncApproach: getCurrentRouteOption(context) == dflt
                        ? PageSyncApproach.doNothing
                        : getCurrentRouteOption(context) == autoRows
                            ? PageSyncApproach.goToLast
                            : PageSyncApproach.goToFirst,
                    fit: FlexFit.tight,
                    initialFirstRowIndex: 0,
                    onRowsPerPageChanged: (value) {
                      // No need to wrap into setState, it will be called inside the widget
                      // and trigger rebuild
                      //setState(() {
                      log('Row per page changed to $value');
                      //});
                    },
                    controller: _controller,
                    empty: Center(
                        child: Container(
                            padding: const EdgeInsets.all(20),
                            color: AppColors.themeColor,
                            child: const Text(
                              'No data',
                              style:
                                  TextStyle(fontSize: 18, color: Colors.white),
                            ))),
                    loading: Center(
                        child: CircularProgressIndicator(
                      color: Colors.blue,
                    )),
                    errorBuilder: (e) => Center(
                          child: SelectableText(e.toString()),
                        ),
                    columns: _columns,
                    source: siteDataSource!),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
