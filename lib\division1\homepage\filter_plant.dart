import 'dart:io';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/motor_model.dart';

import '../../constants/app_sizes.dart';
import '../provider/division_provider.dart';
import 'motor_moving.dart';

class FilterDisplayWidget extends HookConsumerWidget {
  FilterDisplayWidget(
      {this.filterName = 'Filter',
        this.filterId = 'filter1',
        this.showFilter = true,
        this.height = 170,
        this.roleType = 1});

  final String filterName;

  final String filterId;

  final bool showFilter ;

  final int roleType;

  final double height;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterState = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider(filterId), (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        filterState.value = MotorModel(
          output_status: datasnapshot['output_status'],
            device_status: datasnapshot['device_status'],mobile_status: datasnapshot['mobile_status']);
      }
      if(filterState.value.device_status==filterState.value.mobile_status){
        EasyLoading.dismiss();
      }
    });
    return Container(
      height: height,
      decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.all(Radius.circular(4)),
          border: Border.all(color: Colors.black)

      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [

            gapH8,
            Text(
              filterName ?? 'Filter',
              textAlign: TextAlign.center,
              maxLines: 2,
              style: TextStyle(color: (filterState.value.device_status ==1 || filterState.value.output_status == 1) ?  Colors.green :Colors.grey, fontSize: 14,),
            ),
            gapH8,
            if(showFilter)
            Image.asset("assets/icons/ic_filter.png",height: 60, width: 60,
            color:  (filterState.value.device_status ==1 || filterState.value.output_status == 1) ?  Colors.green :Colors.grey,),
            gapH8,
            Consumer(builder: (context, WidgetRef ref, child) {
              final userActive = ref
                  .watch(userProfileProvider)
                  .asData
                  ?.value
                  ?.role ??
                  5;
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: FlutterSwitch(
                      width: 70.0,
                      height: 30.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 20.0,
                      value: filterState.value.device_status == 1 && filterState.value.mobile_status == 1,
                      borderRadius: 20.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {
                        EasyLoading.show();
                        if (await hasNetwork()) {
                          if (userActive == 0) {
                            EasyLoading.showError(
                                'The user is inactive. Please contact concern admins for activating the user.');
                          } else if (userActive == 1 ) {
                            EasyLoading.showError(
                                'You do not have enough permission. Please contact concern admins for activating the user.');
                          } else if (userActive == 2) {
                            if (filterState.value.device_status != 1) {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .startFilter(filterId);
                            } else {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .stopFilter(filterId);
                            }
                          } else if (userActive == 3 || userActive == 4) {
                            if (filterState.value.device_status != 1) {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .startFilter(filterId);
                            } else {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .stopFilter(filterId);
                            }
                          }else{
                            EasyLoading.showError(
                                'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                          }
                        } else {
                          EasyLoading.showError(
                              'No internet Connection');
                        }
                      }),
                ),
              );
            }),
            gapH8,
          ],
        ),
      ),
    );

  }

  Future<bool> hasNetwork() async {
    try {
      final result = await InternetAddress.lookup('example.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
