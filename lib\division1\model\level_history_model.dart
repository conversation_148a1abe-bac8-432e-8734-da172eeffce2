import 'package:cloud_firestore/cloud_firestore.dart';

import '../../utils/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'level_history_model.freezed.dart';
part 'level_history_model.g.dart';

@freezed
class DivisionLevelHistorymodel with _$DivisionLevelHistorymodel {
  const factory DivisionLevelHistorymodel({
    @Default(2) int float,
    @Default(0) int level,
    @Default(2) int mD1,
    @Default(2) int mO1,
    @Default(2) int mD2,
    @Default(2) int mO2,
    @Default(0.0) double v1,
    @Default(0.0) double v2,
    @Default(0) int CF1,
    @Default(0) int CF2,
    @Default("") String id,
    @Default(1) int tankId,
    @TimestampNullableConverter() DateTime? time,
  }) = _DivisionLevelHistorymodel;

  factory DivisionLevelHistorymodel.fromJson(Map<String, dynamic> json) =>
      _$DivisionLevelHistorymodelFromJson(json);
}

@freezed
class TankHistoryModel with _$TankHistoryModel {
  const factory TankHistoryModel({
    @Default(2) int float,
    @Default(0) int level,
    @Default(1) int tankId,
    @Default(1) int rssi,
    int? time,
  }) = _TankHistoryModel;

  factory TankHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$TankHistoryModelFromJson(json);
}

@freezed
class BorewellHistoryModel with _$BorewellHistoryModel {
  const factory BorewellHistoryModel({
    @Default(1) int siteId,
    @Default(0.0) double level,
    @Default(1) int bwId,
    @Default(1) int rssi,
    int? time,
  }) = _BorewellHistoryModel;

  factory BorewellHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$BorewellHistoryModelFromJson(json);
}

@freezed
class LogsModel with _$LogsModel {
  const factory LogsModel({
    @Default(1) int device_id,
    @Default("") String remark,
    @Default("") String name,
    @Default(1) int type,
    @Default("") String siteId,
    @Default("") String id,
    @TimestampNullableConverter() DateTime? time,
  }) = _LogsModel;

  factory LogsModel.fromJson(Map<String, dynamic> json) =>
      _$LogsModelFromJson(json);
}

@freezed
class FlowHistoryModel with _$FlowHistoryModel {
  const factory FlowHistoryModel({
    @Default(0) int mins,
    @Default(0) int volume,
    @TimestampNullableConverter() DateTime? time,
  }) = _FlowHistoryModel;

  factory FlowHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$FlowHistoryModelFromJson(json);
}

@freezed
class SensorHistoryModel with _$SensorHistoryModel {
  const factory SensorHistoryModel({
    @Default(0.0) double t1,
    @Default(0.0) double t2,
    @Default(0.0) double t3,
    @Default(0.0) double t4,
    @Default(0.0) double cl,
    @Default(0.0) double ph,
    @Default(0.0) double flow,
    @Default(0.0) double cum_flow,
    int? time1,
    int? time2,
    int? time3,
    int? time4,
    @TimestampNullableConverter() DateTime? time,
  }) = _SensorHistoryModel;

  factory SensorHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$SensorHistoryModelFromJson(json);
}
