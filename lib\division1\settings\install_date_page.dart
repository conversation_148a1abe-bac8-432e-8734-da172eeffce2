

import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:image_picker/image_picker.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/settings/label_text.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:simple_chips_input/select_chips_input.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../utils/app_colors.dart';
import '../../utils/format.dart';
import '../homepage/widgets/date_widget.dart';


class InstallDatePage extends HookConsumerWidget {
  InstallDatePage( );

  final GlobalKey<FormState> _formAddKey = GlobalKey<FormState>();


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteIdController = useTextEditingController();
    final location = useState(LocationModel());
    final startDateController = useTextEditingController();
    final locationController = useTextEditingController();
    final startDate = useState<DateTime>(DateTime.now());
    final repo = ref.read(divisionRepositoryProvider);
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final setting = ref.read(localStorageProvider).getSettings();

    useEffect(() {
      // print('SessionDEt : ${sessionModel.id}');
      startDateController.text = setting.installed_at ?? "";
      log(startDateController.text);
      location.value = setting.location ?? LocationModel(latitude: 0.0, longitude: 0.0);
      locationController.text = "Lat: ${location.value.latitude}, Long: ${location.value.longitude}";


      return null;
    }, const []);


    void _getLocation() async {
      LocationService locationService = LocationService();
      try {
        Position position = await locationService.getCurrentLocation();
        location.value = LocationModel(latitude: position.latitude, longitude: position.longitude);
        locationController.text = "Lat: ${position.latitude}, Long: ${position.longitude}";
      } catch (e) {

      }
    }

    return BaseScaffold(
      showAppBar: true,
      appbarText: "Installed Information",
      child: Column(
        children: [
          DateWidget(
            startDateController,
            startDate.value,
                (date) {
              log("Date"+date.toString());
              startDate.value = date;
              Navigator.of(context).pop();
            },
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CustomTextFormField(
              name: "Location",
              hintText: "Location",
              controller: locationController,
              suffixIcon: Icon(Icons.location_on_outlined),
              textInputAction: TextInputAction.next,
              enabled: false,
            ),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Container(
                width: 160,
                height: 40,
                child: PrimaryButton(text: "Get Location",
                textSize: 14,
                onPressed: (){
                  _getLocation();
                },),
              ),
            ),
          ),
          Padding(
            padding: padding16,
            child: PrimaryButton(text: "Update",onPressed: () async {
              EasyLoading.show();
                final result = await repo.updateInstallDateLocation(prefs.getSiteId(),
                    Format.onlydate(startDate.value), location.value);
              final realSetting = await repo.getSiteSetting(prefs.getSiteId());
              await ref.read(localStorageProvider).setSettings(realSetting!);
                EasyLoading.dismiss();
                Navigator.of(context).pop();
            },),
          ),
          /*Container(
            padding: const EdgeInsets.only(top: 10, left: 16, right: 16),
            decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(Sizes.p32),
                    topLeft: Radius.circular(Sizes.p32))),
            child:InkWell(
              onTap: () async {
                try {
                  var pickedFileList =
                  await ImagePicker().pickImage(
                    source: ImageSource.gallery,
                    imageQuality: 70,
                  );
                  CroppedFile? croppedFile =
                  await ImageCropper().cropImage(
                    sourcePath: pickedFileList!.path,
                    aspectRatioPresets: [
                      CropAspectRatioPreset.square,
                      CropAspectRatioPreset.ratio3x2,
                      CropAspectRatioPreset.ratio4x3,
                      CropAspectRatioPreset.ratio16x9,
                    ],
                    uiSettings: [
                      AndroidUiSettings(
                          toolbarTitle: "Cropper",
                          toolbarColor: Colors.black,
                          toolbarWidgetColor: Colors.white,
                          initAspectRatio:
                          CropAspectRatioPreset.original,
                          lockAspectRatio: false),
                      IOSUiSettings(
                        title: "Cropper",
                      ),
                    ],
                  );

                 *//* if (createPostImages.value.isEmpty) {
                    final decodeImage = await decodeImageFromList(
                        File(croppedFile!.path).readAsBytesSync());
                    imageratio.value = double.parse(double.parse(
                        (decodeImage.width / decodeImage.height)
                            .toString())
                        .toStringAsFixed(2));
                  }
                  *//*

                  *//* for(final file in listFiles){
                            _createPostImages.value.add(CreatePostImageModel(imagePath: file.path,local: true,ratio: imageratio.value));
                          }*//*
                } catch (e) {
                 // Logger().e('error on image:${e.toString()}');
                }
              },
              child: Padding(
                padding: vertPadding8,
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: Sizes.p18,
                      backgroundColor: Colors.grey.withOpacity(0.3),
                      child: const Icon(
                        Icons.image,
                        color: Colors.black,
                        size: Sizes.p20,
                      ),
                    ),
                    gapW12,
                    Text(
                      "Upload Image",
                    )
                  ],
                ),
              ),
          )
          )*/
        ],
      ),

    );
  }

}


class LocationService {
  Future<Position> getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled, request user to enable it
      return Future.error('Location services are disabled.');
    }

    // Check for location permissions
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied, handle accordingly
        return Future.error('Location permissions are denied.');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle accordingly
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // Get the current location
    return await Geolocator.getCurrentPosition();
  }
}