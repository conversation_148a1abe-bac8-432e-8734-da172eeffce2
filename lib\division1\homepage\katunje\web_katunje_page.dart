

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../provider/auth_provider.dart';
import '../../model/motor_model.dart';
import '../widgets/distribution_widget.dart';
import '../widgets/motor_widget.dart';
import '../widgets/pipeline.dart';
import '../widgets/tank_widget.dart';

class WebKatunjePage extends HookConsumerWidget{
  const WebKatunjePage(this.setting, {super.key});

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appDimension = ref.read(katunjeDimensionProvider);
    final size = MediaQuery.of(context).size;
    final setting = ref.read(localStorageProvider).getSettings();

    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());
    final motor12State = useState<MotorModel>(MotorModel());
    final motor20State = useState<MotorModel>(MotorModel());
    final motor21State = useState<MotorModel>(MotorModel());
    final motor22State = useState<MotorModel>(MotorModel());
    final motor30State = useState<MotorModel>(MotorModel());
    final motor31State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor12State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor12State.value.device_status == motor12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status == motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("21"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor21State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor21State.value.device_status == motor21State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("22"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor22State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor22State.value.device_status == motor22State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("30"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor30State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor30State.value.device_status == motor30State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("31"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor31State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor31State.value.device_status == motor31State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    return Center(
      child: Container(
      height: 720,
      width: 1280,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white, width: 1),
      ),
      child:  Stack(
        children: [
          const Padding(
            padding: const EdgeInsets.only(left: 24.0, top : 8),
            child: Text("कटुन्जे घट्टेखोला (लिफ्ट) खानेपानी, \nसुर्यविनायक नगरपालिका ५ , कटुन्जे भक्तपुर",
                style:TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold)),
          ),
          const Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text("Swodeshi Scada System", style: TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold)),
            ),
          ),
          const Align(
            alignment: Alignment.bottomLeft,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text("Powered by:\n स्वदेशी इनोवेशन \n सम्पर्क : ९८०२३५७९४२",
                  style: TextStyle(color: Colors.black,fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,),
            ),
          ),
          /*Align(
            alignment: Alignment.topCenter,
            child: const Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: const ScrollableText(
                'कटुन्जे घट्टेखोला (लिफ्ट) खानेपानी, सुर्यविनायक नगरपालिका ५ , कटुन्जे भक्तपुर ---Swodeshi Scada System--- Powered by: स्वदेशी इनोवेशन, सम्पर्क नम्बर : ९८०२३५७९४२      ',
                velocity: Velocity(pixelsPerSecond: Offset(40, 0)),
                delayBefore: Duration(milliseconds: 500),
                pauseBetween: Duration(milliseconds: 50),
                fadedBorder: true,
                style: TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
                selectable: true,
              ),
            ),
          ),*/
          Positioned(
            right: 8,
            top: 8,
            child: Row(
              children: [
                gapW8,
                IconButton(
                  icon: Icon(Icons.logout_outlined),
                  onPressed: () async {
                    final bool didRequestSignOut =
                        await showAlertDialog(
                          context: context,
                          title: 'Logout',
                          content:
                          'Are you sure you want to logout?',
                          cancelActionText: 'Cancel',
                          defaultActionText: 'Yes',
                        ) ??
                            false;
                    if (didRequestSignOut == true) {
                      try {
                        EasyLoading.show(status: 'Logging Out');
                        await ref
                            .read(authRepositoryProvider)
                            .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                        await ref
                            .read(authRepositoryProvider)
                            .signOut();
                        // MyApp.of(context).authService.authenticated = true;
                        // onLoginCallback?.call(true);
                        // AutoRouter.of(context).push(const DashboardRouter());
                        EasyLoading.dismiss();
                        context.replace('/login');
                      } catch (err) {
                        print(err);
                        // debugPrint("Error :$err");
                      }
                    }
                  },
                ),
              ],
            ),
          ),
          Positioned(
            top: appDimension.filterY(x: size.width, y: size.height),
            left: appDimension.filterX(x: size.width, y: size.height),
            child: Container(
              height: 200,
              width: 400,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 130,
                    child: const Center(
                      child: Text("Filter Input Unit",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 200,
                    color: Colors.black,
                  ),
                  Expanded(
                    child: Image.asset("assets/icons/katunje_filter.webp",
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 200,
                    color: Colors.black,
                  ),
                  Container(
                    width: 80,
                    child: const Center(
                      child: Text("Filter Output Unit",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: appDimension.m10Y(x: size.width, y: size.height),
            left: appDimension.m10X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '10',
              height: 200,
              motorModel: motor10State.value,
              motorSettingModel: setting.motor10!,
            ),
          ),
          Positioned(
            top: appDimension.l10Y(x: size.width, y: size.height),
            left: appDimension.l10X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l10Length(x: size.width, y: size.height),
              name: "L10",
              isFlowing: (motor10State.value.motorAmps  ?? 0.0)> (setting.motor10?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l1Y(x: size.width, y: size.height),
            left: appDimension.l1X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l1Length(x: size.width, y: size.height),
              name: "L1",
              isFlowing: (motor10State.value.motorAmps  ?? 0.0)> (setting.motor10?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: true,
            ),
          ),

          Positioned(
            top: appDimension.m11Y(x: size.width, y: size.height),
            left: appDimension.m11X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '11',
              height: 200,
              motorModel: motor11State.value,
              motorSettingModel: setting.motor11!,
            ),
          ),
          Positioned(
            top: appDimension.l11Y(x: size.width, y: size.height),
            left: appDimension.l11X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l11Length(x: size.width, y: size.height),
              name: "L11",
              isFlowing: (motor11State.value.motorAmps  ?? 0.0)> (setting.motor11?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.m12Y(x: size.width, y: size.height),
            left: appDimension.m12X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '12',
              height: 200,
              motorModel: motor12State.value,
              motorSettingModel: setting.motor12!,
            ),
          ),
          Positioned(
            top: appDimension.l12Y(x: size.width, y: size.height),
            left: appDimension.l12X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l12Length(x: size.width, y: size.height),
              name: "L12",
              isFlowing: (motor12State.value.motorAmps  ?? 0.0)> (setting.motor12?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              leftFlow: false,
              singleFlow: true,
            ),
          ),

          Positioned(
            top: appDimension.m20Y(x: size.width, y: size.height),
            left: appDimension.m20X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '20',
              height: 200,
              motorModel: motor20State.value,
              motorSettingModel: setting.motor20!,
            ),
          ),
          Positioned(
            top: appDimension.l20Y(x: size.width, y: size.height),
            left: appDimension.l20X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l20Length(x: size.width, y: size.height),
              name: "L20",
              isFlowing: (motor20State.value.motorAmps  ?? 0.0)> (setting.motor20?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: true,
              isBottom: false,
              isLeft: false,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l2Y(x: size.width, y: size.height),
            left: appDimension.l2X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l2Length(x: size.width, y: size.height),
              name: "L2",
              isFlowing: (motor20State.value.motorAmps  ?? 0.0)> (setting.motor20?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              topFlow: true,
              isLeft: false,
              singleFlow: true,
            ),
          ),

          Positioned(
            top: appDimension.m21Y(x: size.width, y: size.height),
            left: appDimension.m21X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '21',
              height: 200,
              isRight: false,
              motorModel: motor21State.value,
              motorSettingModel: setting.motor21!,
            ),
          ),
          Positioned(
            top: appDimension.l21Y(x: size.width, y: size.height),
            left: appDimension.l21X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l21Length(x: size.width, y: size.height),
              name: "L21",
              isFlowing: (motor21State.value.motorAmps  ?? 0.0)> (setting.motor21?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: true,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l3Y(x: size.width, y: size.height),
            left: appDimension.l3X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l3Length(x: size.width, y: size.height),
              name: "L3",
              isFlowing: (motor21State.value.motorAmps  ?? 0.0)> (setting.motor21?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              topFlow: true,
              isLeft: false,
              singleFlow: true,
            ),
          ),

          Positioned(
            top: appDimension.m22Y(x: size.width, y: size.height),
            left: appDimension.m22X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '22',
              height: 200,
              isRight: false,
              motorModel: motor22State.value,
              motorSettingModel: setting.motor22!,
            ),
          ),

          Positioned(
            top: appDimension.l8Y(x: size.width, y: size.height),
            left: appDimension.l8X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l8Length(x: size.width, y: size.height),
              name: "L8",
              isFlowing: (motor22State.value.motorAmps  ?? 0.0)> (setting.motor22?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: true,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l9Y(x: size.width, y: size.height),
            left: appDimension.l9X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l9Length(x: size.width, y: size.height),
              name: "L9",
              isFlowing: (motor22State.value.motorAmps  ?? 0.0)> (setting.motor22?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              topFlow: true,
              isBottom: false,
              isLeft: false,

              singleFlow: true,
            ),
          ),

          Positioned(
            top: appDimension.m30Y(x: size.width, y: size.height),
            left: appDimension.m30X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '30',
              height: 200,
              isRight: false,
              motorModel: motor30State.value,
              motorSettingModel: setting.motor30!,
            ),
          ),
          Positioned(
            top: appDimension.l4Y(x: size.width, y: size.height),
            left: appDimension.l4X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l4Length(x: size.width, y: size.height),
              name: "L4",
              isFlowing: (motor30State.value.motorAmps  ?? 0.0)> (setting.motor30?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: true,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l5Y(x: size.width, y: size.height),
            left: appDimension.l5X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l5Length(x: size.width, y: size.height),
              name: "L5",
              isFlowing: (motor30State.value.motorAmps  ?? 0.0)> (setting.motor30?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: true,
            ),
          ),


          Positioned(
            top: appDimension.m31Y(x: size.width, y: size.height),
            left: appDimension.m31X(x: size.width, y: size.height),
            child: MotorPainter(
              motorId: '31',
              height: 200,
              isRight: false,
              motorModel: motor31State.value,
              motorSettingModel: setting.motor31!,
            ),
          ),
          Positioned(
            top: appDimension.l6Y(x: size.width, y: size.height),
            left: appDimension.l6X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l6Length(x: size.width, y: size.height),
              name: "L4",
              isFlowing: (motor31State.value.motorAmps  ?? 0.0)> (setting.motor31?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: true,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l7Y(x: size.width, y: size.height),
            left: appDimension.l7X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l7Length(x: size.width, y: size.height),
              name: "L5",
              isFlowing: (motor31State.value.motorAmps  ?? 0.0)> (setting.motor31?.current_threshold ?? 0.0) ? true : false,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            key: ValueKey('T10'),
            left: appDimension.t10X(x: size.width, y: size.height),
            top: appDimension.t10Y(x: size.width, y: size.height),
            child:  Row(
              children: [
                TankBlock(
                  tankId: "10",
                  tankSettingModel: setting!.tank10!,
                  height: appDimension.t10Height(
                      x: size.width, y: size.height),
                  width: appDimension.t10Width(
                      x: size.width, y: size.height),
                ),
                RotatedDistributionWidget(
                  name: "Distribution",
                  turn: 0,
                  isActive: true,
                  height: 40,
                  width: 70,
                ),
              ],
            ),
          ),

          Positioned(
            key: ValueKey('T20'),
            left: appDimension.t20X(x: size.width, y: size.height),
            top: appDimension.t20Y(x: size.width, y: size.height),
            child:  Row(
              children: [
                TankBlock(
                  tankId: "20",
                  tankSettingModel: setting!.tank20!,
                  height: appDimension.t20Height(
                      x: size.width, y: size.height),
                  width: appDimension.t20Width(
                      x: size.width, y: size.height),
                ),
                RotatedDistributionWidget(
                  name: "Distribution",
                  turn: 0,
                  isActive: true,
                  height: 40,
                  width: 70,
                ),
              ],
            ),
          ),
          Positioned(
            top: appDimension.l41Y(x: size.width, y: size.height),
            left: appDimension.l41X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l41Length(x: size.width, y: size.height),
              name: "",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: false,
            ),
          ),

          Positioned(
            key: ValueKey('T30'),
            left: appDimension.t30X(x: size.width, y: size.height),
            top: appDimension.t30Y(x: size.width, y: size.height),
            child:  Row(
              children: [
                TankBlock(
                  tankId: "30",
                  tankSettingModel: setting!.tank30!,
                  height: appDimension.t30Height(
                      x: size.width, y: size.height),
                  width: appDimension.t30Width(
                      x: size.width, y: size.height),
                ),
                RotatedDistributionWidget(
                  name: "Distribution",
                  turn: 0,
                  isActive: true,
                  height: 40,
                  width: 70,
                ),
              ],
            ),
          ),

          Positioned(
            key: ValueKey('T11'),
            left: appDimension.t11X(x: size.width, y: size.height),
            top: appDimension.t11Y(x: size.width, y: size.height),
            child:  Row(
              children: [
                TankBlock(
                  tankId: "11",
                  tankSettingModel: setting!.tank11!,
                  height: appDimension.t11Height(
                      x: size.width, y: size.height),
                  width: appDimension.t11Width(
                      x: size.width, y: size.height),
                ),
                RotatedDistributionWidget(
                  name: "Distribution",
                  turn: 0,
                  isActive: true,
                  height: 40,
                  width: 70,
                ),
              ],
            ),
          ),
          Positioned(
            top: appDimension.l40Y(x: size.width, y: size.height),
            left: appDimension.l40X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l40Length(x: size.width, y: size.height),
              name: "L40",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              leftFlow: false,
              singleFlow: true,
            ),
          ),

          Positioned(
            top: appDimension.l45Y(x: size.width, y: size.height),
            left: appDimension.l45X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l45Length(x: size.width, y: size.height),
              name: "L45",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l42Y(x: size.width, y: size.height),
            left: appDimension.l42X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l42Length(x: size.width, y: size.height),
              name: "L42",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l43Y(x: size.width, y: size.height),
            left: appDimension.l43X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l43Length(x: size.width, y: size.height),
              name: "L43",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l44Y(x: size.width, y: size.height),
            left: appDimension.l44X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l44Length(x: size.width, y: size.height),
              name: "L44",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: true,
              leftFlow: false,
              singleFlow: true,
            ),
          ),
        ],
      )
    ));
  }
}