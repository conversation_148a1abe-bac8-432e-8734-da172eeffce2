

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/history/widgets/site_four_table.dart';
import 'package:si/kawosoti/schema/block_diagram/site_four_block_diagram.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../model/site_model.dart';
import '../site_one/provider/site_one_provider.dart';

class SiteFourScreen extends HookConsumerWidget{
  const SiteFourScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteState = useState<SiteRtDbModel>(SiteRtDbModel());

    ref.listen<AsyncValue<DatabaseEvent>>(siteDataProvider('site4'), (previous, next) {
      final datasnapshot = next.asData?.value.snapshot.value as Map;
      siteState.value = SiteRtDbModel(
          Borewell: double.parse(datasnapshot['Borewell'].toString()),
          RMU2_timestamp: datasnapshot['RMU1_timestamp']);

    });
    return SiteFourBlockDiagram(siteRtDbModel: siteState.value,);
  }


}