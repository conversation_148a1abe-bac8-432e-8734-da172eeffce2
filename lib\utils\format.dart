import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';

class Format {
  static String hours(double hours) {
    final hoursNotNegative = hours < 0.0 ? 0.0 : hours;
    final formatter = NumberFormat.decimalPattern();
    final formatted = formatter.format(hoursNotNegative);
    return '${formatted}h';
  }

  static String onlydate(DateTime date) {
    initializeDateFormatting();
    return DateFormat("MMM dd, yyyy").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(date.toString(), true));}

  static String date(DateTime date) {
    initializeDateFormatting();
    return DateFormat("MMM dd, yyyy hh:mm a").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(date.toString(), true));}

  static String onlyTime(DateTime date) {
    initializeDateFormatting();
    return DateFormat("hh:mm a").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(date.toString(), true));}

  static String onlyTimeFromTimeStamp(int timeStamp) {
    initializeDateFormatting();
    return DateFormat("hh:mm a").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(DateTime.fromMillisecondsSinceEpoch(timeStamp*1000).toString(), true));}


  static String dateFromTimeStamp(int timeStamp) {
    initializeDateFormatting();
    return DateFormat("MMM dd, hh:mm a").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(DateTime.fromMillisecondsSinceEpoch(timeStamp*1000).toString(), true));}

  static String getDateOnly(DateTime date) {
    initializeDateFormatting();
    return DateFormat("MMM, yyyy").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(date.toString(), true));}

  static String getDayDateOnly(DateTime date) {
    initializeDateFormatting();
    return DateFormat("dd MMM, yyyy").format(DateFormat("yyyy-MM-dd HH:mm:ss")
        .parse(date.toString(), true));}

  static String getStartDate(DateTime date, bool isOneToOne) {
    if (isOneToOne) {
      return DateFormat.yMMMd().format(DateFormat("yyyy-MM-dd HH:mm:ss")
          .parse(date.toString(), true)
          .toLocal());
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }


  static String getStartTime(DateTime date, bool isOneToOne) {
    if (isOneToOne) {
      return   DateFormat.Hm().format(DateFormat("yyyy-MM-dd HH:mm:ss")
          .parse(date.toString(), true)
          .toLocal());
    } else {
      return DateFormat.Hm().format(date);
    }
  }

  static String dayOfWeek(DateTime date) {
    return DateFormat.E().format(date);
  }

  static String currency(double pay) {
    if (pay != 0.0) {
      final formatter = NumberFormat.simpleCurrency(decimalDigits: 0);
      return formatter.format(pay);
    }
    return '';
  }
}
