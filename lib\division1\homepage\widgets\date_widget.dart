



import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/dashboard/dashboard_page.dart';

import '../../../common/widgets/widgets.dart';

class DateWidget extends HookConsumerWidget {
  const DateWidget(this.dateController, this.initialDateTime, this.onConfirm,
      {super.key});

  final TextEditingController dateController;
  final Function(DateTime) onConfirm;
  final DateTime initialDateTime;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tempDate = useRef(DateTime.now());
    log(dateController.text);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        showDateTimeDialog(
          context,
          buttonText: "Done",
          onButtonPressed: () {

            onConfirm(tempDate.value);
            },
          child: Semantics(
            focusable: true,
            label: 'date-picker',
            child: CupertinoDatePicker(
              initialDateTime: initialDateTime,
              mode: CupertinoDatePickerMode.date,
              use24hFormat: false,
              // This is called when the user changes the date.
              onDateTimeChanged: (DateTime newDate) {
                tempDate.value = newDate;
              },
            ),
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CustomTextFormField(
          name: "Date",
          hintText: "Date",
          controller: dateController,
          suffixIcon: Icon(Icons.date_range),
          textInputAction: TextInputAction.next,
          enabled: false,
        ),
      ),
    );
  }
}