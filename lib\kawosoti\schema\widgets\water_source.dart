

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../constants/assets_manager.dart';

class WaterSource extends StatelessWidget{
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(15.0),
      padding: const EdgeInsets.all(3.0),
      decoration: BoxDecoration(border: Border.all(color: Colors.blueAccent)),
      child: Center(
          child: Text(
            'Source',
            style: TextStyle(fontSize: 16),
          )),
    );
  }

}

class SedimentTank extends StatelessWidget{

  const SedimentTank({
    this.height = 150,
    this.width = 300,
    Key? key,
  }) : super(key: key);

  final double height;
  final double width;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Image.asset(
        AppAssets.instance.sediment,
        height: height,
        width: width,
      ),
    );
  }

}


class Tank extends StatelessWidget{

  const Tank({
    this.height = 150,
    this.width = 300,
    Key? key,
  }) : super(key: key);

  final double height;
  final double width;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Image.asset(
        AppAssets.instance.tank,
        height: height,
        width: width,
      ),
    );
  }

}