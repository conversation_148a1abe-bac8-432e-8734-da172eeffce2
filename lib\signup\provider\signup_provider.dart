
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/provider/auth_provider.dart';

import '../controller/signup_controller.dart';
import '../controller/signup_state.dart';


final signUpEyeToggleProvider = StateProvider.autoDispose<bool>((ref) {
  return true;
});


final signupControllerProvider = StateNotifierProvider.autoDispose<SignupController, SignupState>((ref) {
  final signupRepository = ref.watch(authRepositoryProvider);
  return SignupController(signupRepository: signupRepository);
});
