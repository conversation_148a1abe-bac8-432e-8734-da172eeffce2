

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../utils/timestamp_converter.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';


@freezed
class UserModel with _$UserModel{
  const factory UserModel({
    String? email,
    String? fullName,
    String? id,
    String? phoneNumber,
    int? role,
    String? siteId,
    bool? is_active,
    String? token,
    bool? send_notification,
    @TimestampNullableConverter() DateTime? created_at,

  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
}

class UserCallbackModel {

  String? email;
  String? password;

  UserCallbackModel({this.email, this.password});

}