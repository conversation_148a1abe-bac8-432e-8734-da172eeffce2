



import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../utils/timestamp_converter.dart';

part 'history_model.freezed.dart';
part 'history_model.g.dart';

@freezed
class Historymodel with _$Historymodel{
  const factory Historymodel({
    @Default(0) int loc_id,
    @Default(0) int status,
    @TimestampNullableConverter() DateTime? time,

  }) = _Historymodel;

  factory Historymodel.fromJson(Map<String, dynamic> json) => _$HistorymodelFromJson(json);
}



@freezed
class LevelHistorymodel with _$LevelHistorymodel{
  const factory LevelHistorymodel({
    @Default(0) int loc_id,
    @Default(0) int level,
    @TimestampNullableConverter() DateTime? time,

  }) = _LevelHistorymodel;

  factory LevelHistorymodel.fromJson(Map<String, dynamic> json) => _$LevelHistorymodelFromJson(json);
}


@freezed
class AllHistorymodel with _$AllHistorymodel{
  const factory AllHistorymodel({
    @Default(0) int loc_id,
    @Default(0) int float,
    @Default(0) int motor,
    @Default(0) int timer,
    @Default(0) int level,
    @Default(0.0) double current,
    @TimestampNullableConverter() DateTime? time,

  }) = _AllHistorymodel;

  factory AllHistorymodel.fromJson(Map<String, dynamic> json) => _$AllHistorymodelFromJson(json);
}
