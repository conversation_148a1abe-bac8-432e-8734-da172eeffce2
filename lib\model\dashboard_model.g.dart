// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DashboardModelImpl _$$DashboardModelImplFromJson(Map<String, dynamic> json) =>
    _$DashboardModelImpl(
      float_status: (json['float_status'] as num?)?.toInt(),
      float_updated_at: const TimestampNullableConverter()
          .fromJson(json['float_updated_at'] as Timestamp?),
      motor_status: (json['motor_status'] as num?)?.toInt(),
      motor_updated_at: const TimestampNullableConverter()
          .fromJson(json['motor_updated_at'] as Timestamp?),
      timer_status: (json['timer_status'] as num?)?.toInt(),
      timer_updated_at: const TimestampNullableConverter()
          .fromJson(json['timer_updated_at'] as Timestamp?),
      level: (json['level'] as num?)?.toInt(),
      level_updated_at: const TimestampNullableConverter()
          .fromJson(json['level_updated_at'] as Timestamp?),
      maxHeight: (json['maxHeight'] as num?)?.toInt(),
      minHeight: (json['minHeight'] as num?)?.toInt(),
      actualHeight: (json['actualHeight'] as num?)?.toInt(),
      timer: (json['timer'] as num?)?.toInt(),
      mode: (json['mode'] as num?)?.toInt(),
      current: (json['current'] as num?)?.toDouble(),
      current_updated_at: const TimestampNullableConverter()
          .fromJson(json['current_updated_at'] as Timestamp?),
    );

Map<String, dynamic> _$$DashboardModelImplToJson(
        _$DashboardModelImpl instance) =>
    <String, dynamic>{
      'float_status': instance.float_status,
      'float_updated_at':
          const TimestampNullableConverter().toJson(instance.float_updated_at),
      'motor_status': instance.motor_status,
      'motor_updated_at':
          const TimestampNullableConverter().toJson(instance.motor_updated_at),
      'timer_status': instance.timer_status,
      'timer_updated_at':
          const TimestampNullableConverter().toJson(instance.timer_updated_at),
      'level': instance.level,
      'level_updated_at':
          const TimestampNullableConverter().toJson(instance.level_updated_at),
      'maxHeight': instance.maxHeight,
      'minHeight': instance.minHeight,
      'actualHeight': instance.actualHeight,
      'timer': instance.timer,
      'mode': instance.mode,
      'current': instance.current,
      'current_updated_at': const TimestampNullableConverter()
          .toJson(instance.current_updated_at),
    };
