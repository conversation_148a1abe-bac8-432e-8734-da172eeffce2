import 'dart:async';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:si/model/setting_model.dart';

import '../services/firestore_services.dart';

class RealtimeDatabase {
  RealtimeDatabase();

  final DatabaseReference databaseReference = FirebaseDatabase.instance.ref();

  final dbInstance = FirestoreService.instance;

  Future<void> startMotor(String motorId) async {
    try{
      await databaseReference
          .child("9216/${motorId}")
          .update({
        'mobile_status': 1,
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> startValve(String valveId) async {
    try{
      await databaseReference
          .child(valveId)
          .update({
        'mobile_status': 1,
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> startOutput(String outputId) async {
    try{
      await databaseReference
          .child(outputId)
          .update({
        'status': 1,
      })
          .whenComplete(() => debugPrint('StartOutput > Motor started from mobile.'));
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> stopValve(String valveId) async {
    try{
      await databaseReference
          .child(valveId)
          .update({
        'mobile_status': 2,
      })
          .whenComplete(() => debugPrint('updateMotorStatus > Motor started from mobile.'));
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> stopOutput(String outputId) async {
    try{
      await databaseReference
          .child(outputId)
          .update({
        'status': 2,
      })
          .whenComplete(() => debugPrint('stopOutput > Motor started from mobile.'));
    }catch(e){
      EasyLoading.dismiss();
      print(e);
    }

  }

  Future<void> stopMotor(String motorId) async {
    await databaseReference
        .child("9216/${motorId}")
        .update({
      'mobile_status': 2,
    })
        .whenComplete(() => debugPrint('updateMotorStatus > Motor stopped from mobile.'));
  }

  Future<void> testNotification(String test) async {
    final keys = await databaseReference
        .ref.key;
    await databaseReference.child('test').update({'test':test});

  }


  Stream<DatabaseEvent> readMotor(String motorId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(motorId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Stream<DatabaseEvent> readTank(String tankId) {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(tankId);
    if (!kIsWeb) ref.keepSynced(true);
    return ref.onValue.asBroadcastStream();
  }

  Future<DatabaseEvent> getTankLevel(String tankId) async {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child(tankId);
    return await ref.once();
  }

  Future<SettingModel> getSetting(String settingId) async {
    // print("REALTIME DATABAES");
    final ref = databaseReference.child("9216/${settingId}");
    return SettingModel.fromJson( Map<String, dynamic>.from((await ref.once()).snapshot.value as Map));
  }

  Future<bool> setSetting(String site, SettingModel settingModel) async {
    String  setting = site == "site1" ? "setting1" : "setting2";
    try{
      await databaseReference.child("9216/${setting}").set(settingModel.toJson());
      await dbInstance.setData(path: "9216/${site}", data: settingModel.toJson());
      return true;
    }catch(e){
      print(e);
      rethrow;
    }

  }


}
