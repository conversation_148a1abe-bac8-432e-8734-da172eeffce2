import 'dart:developer';
import 'dart:math' as math;

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/repository/new_division_repository.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:si/utils/app_dimension.dart';

import '../../constants/state_model.dart';
import '../../provider/auth_provider.dart';
import '../../utils/dimensions/katunje_dimension.dart';
import '../../utils/dimensions/kolati_dimension.dart';
import '../../utils/dimensions/talkhu_dimension.dart';
import '../hive/hive_repository.dart';
import '../model/division_setting_model.dart';
import '../model/motor_setting_model.dart';
import '../model/tank_setting_model.dart';
import '../repository/division_rtdb_repository.dart';

final divisionRepositoryProvider = Provider<INewDivisionRepository>((ref) {
  return NewDivisionRepository();
});

final localStorageProvider = Provider<ILocalRepository>((ref) {
  final preference = ref.read(sharedPreferencesServiceProvider);
  return LocalRepository(preference);
});

final dimensionProvider = Provider<AppDimension>((ref){
  return AppDimension();
});

final talkhuProvider = Provider<TalkhuAppDimension>((ref){
  return TalkhuAppDimension();
});

final sindhulpalchowkProvider = Provider<SindhupalchowkDimension>((ref){
  return SindhupalchowkDimension();
});

final kolatiDimensionProvider = Provider<KolatiDimension>((ref){
  return KolatiDimension();
});

final katunjeDimensionProvider = Provider<KatunjeDimension>((ref){
  return KatunjeDimension();
});

final secureStorageProvider = Provider<FlutterSecureStorage>((ref) {
  return const FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
  );
});

final divisionRtdbServiceProvider = Provider<DivisionRtdbRepository>((ref) {
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return DivisionRtdbRepository(prefs);
});

final tankProvider =
    StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, tankId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readTank(prefs.getSiteId() + "/tank/" + tankId);
});

final sensorStreamProvider =
StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, sensorId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readTank(prefs.getSiteId() + "/sensor/" + sensorId);
});



final bwProvider =
StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, id) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readBorewell(prefs.getSiteId() + "/bw/" + id);
});


final inputProvider =
    StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, inputId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readTank(prefs.getSiteId() + "/" + inputId);
});

final outputProvider =
    StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, outputId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readTank(prefs.getSiteId() + "/" + outputId);
});

final futureTankProvider =
    FutureProvider.autoDispose.family<DatabaseEvent, String>((ref, tankId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.getTankLevel(prefs.getSiteId() + "/" + tankId);
});

final futureSiteListProvider =
    FutureProvider.autoDispose<List<DivisionSettingModel?>>((ref) {
  final repo = ref.watch(divisionRepositoryProvider);
  return repo.getSiteList();
});

final motorProvider =
    StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, motorId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readMotor(prefs.getSiteId() + "/motor/" + motorId);
});

final sensorProvider =
StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, sensorId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readMotor(prefs.getSiteId() + "/sensor/" + sensorId);
});

final valveProvider =
StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, valveId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readMotor(prefs.getSiteId() + "/valve/" + valveId);
});


final filterProvider =
    StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, filterId) {
  final rtdb = ref.watch(divisionRtdbServiceProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  return rtdb.readMotor(prefs.getSiteId() + "/filter/" + filterId);
});

final motorSettingProvider =
    FutureProvider.autoDispose.family<MotorSettingModel?, String>((ref, req) {
  final auth = ref.watch(authStateNotifierProviderAuth);
  final repo = ref.watch(divisionRepositoryProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  if (auth != null) {
    return repo.getMotorSetting(req);
  } else {
    return Future.value(MotorSettingModel());
  }
});

final siteSettingProvider = FutureProvider.autoDispose
    .family<DivisionSettingModel?, String>((ref, req) async {
  final auth = ref.watch(authStateNotifierProviderAuth);
  final repo = ref.watch(divisionRepositoryProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  if (auth != null) {
    final setting = await repo.getSiteSetting(req);
    await ref.read(localStorageProvider).setSettings(setting!);
    return setting;
  } else {
    return Future.value(DivisionSettingModel());
  }
});

final tankSettingProvider =
    FutureProvider.autoDispose.family<TankSettingModel?, String>((ref, req) {
  final auth = ref.watch(authStateNotifierProviderAuth);
  final repo = ref.watch(divisionRepositoryProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  if (auth != null) {
    return repo.getTankSetting(prefs.getSiteId() + "/" + req);
  } else {
    return Future.value(TankSettingModel());
  }
});

final tankSettingStreamProvider =
    StreamProvider.autoDispose.family<TankSettingModel?, String>((ref, req) {
  final auth = ref.watch(authStateNotifierProviderAuth);
  final repo = ref.watch(divisionRepositoryProvider);
  final prefs = ref.watch(sharedPreferencesServiceProvider);
  if (auth != null) {
    return repo.getStreamTankSetting(prefs.getSiteId() + "/" + req);
  } else {
    return Stream.value(TankSettingModel());
  }
});

final userProfileProvider = StreamProvider<UserModel?>((ref) {
  final auth = ref.watch(authStateNotifierProviderAuth);

  final repo = ref.watch(divisionRepositoryProvider);
  if (auth != null) {
    return repo.getUserProfile(auth.uid);
  } else {
    return const Stream.empty();
  }
});

final divisionStateProvider = StateNotifierProvider.family
    .autoDispose<DivisionSettingState, Resource<DivisionSettingModel>, String>(
        (ref, req) {
  final repo = ref.watch(divisionRepositoryProvider);
  final localRepo = ref.watch(localStorageProvider);
  return DivisionSettingState(
      divisionRepository: repo, localRepository: localRepo, siteId: req);
});

class DivisionSettingState
    extends StateNotifier<Resource<DivisionSettingModel>> {
  DivisionSettingState(
      {required this.divisionRepository,
      required this.localRepository,
      required this.siteId})
      : super(const Resource.unInitialized()) {
    getSetting(siteId);
  }

  final INewDivisionRepository divisionRepository;
  final ILocalRepository localRepository;
  final String siteId;

  Future<void> getSetting(String siteId) async {
    try{
      state = const Resource.loading();
      final setting = await divisionRepository.getSiteSetting(siteId);
      log(setting.toString());
      await localRepository.setSettings(setting!);
      state = Resource.success(setting, "Success");
    }catch(e){
      state = Resource.error("Error");
    }

  }
}

final refreshProvider = StateProvider<int>((ref) {
  return random(0,999999);
});

int random(int min, int max) {
  return min + math.Random().nextInt(max - min);
}

final motor10Provider = StateProvider<bool>((ref) {
  return false;
});

final motor11Provider = StateProvider<bool>((ref) {
  return false;
});

final motor12Provider = StateProvider<bool>((ref) {
  return false;
});

final motor20Provider = StateProvider<bool>((ref) {
  return false;
});

final motor21Provider = StateProvider<bool>((ref) {
  return false;
});

final motor30Provider = StateProvider<bool>((ref) {
  return false;
});

final motor31Provider = StateProvider<bool>((ref) {
  return false;
});

final motor41Provider = StateProvider<bool>((ref) {
  return false;
});

final motor40Provider = StateProvider<bool>((ref) {
  return false;
});


final valve10Provider = StateProvider<bool>((ref) {
  return false;
});

final valve11Provider = StateProvider<bool>((ref) {
  return false;
});

final valve12Provider = StateProvider<bool>((ref) {
  return false;
});

final valve13Provider = StateProvider<bool>((ref) {
  return false;
});

final valve20Provider = StateProvider<bool>((ref) {
  return false;
});

final valve21Provider = StateProvider<bool>((ref) {
  return false;
});

final valve30Provider = StateProvider<bool>((ref) {
  return false;
});

final valve31Provider = StateProvider<bool>((ref) {
  return false;
});

final valve41Provider = StateProvider<bool>((ref) {
  return false;
});

final valve40Provider = StateProvider<bool>((ref) {
  return false;
});


final valve50Provider = StateProvider<bool>((ref) {
  return false;
});

final motor50Provider = StateProvider<bool>((ref) {
  return false;
});