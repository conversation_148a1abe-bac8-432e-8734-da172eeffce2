import 'dart:math';

import 'package:flutter/material.dart';
import 'package:si/division1/repository/division_repository.dart';
import 'package:si/division1/repository/new_division_repository.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/services/firestore_path.dart';

import '../../../datatable/paginated_data_table_2.dart';
import '../../../utils/format.dart';
import '../model/level_history_model.dart';

// Async datasource for AsynPaginatedDataTabke2 example. Based on AsyncDataTableSource which
/// is an extension to <PERSON>utter's DataTableSource and aimed at solving
/// saync data fetching scenarious by paginated table (such as using Web API)
class LogsHistorySiteDataSource extends AsyncDataTableSource {
  LogsHistorySiteDataSource({this.userType = 1, this.onEdit, this.onDelete}) {
    print('SiteDataSource created');
  }

  final Function(LogsModel)? onEdit;
  final Function(LogsModel)? onDelete;

  LogsHistorySiteDataSource.empty(
      {this.userType = 1, this.onEdit, this.onDelete}) {
    _empty = true;
    print('SiteDataSource.empty created');
  }

  LogsHistorySiteDataSource.error(
      {this.userType = 1, this.onEdit, this.onDelete}) {
    print('SiteDataSource.error created');
  }

  bool _empty = false;
  final int userType;

  DateTime? _siteDate;
  String deviceId = "10";

  int type = 1;

  DateTime? startSiteDate;
  DateTime? endSiteDate;

  String siteId = FirestorePath.schemaOne;

  set startDate(DateTime? value) {
    startSiteDate = value;
    notifyListeners();
  }

  set SiteId(String value) {
    siteId = value;
    notifyListeners();
  }

  set Id(String id) {
    deviceId = id;
    notifyListeners();
  }

  int startingIndex = 0;

  List<LogsModel> listLogsModel = [];

  final INewDivisionRepository _repo = NewDivisionRepository();

  Future<int> getTotalRecords() {
    return Future<int>.delayed(
        const Duration(milliseconds: 0), () => _empty ? 0 : 15);
  }

  @override
  Future<AsyncRowsResponse> getRows(int start, int end) async {
    var index = 0;
    final today = DateTime.now();
    var x = start == 0
        ? await _repo.getSiteLogs(
            siteId: siteId,
            id: deviceId,
            date: startSiteDate ??
                DateTime(today.year, today.month, today.day, 0, 0, 0),
            type: type)
        : start < startingIndex
            ? listLogsModel.skip(start).take(end).toList()
            : await _repo.getSiteLogs(
                siteId: siteId,
                id: deviceId,
                type: type,
                startAfterDate: _siteDate,
                date: startSiteDate ??
                    DateTime(today.year, today.month, today.day, 0, 0, 0));
    if (start == 0) {
      listLogsModel.clear();
    }
    final count = await _repo.getLogsLength(
        siteId: siteId,
        id: deviceId,
        dateTime: (startSiteDate ??
            DateTime(today.year, today.month, today.day, 0, 0, 0)),
        type: type);
    if (count == 0) {
      _empty = true;
      return AsyncRowsResponse(listLogsModel.length, []);
    } else {
      var r = AsyncRowsResponse(
          count,
          x.map((siteModel) {
            index++;
            return DataRow(
              key: ValueKey<String>(siteModel!.time!.toString()),
              selected: false,
              onSelectChanged: (value) {},
              cells: getSiteCells(start, index, siteModel,
                  onEdit: onEdit, onDelete: onDelete),
            );
          }).toList());

      startingIndex = start;
      if (start >= startingIndex) {
        for (final model in x) {
          listLogsModel.add(model!);
        }
      }
      _siteDate = x.last!.time;

      return r;
    }
  }
}

List<DataCell> getSiteCells(int start, int index, LogsModel logsModel,
    {Function? onEdit, Function? onDelete}) {
  return [
    DataCell(Text(
      Format.onlyTime(logsModel.time ?? DateTime.now()).toString(),
      style: TextStyle(fontSize: 14, color: Colors.black),
    )),
    DataCell(Text(
      logsModel.type == 1 ? "Tank" : "Motor",
      style: TextStyle(fontSize: 14, color: Colors.black),
    )),
    DataCell(Text(
      logsModel.name.toString(),
      style: TextStyle(fontSize: 14, color: Colors.black),
    )),
    DataCell(Text(
      logsModel.device_id.toString(),
      style: TextStyle(fontSize: 14, color: Colors.black),
    )),
    DataCell(Text(
      logsModel.remark,
      style: TextStyle(fontSize: 14, color: Colors.black),
    )),
    DataCell(Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(Icons.edit, color: Colors.blue, size: 20),
          onPressed: () {
            if (onEdit != null) {
              onEdit(logsModel);
            }
          },
        ),
        IconButton(
          icon: Icon(Icons.delete, color: Colors.red, size: 20),
          onPressed: () {
            if (onDelete != null) {
              onDelete(logsModel);
            }
          },
        ),
      ],
    ))
  ];
}
