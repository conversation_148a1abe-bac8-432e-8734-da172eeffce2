
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/provider/auth_provider.dart';
import '../kawosoti/schema/model/user_model.dart';
import '../services/firestore_path.dart';
import '../services/firestore_services.dart';
import '../services/shared_preferences_service.dart';

abstract class IAuthRepository {
  const IAuthRepository();
  Stream<User?> get authStateChanges;
  Stream<User?> get idTokenChanges;

  Future<bool> signInWithEmail(String email, String password, String token);
  Future<String> verifyPasswordAndReturnSite(String password);
  Future<bool> createUserWithEmail(String email,
      String password, String fullname, String phonenumber, String siteId);
  Future<void> forgotPassword(String email);
  User? getCurrentUser();

  Future<void> setUserLoginInfo(bool loginInfo);
  Future<void> signOut();

  bool isLogin();
  int getRoleType();
  bool hasSite();
  Future<void> updateToken(String userId, String token);
  Future<void> updateUserSiteId(String userId, String siteId);

}

class AuthRepository extends IAuthRepository{
  AuthRepository(this.ref);

  final ProviderRef ref;
  final dbInstance = FirestoreService.instance;



  @override
  Stream<User?> get authStateChanges =>  ref.read(firebaseAuthProvider).authStateChanges();


  @override
  Future<bool> signInWithEmail(String email, String password, String token) async {
    final sharedPreferencesService = ref.read(sharedPreferencesServiceProvider);

    try {
      final user = await ref.read(firebaseAuthProvider).signInWithEmailAndPassword(email: email, password: password);
      await dbInstance.updateDoc(path: "${FirestorePath.users}/${user.user?.uid}", data: { "token": token});
      final databaseUser = await dbInstance.documentFuture(path: "${FirestorePath.users}/${user.user?.uid}",
        builder: (data, documentID) => data == null
          ? UserModel()
          : UserModel.fromJson(data
        ..addAll({
          'id': documentID,
        })),);
      await sharedPreferencesService.setEmail(databaseUser.email ?? '');
      await sharedPreferencesService.setPhoneNumber(databaseUser.phoneNumber ?? '');
      await sharedPreferencesService.setFullName(databaseUser.fullName ?? '');
      await sharedPreferencesService.setRoleType(databaseUser.role ?? 0);
      await sharedPreferencesService.setLogin();
      return true;
    } on PlatformException catch (platEx) {
      await sharedPreferencesService.setLogout();
      EasyLoading.showError('Email sign in failed: ${platEx.code.replaceAll('_', ' ')}');
      rethrow;
    } on FirebaseAuthException catch (authEx) {
      // print('authEx.code : ${authEx.code}');
      if (authEx.code == 'wrong-password') {
        EasyLoading.showError('You have entered an invalid username or password');
        rethrow;
      }
      await sharedPreferencesService.setLogout();
      EasyLoading.showError('Email sign in failed: ${authEx.message}');
      rethrow;

    } catch (e) {
      await sharedPreferencesService.setLogout();
      EasyLoading.showError('Email sign in failed');
      rethrow;
    }
  }

  @override
  Future<void> forgotPassword(String email) {
    // TODO: implement forgotPassword
    throw UnimplementedError();
  }

  @override
  User? getCurrentUser() {
    return FirebaseAuth.instance.currentUser;
  }

  @override
  // TODO: implement idTokenChanges
  Stream<User?> get idTokenChanges => throw UnimplementedError();

  @override
  Future<void> setUserLoginInfo(bool loginInfo) {
    // TODO: implement setUserLoginInfo
    throw UnimplementedError();
  }


  @override
  Future<void> signOut() async{
    EasyLoading.show();
    await ref.read(firebaseAuthProvider).signOut();
    await ref.read(sharedPreferencesServiceProvider).clearPreferences();
    EasyLoading.dismiss();
  }

  @override
  bool isLogin() {
    return ref.read(firebaseAuthProvider).currentUser != null && ref.read(sharedPreferencesServiceProvider).isLogin();
  }

  @override
  Future<bool> createUserWithEmail(String email, String password,
      String fullname, String phonenumber, String siteId) async {
    try {
      final user = await ref.read(firebaseAuthProvider).createUserWithEmailAndPassword(email: email, password: password);
      dbInstance.setData(path: "${FirestorePath.users}/${user.user?.uid}",
          data: UserModel(fullName: fullname,created_at: DateTime.now(),
              is_active: true,
              send_notification: false,
              siteId: siteId,
              email: email, phoneNumber: phonenumber, id: user.user?.uid, role: 1, token: "").toJson());
      return true;
    } on PlatformException catch (platEx) {
      //Logger().e('PLATFORM EXCEPTION : failed to sign up email> $platEx');
      EasyLoading.showError('Failed to create an account: ${platEx.code.replaceAll('_', ' ')}');
      rethrow;
    } on FirebaseAuthException catch (authEx) {
      //Logger().e('AUTH EXCEPTION : failed to signup email > ${authEx.code}');
      EasyLoading.showError('Failed to create an account:  ${authEx.message}');
      rethrow;
    } catch (e) {
      //Logger().e('failed create user from email', e);
      EasyLoading.showError('Failed to create an account.\nPlease try again.');
      rethrow;
    }
  }

  @override
  Future<String> verifyPasswordAndReturnSite(String password) {
    return Future.value("site1");
  }

  @override
  int getRoleType() {
    return ref.read(sharedPreferencesServiceProvider).getUserRole();
  }

  @override
  bool hasSite() {
    return ref.read(sharedPreferencesServiceProvider).hasSite();
  }

  @override
  Future<void> updateToken(
      String userId,
      String token,
      ) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.userDoc(userId),
        data: {
          'token': token,
        },
      );
    } catch (e) {
      print(e);
    }
  }

  @override
  Future<void> updateUserSiteId(String userId, String siteId) async {
    try {
      await dbInstance.updateDoc(
        path: FirestorePath.userDoc(userId),
        data: {
          'siteId': siteId,
        },
      );
    } catch (e) {}
  }


}