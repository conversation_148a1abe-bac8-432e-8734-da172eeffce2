


import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_common_motor_unit.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';

import '../../../../common/widgets/base_scaffold.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../constants/app_sizes.dart';
import '../../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';


class MobileKolatiPage extends HookConsumerWidget{

  MobileKolatiPage(this.setting);

  DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);

    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return BaseScaffold(
        showLeftIcon: false,
        appbarText: setting?.name ?? "",
        showAction: prefs.getUserRole() >3 ? true : false,
        onActionClick:() async {
          final setting = await context.push("/location");
        },
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Container(
                      height: 40,
                      width: double.infinity,
                      color: Colors.blueAccent,
                      child: Center(
                        child: Text(
                          "मुलको पानी",
                          style: TextStyle(color: Colors.white),
                        ),
                      )
                  ),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0, left: 8, right: 0),
                              child: NewMotorUnit(ratio: 2.5,motorId: "10",
                                motorSettingModel: setting!.motor10!,
                                motorModel: motor10State.value,
                              ),
                            ),
                            Container(
                              height: 60,
                              width: 20,
                              color: Colors.blueAccent,
                            )
                          ],
                        ),
                        Container(
                          child: Column(
                            children: [
                              gapH80,
                              Container(
                                height: 20,
                                width: 50,
                                color: Colors.blueAccent,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                color: Colors.blueAccent,
                                height: 40,
                                width: 20,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: NewTankUnit(3, "10",
                                    setting.tank10!!, 180, double.infinity),
                              ),
                              gapH20,
                            ],
                          ),
                        ),
                      ]),
                  Container(
                    height: 20,
                    width: double.infinity,
                    color: Colors.blueAccent,

                  ),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                color: Colors.blueAccent,
                                height: 40,
                                width: 20,
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                                child: NewTankUnit(3, "11",
                                    setting.tank11!!, 220, double.infinity),
                              ),
                              Container(
                                color: Colors.blueAccent,
                                height: 60,
                                width: 20,
                              )
                            ],
                          ),
                        ),
                      ]),
                  Container(
                    height: 20,
                    width: double.infinity,
                    color: Colors.blueAccent,
                  ),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              height: 60,
                              width: 20,
                              color: Colors.blueAccent,
                            ),
                            Padding(
                              padding: const EdgeInsets.only( left: 8, right: 0),
                              child: NewMotorUnit(ratio: 2.5,motorId: "11",
                                motorSettingModel: setting!.motor11!,
                                motorModel: motor11State.value,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          child: Column(
                            children: [
                              SizedBox(
                                height: 120,
                              ),
                              Container(
                                height: 20,
                                width: 50,
                                color: Colors.blueAccent,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              gapH48,
                              Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: NewTankUnit(3, "12",
                                    setting.tank12!!, 180, double.infinity),
                              ),
                              Container(
                                color: Colors.blueAccent,
                                height: 40,
                                width: 20,

                              ),

                            ],
                          ),
                        ),
                      ]),
                  Container(
                    height: 30,
                    width: double.infinity,
                    color: Colors.blueAccent,
                  ),
                ],
              ),
            ),
          ],
        ));
  }

}