

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import '../widgets/inactive_valve_widget.dart';
import '../widgets/tank_widget.dart';

class MobileJyotinagarPage extends HookConsumerWidget{

  final DivisionSettingModel? setting;

  const MobileJyotinagarPage(this.setting);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);

    final motor10State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            height: 40,
            color: Colors.blueAccent,
            child: Center(
              child: Text(
                "बोरिङ्ग ",
                style: TextStyle(color: Colors.white, fontSize: 20),
              ),
            ),
          ),
          Column(
            children: [
              Container(
                height: 50,
                width: 20,
                color: Colors.blueAccent,
              ),
              NewMotorUnit(
                motorId: '10',
                motorSettingModel: setting!.motor10!,
                motorModel: motor10State.value,
                ratio: 2,
              ),
            ],
          ),
          Column(
            children: [
              Container(
                height: 40,
                width: 20,
                color: Colors.blueAccent,
              ),
              TankBlock(
                tankId: "10",
                tankSettingModel: setting!.tank10!,
                height: 250,
                width: 250,
              ),
            ],
          ),
        ],
      ),
    );

  }
}