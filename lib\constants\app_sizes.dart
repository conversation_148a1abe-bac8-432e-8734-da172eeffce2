import 'package:flutter/material.dart';

/// Constant sizes to be used in the app (paddings, gaps, rounded corners etc.)
class Sizes {
  static const phalf = 0.5;
  static const p1 = 1.0;
  static const p2 = 2.0;
  static const p3 = 3.0;
  static const p4 = 4.0;
  static const p8 = 8.0;
  static const p6 = 6.0;
  static const p10 = 10.0;
  static const p12 = 12.0;
  static const p16 = 16.0;
  static const p18 = 18.0;
  static const p20 = 20.0;
  static const p24 = 24.0;
  static const p28 = 28.0;
  static const p32 = 32.0;
  static const p36 = 36.0;
  static const p38 = 38.0;
  static const p40 = 40.0;
  static const p44 = 44.0;
  static const p48 = 48.0;
  static const p52 = 52.0;
  static const p60 = 60.0;
  static const p64 = 64.0;
  static const p80 = 80.0;
  static const p90 = 90.0;
  static const p120 = 120.0;
}

/// Constant gap widths
const gapW4 = SizedBox(width: Sizes.p4);
const gapW2 = SizedBox(width: Sizes.p2);
const gapW8 = SizedBox(width: Sizes.p8);
const gapW12 = SizedBox(width: Sizes.p12);
const gapW16 = SizedBox(width: Sizes.p16);
const gapW20 = SizedBox(width: Sizes.p20);
const gapW24 = SizedBox(width: Sizes.p24);
const gapW32 = SizedBox(width: Sizes.p32);
const gapW48 = SizedBox(width: Sizes.p48);
const gapW64 = SizedBox(width: Sizes.p64);
const gapW80 = SizedBox(width: Sizes.p80);

/// Constant gap heights
const gapH4 = SizedBox(height: Sizes.p4);
const gapH2 = SizedBox(height: Sizes.p2);
const gapH8 = SizedBox(height: Sizes.p8);
const gapH6 = SizedBox(height: Sizes.p6);
const gapH12 = SizedBox(height: Sizes.p12);
const gapH16 = SizedBox(height: Sizes.p16);
const gapH20 = SizedBox(height: Sizes.p20);
const gapH24 = SizedBox(height: Sizes.p24);
const gapH32 = SizedBox(height: Sizes.p32);
const gapH36 = SizedBox(height: Sizes.p36);
const gapH38 = SizedBox(height: Sizes.p38);
const gapH40 = SizedBox(height: Sizes.p40);
const gapH44 = SizedBox(height: Sizes.p44);
const gapH48 = SizedBox(height: Sizes.p48);
const gapH64 = SizedBox(height: Sizes.p64);
const gapH80 = SizedBox(height: Sizes.p80);
const gapH90 = SizedBox(height: Sizes.p90);

const horPadding4 = EdgeInsets.symmetric(horizontal: Sizes.p4);
const horPadding2 = EdgeInsets.symmetric(horizontal: Sizes.p2);
const horPadding8 = EdgeInsets.symmetric(horizontal: Sizes.p8);
const horPadding12 = EdgeInsets.symmetric(horizontal: Sizes.p12);
const horPadding16 = EdgeInsets.symmetric(horizontal: Sizes.p16);
const horPadding20 = EdgeInsets.symmetric(horizontal: Sizes.p20);
const horPadding24 = EdgeInsets.symmetric(horizontal: Sizes.p24);
const horPadding32 = EdgeInsets.symmetric(horizontal: Sizes.p32);
const horPadding40 = EdgeInsets.symmetric(horizontal: Sizes.p40);
const horPadding48 = EdgeInsets.symmetric(horizontal: Sizes.p48);
const horPadding64 = EdgeInsets.symmetric(horizontal: Sizes.p64);

const vertPadding4 = EdgeInsets.symmetric(vertical: Sizes.p4);
const vertPadding8 = EdgeInsets.symmetric(vertical: Sizes.p8);
const vertPadding12 = EdgeInsets.symmetric(vertical: Sizes.p12);
const vertPadding16 = EdgeInsets.symmetric(vertical: Sizes.p16);
const vertPadding20 = EdgeInsets.symmetric(vertical: Sizes.p20);
const vertPadding24 = EdgeInsets.symmetric(vertical: Sizes.p24);
const vertPadding32 = EdgeInsets.symmetric(vertical: Sizes.p32);
const vertPadding48 = EdgeInsets.symmetric(vertical: Sizes.p48);

const padding2 = EdgeInsets.all(Sizes.p2);
const padding4 = EdgeInsets.all(Sizes.p4);
const padding8 = EdgeInsets.all(Sizes.p8);
const padding12 = EdgeInsets.all(Sizes.p12);
const padding16 = EdgeInsets.all(Sizes.p16);
const padding20 = EdgeInsets.all(Sizes.p20);
const padding24 = EdgeInsets.all(Sizes.p24);
const padding32 = EdgeInsets.all(Sizes.p32);
const padding48 = EdgeInsets.all(Sizes.p48);
const padding44 = EdgeInsets.all(Sizes.p44);
const padding64 = EdgeInsets.all(Sizes.p64);

//custom
const introTextPadding = EdgeInsets.symmetric(horizontal: 50, vertical: 10);

class FontSize {
  static const double s10 = 10.0;
  static const double s12 = 12.0;
  static const double s14 = 14.0;
  static const double s16 = 16.0;
  static const double s17 = 17.0;
  static const double s18 = 18.0;
  static const double s20 = 20.0;
  static const double s24 = 24.0;
  static const double s32 = 32.0;
  static const double s36 = 36.0;
}

class AppMargin {
  static const double m8 = 8.0;
  static const double m12 = 12.0;
  static const double m14 = 14.0;
  static const double m16 = 16.0;
  static const double m18 = 18.0;
  static const double m20 = 20.0;
}

class AppSize {
  static const double s0 = 0;
  static const double s1_5 = 1.5;
  static const double s4 = 4.0;
  static const double s8 = 8.0;
  static const double s12 = 12.0;
  static const double s14 = 14.0;
  static const double s16 = 16.0;
  static const double s18 = 18.0;
  static const double s20 = 20.0;
  static const double s28 = 28.0;
  static const double s40 = 40.0;
  static const double s44 = 44.0;
  static const double s60 = 60.0;
  static const double s65 = 65.0;
  static const double s100 = 100.0;
  static const double s180 = 180.0;
}

class AppRadius {
  static const double socialLinkCircleAvatarRadius = 22;
  static const double searchCircularRadius = 40;
}
