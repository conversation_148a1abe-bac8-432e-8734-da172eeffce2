

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/bhatethumki/mobile_bhatethumki_page.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';

class BhatethumkiPage extends HookConsumerWidget {

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));

    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    return siteSetting.when(
        success: (setting, message) {

          return BaseScaffold(
              showLeftIcon: false,
              appbarText: setting?.name ?? "",
              showAction: prefs.getUserRole() >3 ? true : false,
              onActionClick:() async {
                final setting = await context.push("/location");
              },
              child: MobileBhateThumkiPage(setting!));


        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }

}