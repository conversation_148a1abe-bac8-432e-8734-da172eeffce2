import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/kawosoti/schema/site_one/provider/site_one_provider.dart';

import '../../../repository/abstract_site_repository.dart';
import '../../model/chart_model.dart';
import '../../model/resource.dart';

final chartOneProvider = StateNotifierProvider.family
    .autoDispose<ChartOneController, Resource<ChartModel>, String>((ref, req) {
  final repository = ref.read(siteOneRepositoryProvider);
  return ChartOneController(
      apiRepository: repository, date: DateTime.now(), siteId: req);
});

class ChartOneController extends StateNotifier<Resource<ChartModel>> {
  ChartOneController(
      {required this.apiRepository, required this.date, required this.siteId})
      : super(const Resource.unInitialized()) {
    getData(DateTime.now());
  }

  final ISiteFirestoreRepository apiRepository;
  final DateTime? date;
  final String siteId;

  Future<void> getData(DateTime dateTime) async {
    state = const Resource.loading();
    try {
      final response =
          await apiRepository.getChartData(siteId: siteId, date: dateTime);
      final t1List = <FlSpot>[];
      final t2List = <FlSpot>[];
      final t3List = <FlSpot>[];
      final t4List = <FlSpot>[];
      final t5List = <FlSpot>[];
      final chList = <FlSpot>[];
      final phList = <FlSpot>[];
      final ohList = <FlSpot>[];
      final bwList = <FlSpot>[];
      for (int i = 0; i < response.length; i++) {
        if (siteId != "site4History") {
          t1List
              .add(FlSpot(getMins(response[i]!.timestamp!), response[i]!.t1!));
        }

        if (siteId == "site1History" || siteId == "site3History") {
          t4List.add(
              FlSpot(getMins(response[i]!.timestamp!), response[i]?.t4 ?? 0.0));
        }
        if (siteId == "site1History" ||
            siteId == "site2History" ||
            siteId == "site3History") {
          t2List
              .add(FlSpot(getMins(response[i]!.timestamp!), response[i]!.t2!));
          t3List
              .add(FlSpot(getMins(response[i]!.timestamp!), response[i]!.t3!));
        }

        if (siteId == "site1History" ||
            siteId == "site2History" ||
            siteId == "site3History" ||
            siteId == "site6History" ||
            siteId == "site7History" ||
            siteId == "site8History") {
          chList
              .add(FlSpot(getMins(response[i]!.timestamp!), response[i]!.cl!));
          phList
              .add(FlSpot(getMins(response[i]!.timestamp!), response[i]!.ph!));
        }
        if (siteId == "site5History" ||
            siteId == "site6History" ||
            siteId == "site7History") {
          ohList.add(FlSpot(getMins(response[i]!.timestamp!),
              double.parse(response[i]!.oh!.toString())));
        }

        if (siteId == "site3History" ||
            siteId == "site4History" ||
            siteId == "site5History" ||
            siteId == "site6History" ||
            siteId == "site7History" ||
            siteId == "site8History") {
          bwList
              .add(FlSpot(getMins(response[i]!.timestamp!), response[i]!.bw!));
        }
      }
      state = Resource.success(
          ChartModel(
              t1List: t1List,
              t2List: t2List,
              t3List: t3List,
              t4List: t4List,
              t5List: t5List,
              chList: chList,
              phList: phList,
              bwList: bwList,
              ohList: ohList,
              minX: 0,
              minY: 0,
              maxX: 1440,
              maxY: 9),
          "");
    } catch (error) {
      state = Resource.error(error.toString());
    }
  }

  double getMins(DateTime date) {
    //print(date);
    return double.parse((date.hour * 60 + date.minute).toString());
  }
}
