
import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/chanakhel/system_one_page.dart';
import 'package:si/division1/homepage/chanakhel/system_two_page.dart';
import 'package:si/division1/homepage/pandubazaar/pandubazar_site_one_page.dart';
import 'package:si/division1/homepage/simkhola/simkhola_site_one_page.dart';
import 'package:si/division1/homepage/simkhola/simkhola_site_two_page.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/utils/app_colors.dart';

import '../../../../common/widgets/base_scaffold.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../constants/app_sizes.dart';
import '../../../../provider/auth_provider.dart';
import '../../../../services/shared_preferences_service.dart';
import '../../../provider/division_provider.dart';
import '../pandubazar_site_two_page.dart';
import 'web_pandubazar_site_one_page.dart';
import 'web_pandubazar_site_two_page.dart';

class WebPandubazarPage extends HookConsumerWidget{

  WebPandubazarPage(this.setting);

  DivisionSettingModel setting;

  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    //final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    return siteSetting.when(
        success: (setting, message) {
          return BaseScaffold(
              showLeftIcon: false,
              showAppBar: false,
              appbarText: setting?.name ?? "",
              showAction: true,
              onActionClick:() async {
                final bool didRequestSignOut =
                    await showAlertDialog(
                      context: context,
                      title: 'Logout',
                      content:
                      'Are you sure you want to logout?',
                      cancelActionText: 'Cancel',
                      defaultActionText: 'Yes',
                    ) ??
                        false;
                if (didRequestSignOut == true) {
                  try {
                    EasyLoading.show(status: 'Logging Out');
                    await ref
                        .read(authRepositoryProvider)
                        .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                    await ref
                        .read(authRepositoryProvider)
                        .signOut();
                    // MyApp.of(context).authService.authenticated = true;
                    // onLoginCallback?.call(true);
                    // AutoRouter.of(context).push(const DashboardRouter());
                    EasyLoading.dismiss();
                    context.replace('/login');
                  } catch (err) {
                    print(err);
                    // debugPrint("Error :$err");
                  }
                }
              },
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFF87CEFA), // Light Blue (Sky color)
                      Color(0xFFE0FFFF),
                      Color(0xFF87CEFA), // Light Blue (Sky color)
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                        child: WebPandubazarSiteOnePage(setting!)),
                    Container(
                      height: double.infinity,
                      width: 1,
                      color: Colors.black12,
                    ),
                    gapW8,
                    Container(
                      height: double.infinity,
                      width: 1,
                      color: Colors.black12,
                    ),
                    Expanded(
                      flex: 1,
                        child: WebPandubazarSiteTwoPage(setting)),
                  ],
                ),
              )
          );
        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }


}