import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/division1/homepage/filter_motor_widget.dart';
import 'package:si/division1/homepage/valve_widget.dart';
import 'package:si/division1/homepage/widgets/source_widget.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../constants/app_sizes.dart';
import '../model/motor_model.dart';
import '../provider/division_provider.dart';
import 'display.dart';
import 'filter_plant.dart';
import 'new_tank_unit.dart';

class KhadkaGauPage extends HookConsumerWidget {
  const KhadkaGauPage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor1State = useState<MotorModel>(MotorModel());
    final motor2State = useState<MotorModel>(MotorModel());
    final motor3State = useState<MotorModel>(MotorModel());
    final motor4State = useState<MotorModel>(MotorModel());
    final motor5State = useState<MotorModel>(MotorModel());
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor1'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor1State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor1State.value.device_status == motor1State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor2'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor2State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor2State.value.device_status == motor2State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor3'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor3State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                device_status: datasnapshot['device_status'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor3State.value.device_status == motor3State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor4'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor4State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                device_status: datasnapshot['device_status'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor4State.value.device_status == motor4State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor5'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor5State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                device_status: datasnapshot['device_status'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor5State.value.device_status == motor5State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return siteSetting.when(
        data: (data) {
          prefs.setMotorNumber(data?.motor_number ?? 1);
          prefs.setTankNumber(data?.tank_number ?? 1);
          prefs.setTankName( "tankone",data?.tank10?.name ?? "Tank 1");
          prefs.setTankName( "tanktwo",data?.tank20?.name ?? "Tank 2");
          return BaseScaffold(
            appbarText: data?.name ?? "Khadka Gau",
            showLeftIcon: false,
            showAction: false,
            onActionClick: () async {
              EasyLoading.show();
              final response = await ref
                  .read(divisionRepositoryProvider)
                  .getSiteSetting(prefs.getSiteId());
              ref
                  .read(sharedPreferencesServiceProvider)
                  .setSiteNae(response?.name ?? '');
              EasyLoading.dismiss();
              context.push("/home/<USER>", extra: response);
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SourceWidget( "Source"),
                  Row(
                    children: [
                      gapW8,
                      Column(
                        children: [
                          Container(
                            height: 20,
                            color: Colors.blueAccent,
                            width: 20,
                          ),
                          Container(
                            height: 200,
                            width: 110,
                            child: DisplayWidget(
                              motorName: data!.motor10?.name ?? 'Motor 1',
                              motorModel: motor1State.value,
                              motorState: motor1State.value.device_status,
                              motorId: 'motor1',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor1State.value.voltage,
                              motorAmps: motor1State.value.motorAmps!,
                              motorSettingModel: data!.motor10!,
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          )
                        ],
                      ),
                      Expanded(
                        child: Container(
                          height: 20,
                          color: Colors.blueAccent,
                        ),
                      ),
                      Container(
                        height: 180,
                        width: 110,
                        child: FilterDisplayWidget(
                          filterName: data!.filter10?.name ?? 'Filter 1',
                          roleType: ref.read(userState).role ?? 1,
                          filterId: 'filter1',
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 20,
                          color: Colors.blueAccent,
                        ),
                      ),
                      Column(
                        children: [
                          Container(
                            width: 20,
                            height: 60,
                            color: Colors.transparent,
                          ),
                          Container(
                            height: 100,
                            width: 80,
                            child: FilterDisplayWidget(
                              showFilter: false,
                              filterName: data!.filter20?.name ?? 'Filter 2',
                              roleType: ref.read(userState).role ?? 1,
                              filterId: 'filter2',
                            ),
                          ),
                          Container(
                            width: 20,
                            height: 70,
                            color: Colors.blueAccent,
                          ),
                        ],
                      ),
                      gapW8,
                    ],
                  ),
                  Container(
                    height: 20,
                    width: double.infinity,
                    color: Colors.blueAccent,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      gapW16,
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            height: 80,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          Container(
                            height: 200,
                            width: MediaQuery.of(context).size.width / 2.5 ,
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.black)),
                            child: TankBlockWidget(
                                ref.read(userState).role ?? 1,
                                'tank1',
                                data!.tank10!,
                                170,
                                MediaQuery.of(context).size.width / 2.5  - 10),
                          ),
                          Container(
                            height: 40,
                            width: 20,
                            color: Colors.transparent,
                          ),

                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:120.0),
                        child: Container(
                          height: 20,
                          width: 60,
                          color: Colors.blueAccent,
                        ),
                      ),
                      Column(
                        children: [
                          Container(
                            height: 10,
                            width: 20,
                            color: Colors.transparent,
                          ),
                          Container(
                            height: 200,
                            width: 130,
                            child: DisplayWidget(
                              motorName: data!.motor20?.name ?? 'Motor 2',
                              motorModel: motor2State.value,
                              motorState: motor2State.value.device_status,
                              motorId: 'motor2',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor2State.value.voltage,
                              motorAmps: motor2State.value.motorAmps!,
                              motorSettingModel: data!.motor20!,
                            ),
                          ),
                          Container(
                            height: 40,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          Container(
                            height: 200,
                            width: MediaQuery.of(context).size.width / 3 ,
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.black)),
                            child: TankBlockWidget(
                                ref.read(userState).role ?? 1,
                                'tank2',
                                data!.tank20!,
                                170,
                                MediaQuery.of(context).size.width / 3  - 10),
                          ),
                          Container(
                            height: 20,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                        ],
                      ),
                    ],
                  ),
                  Container(
                    height: 20,
                    width: double.infinity,
                    color: Colors.blueAccent,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: [
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          Container(
                            height: 200,
                            width: 110,
                            child: DisplayWidget(
                              motorName: data!.motor30?.name ?? 'Motor 3',
                              motorModel: motor3State.value,
                              motorState: motor3State.value.device_status,
                              motorId: 'motor3',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor3State.value.voltage,
                              motorAmps: motor3State.value.motorAmps!,
                              motorSettingModel: data!.motor30!,
                            ),
                          ),
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          Container(
                            height: 200,
                            width: 110,
                            child: DisplayWidget(
                              motorName: data!.motor40?.name ?? 'Motor 4',
                              motorModel: motor4State.value,
                              motorState: motor4State.value.device_status,
                              motorId: 'motor4',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor4State.value.voltage,
                              motorAmps: motor4State.value.motorAmps!,
                              motorSettingModel: data!.motor40!,
                            ),
                          ),
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                          Container(
                            height: 200,
                            width: 110,
                            child: DisplayWidget(
                              motorName: data!.motor50?.name ?? 'Motor 5',
                              motorModel: motor5State.value,
                              motorState: motor5State.value.device_status,
                              motorId: 'motor5',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor5State.value.voltage,
                              motorAmps: motor5State.value.motorAmps!,
                              motorSettingModel: data?.motor50!,
                            ),
                          ),
                          Container(
                            height: 30,
                            width: 20,
                            color: Colors.blueAccent,
                          ),
                        ],
                      ),

                    ],
                  ),
                  Container(
                    height: 60,
                    width: double.infinity,
                    color: Colors.blueAccent,
                    child: Center(child: const Text("Distribution Network", style: TextStyle(color: Colors.white),)),
                  ),
                ],
              ),
            ),
          );
        },
        error: (er, st) {
          return Container();
        },
        loading: () => const LoadingIndicator());
  }
}
