// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tank_setting_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TankSettingModelImpl _$$TankSettingModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TankSettingModelImpl(
      level_sensor_enable: json['level_sensor_enable'] as bool? ?? true,
      float_enable: json['float_enable'] as bool? ?? true,
      max_height: (json['max_height'] as num?)?.toInt() ?? 400,
      min_height: (json['min_height'] as num?)?.toInt() ?? 50,
      actual_height: (json['actual_height'] as num?)?.toInt() ?? 450,
      offset: (json['offset'] as num?)?.toInt() ?? 0,
      is_active: json['is_active'] as bool? ?? true,
      is_bw: json['is_bw'] as bool? ?? false,
      upload_time: (json['upload_time'] as num?)?.toInt() ?? 15,
      name: json['name'] as String? ?? '',
    );

Map<String, dynamic> _$$TankSettingModelImplToJson(
        _$TankSettingModelImpl instance) =>
    <String, dynamic>{
      'level_sensor_enable': instance.level_sensor_enable,
      'float_enable': instance.float_enable,
      'max_height': instance.max_height,
      'min_height': instance.min_height,
      'actual_height': instance.actual_height,
      'offset': instance.offset,
      'is_active': instance.is_active,
      'is_bw': instance.is_bw,
      'upload_time': instance.upload_time,
      'name': instance.name,
    };

_$BorewellSettingModelImpl _$$BorewellSettingModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BorewellSettingModelImpl(
      height: (json['height'] as num?)?.toDouble() ?? 0.0,
      name: json['name'] as String? ?? "",
      is_active: json['is_active'] as bool? ?? true,
      upload_time: (json['upload_time'] as num?)?.toInt() ?? 15,
    );

Map<String, dynamic> _$$BorewellSettingModelImplToJson(
        _$BorewellSettingModelImpl instance) =>
    <String, dynamic>{
      'height': instance.height,
      'name': instance.name,
      'is_active': instance.is_active,
      'upload_time': instance.upload_time,
    };
