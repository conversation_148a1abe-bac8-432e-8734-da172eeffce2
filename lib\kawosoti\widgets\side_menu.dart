import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/assets_manager.dart';
import 'package:si/kawosoti/schema/site_one/provider/site_one_provider.dart';
import 'package:si/kawosoti/widgets/expansion_widget.dart';
import 'package:si/kawosoti/widgets/sub_tile_widget.dart';
import 'package:si/utils/app_colors.dart';

import '../../common/widgets/widgets.dart';

class SideMenu extends HookConsumerWidget {
  const SideMenu({Key? key, required this.navigationShell}) : super(key: key);

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteName = ref.watch(siteNameProvider);
    return siteName.when(
        data: (data) {
          return getSideMenu(
              siteName.value?.site1 ?? "Site 1",
              siteName.value?.site2 ?? "Site 2",
              siteName.value?.site3 ?? "Site 3",
              siteName.value?.site4 ?? "Site 4",
              siteName.value?.site5 ?? "Site 5",
              siteName.value?.site6 ?? "Site 6",
              siteName.value?.site7 ?? "Site 7",
              siteName.value?.site8 ?? "Site 8");
        },
        error: (er, st) {
          return getSideMenu("Site 1", "Site 2", "Site 3", "Site 4", "Site 5",
              "Site 6", "Site 7", "Site 8");
        },
        loading: () => Center(child: CircularProgressIndicator()));
  }

  Widget getSideMenu(String site1, String site2, String site3, String site4,
      String site5, String site6, String site7, String site8) {
    return AnimatedContainer(
        width: 254,
        curve: Curves.easeInOut,
        color: AppColors.sideMenuBg,
        duration: const Duration(milliseconds: 200),
        child: Column(
          children: [
            const SizedBox(
              height: 50,
            ),
            Image.asset(
              AppAssets.instance.appLogo,
              height: 80,
              width: 80,
            ),
            const SizedBox(
              height: 50,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ExpansionWidget(
                        title: "Schema",
                        icon: Icon(
                          Icons.block_outlined,
                          color: AppColors.themeColor,
                        ),
                        children: [
                          SubTileWidget(
                              title: '\u2022' + '    ${site1}',
                              isActive: 0 == navigationShell.currentIndex,
                              onTap: () {

                                navigationShell.goBranch(
                                  0,
                                  initialLocation:
                                      0 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site2}',
                              isActive: 1 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  1,
                                  initialLocation:
                                      1 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site3}',
                              isActive: 2 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  2,
                                  initialLocation:
                                      2 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site4}',
                              isActive: 3 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  3,
                                  initialLocation:
                                      3 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site5}',
                              isActive: 4 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  4,
                                  initialLocation:
                                      4 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site6}',
                              isActive: 5 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  5,
                                  initialLocation:
                                      5 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site7}',
                              isActive: 6 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  6,
                                  initialLocation:
                                      6 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site8}',
                              isActive: 7 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  7,
                                  initialLocation:
                                      7 == navigationShell.currentIndex,
                                );
                              })
                        ]),
                    ExpansionWidget(
                        title: "History",
                        icon: Icon(
                          Icons.history,
                          color: AppColors.themeColor,
                        ),
                        children: [
                          SubTileWidget(
                              title: '\u2022' + '    ${site1}',
                              isActive: 8 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  8,
                                  initialLocation:
                                      8 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site2}',
                              isActive: 9 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  9,
                                  initialLocation:
                                      9 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site3}',
                              isActive: 10 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  10,
                                  initialLocation:
                                      10 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site4}',
                              isActive: 11 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  11,
                                  initialLocation:
                                      11 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site5}',
                              isActive: 12 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  12,
                                  initialLocation:
                                      12 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site6}',
                              isActive: 13 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  13,
                                  initialLocation:
                                      13 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site7}',
                              isActive: 14 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  14,
                                  initialLocation:
                                      14 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site8}',
                              isActive: 15 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  15,
                                  initialLocation:
                                      15 == navigationShell.currentIndex,
                                );
                              }),
                        ]),
                    ExpansionWidget(
                        title: "Charts",
                        icon: Icon(
                          Icons.add_chart_outlined,
                          color: AppColors.themeColor,
                        ),
                        children: [
                          SubTileWidget(
                              title: '\u2022' + '    ${site1}',
                              isActive: 16 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  16,
                                  initialLocation:
                                      16 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site2}',
                              isActive: 17 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  17,
                                  initialLocation:
                                      17 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site3}',
                              isActive: 18 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  18,
                                  initialLocation:
                                      18 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site4}',
                              isActive: 19 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  19,
                                  initialLocation:
                                      19 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site5}',
                              isActive: 20 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  20,
                                  initialLocation:
                                      20 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site6}',
                              isActive: 21 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  21,
                                  initialLocation:
                                      21 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site7}',
                              isActive: 22 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  22,
                                  initialLocation:
                                      22 == navigationShell.currentIndex,
                                );
                              }),
                          SubTileWidget(
                              title: '\u2022' + '    ${site8}',
                              isActive: 23 == navigationShell.currentIndex,
                              onTap: () {
                                navigationShell.goBranch(
                                  23,
                                  initialLocation:
                                      23 == navigationShell.currentIndex,
                                );
                              })
                        ]),
                    Consumer(builder: (context, ref, child) {
                      return ListTile(
                        leading: Icon(
                          Icons.settings,
                          color: AppColors.themeColor,
                        ),
                        title: Text(
                          'Settings',
                          style: TextStyle(
                              color: 24 == navigationShell.currentIndex ? AppColors.themeColor :
                              AppColors.onSideMenu, fontSize: 16),
                        ),
                        onTap: () async {
                          navigationShell.goBranch(
                            24,
                            initialLocation: 24 == navigationShell.currentIndex,
                          );
                        },
                      );
                    }),
                    Consumer(builder: (context, ref, child) {
                      return ListTile(
                        leading: Icon(
                          Icons.person,
                          color: AppColors.themeColor,
                        ),
                        title: Text(
                          'Users',
                          style: TextStyle(
                              color: 25 == navigationShell.currentIndex ? AppColors.themeColor :
                              AppColors.onSideMenu, fontSize: 16),
                        ),
                        onTap: () async {
                          navigationShell.goBranch(
                            25,
                            initialLocation: 25 == navigationShell.currentIndex,
                          );
                        },
                      );
                    }),
                    Consumer(builder: (context, ref, child) {
                      return ListTile(
                        leading: Icon(
                          Icons.logout,
                          color: AppColors.themeColor,
                        ),
                        title: Text(
                          'LogOut',
                          style: TextStyle(
                              color: AppColors.onSideMenu, fontSize: 16),
                        ),
                        onTap: () async {
                          final bool didRequestSignOut = await showAlertDialog(
                                context: context,
                                title: 'Logout',
                                content: 'Are you sure you want to logout?',
                                cancelActionText: 'Cancel',
                                defaultActionText: 'Logout',
                              ) ??
                              false;
                          if (didRequestSignOut == true) {
                            try {} catch (err) {
                              // debugPrint("Error :$err");
                            }
                          }
                        },
                      );
                    }),
                  ],
                ),
              ),
            )
          ],
        ));
  }
}
