// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'division_setting_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DivisionSettingModel _$DivisionSettingModelFromJson(Map<String, dynamic> json) {
  return _DivisionSettingModel.fromJson(json);
}

/// @nodoc
mixin _$DivisionSettingModel {
  int get motor_number => throw _privateConstructorUsedError;
  int get tank_number => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  TankSettingModel? get tank10 => throw _privateConstructorUsedError;
  BorewellSettingModel? get bw10 => throw _privateConstructorUsedError;
  TankSettingModel? get tank12 => throw _privateConstructorUsedError;
  TankSettingModel? get tank13 => throw _privateConstructorUsedError;
  TankSettingModel? get tank14 => throw _privateConstructorUsedError;
  SensorSettingModel? get sensor10 => throw _privateConstructorUsedError;
  SensorSettingModel? get sensor20 => throw _privateConstructorUsedError;
  SensorSettingModel? get sensor21 => throw _privateConstructorUsedError;
  SensorSettingModel? get sensor11 => throw _privateConstructorUsedError;
  SensorSettingModel? get sensor32 => throw _privateConstructorUsedError;
  List<int>? get tank_id => throw _privateConstructorUsedError;
  List<int>? get motor_id => throw _privateConstructorUsedError;
  List<int>? get bw_id => throw _privateConstructorUsedError;
  List<int>? get filter_id => throw _privateConstructorUsedError;
  List<int>? get valve_id => throw _privateConstructorUsedError;
  TankSettingModel? get tank11 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor10 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor11 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor12 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor13 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor14 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve10 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve11 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve12 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve13 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve14 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve20 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve21 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve22 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve30 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve31 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve32 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve33 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve40 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve41 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve42 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve43 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve44 => throw _privateConstructorUsedError;
  ValveSettingModel? get valve50 => throw _privateConstructorUsedError;
  MotorSettingModel? get filter10 => throw _privateConstructorUsedError;
  MotorSettingModel? get filter11 => throw _privateConstructorUsedError;
  MotorSettingModel? get filter20 => throw _privateConstructorUsedError;
  MotorSettingModel? get filter30 => throw _privateConstructorUsedError;
  TankSettingModel? get tank20 => throw _privateConstructorUsedError;
  TankSettingModel? get tank21 => throw _privateConstructorUsedError;
  TankSettingModel? get tank22 => throw _privateConstructorUsedError;
  int? get valveNumber => throw _privateConstructorUsedError;
  bool? get has_valve => throw _privateConstructorUsedError;
  MotorSettingModel? get motor20 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor21 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor22 => throw _privateConstructorUsedError;
  TankSettingModel? get tank30 => throw _privateConstructorUsedError;
  TankSettingModel? get tank31 => throw _privateConstructorUsedError;
  TankSettingModel? get tank32 => throw _privateConstructorUsedError;
  TankSettingModel? get tank40 => throw _privateConstructorUsedError;
  TankSettingModel? get tank41 => throw _privateConstructorUsedError;
  TankSettingModel? get tank50 => throw _privateConstructorUsedError;
  TankSettingModel? get tank51 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor30 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor31 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor40 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor41 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor50 => throw _privateConstructorUsedError;
  MotorSettingModel? get motor51 => throw _privateConstructorUsedError;
  LocationModel? get location => throw _privateConstructorUsedError;
  String? get installed_at => throw _privateConstructorUsedError;
  String? get bom => throw _privateConstructorUsedError;
  bool? get is_customized => throw _privateConstructorUsedError;

  /// Serializes this DivisionSettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DivisionSettingModelCopyWith<DivisionSettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DivisionSettingModelCopyWith<$Res> {
  factory $DivisionSettingModelCopyWith(DivisionSettingModel value,
          $Res Function(DivisionSettingModel) then) =
      _$DivisionSettingModelCopyWithImpl<$Res, DivisionSettingModel>;
  @useResult
  $Res call(
      {int motor_number,
      int tank_number,
      String name,
      String password,
      String id,
      TankSettingModel? tank10,
      BorewellSettingModel? bw10,
      TankSettingModel? tank12,
      TankSettingModel? tank13,
      TankSettingModel? tank14,
      SensorSettingModel? sensor10,
      SensorSettingModel? sensor20,
      SensorSettingModel? sensor21,
      SensorSettingModel? sensor11,
      SensorSettingModel? sensor32,
      List<int>? tank_id,
      List<int>? motor_id,
      List<int>? bw_id,
      List<int>? filter_id,
      List<int>? valve_id,
      TankSettingModel? tank11,
      MotorSettingModel? motor10,
      MotorSettingModel? motor11,
      MotorSettingModel? motor12,
      MotorSettingModel? motor13,
      MotorSettingModel? motor14,
      ValveSettingModel? valve10,
      ValveSettingModel? valve11,
      ValveSettingModel? valve12,
      ValveSettingModel? valve13,
      ValveSettingModel? valve14,
      ValveSettingModel? valve20,
      ValveSettingModel? valve21,
      ValveSettingModel? valve22,
      ValveSettingModel? valve30,
      ValveSettingModel? valve31,
      ValveSettingModel? valve32,
      ValveSettingModel? valve33,
      ValveSettingModel? valve40,
      ValveSettingModel? valve41,
      ValveSettingModel? valve42,
      ValveSettingModel? valve43,
      ValveSettingModel? valve44,
      ValveSettingModel? valve50,
      MotorSettingModel? filter10,
      MotorSettingModel? filter11,
      MotorSettingModel? filter20,
      MotorSettingModel? filter30,
      TankSettingModel? tank20,
      TankSettingModel? tank21,
      TankSettingModel? tank22,
      int? valveNumber,
      bool? has_valve,
      MotorSettingModel? motor20,
      MotorSettingModel? motor21,
      MotorSettingModel? motor22,
      TankSettingModel? tank30,
      TankSettingModel? tank31,
      TankSettingModel? tank32,
      TankSettingModel? tank40,
      TankSettingModel? tank41,
      TankSettingModel? tank50,
      TankSettingModel? tank51,
      MotorSettingModel? motor30,
      MotorSettingModel? motor31,
      MotorSettingModel? motor40,
      MotorSettingModel? motor41,
      MotorSettingModel? motor50,
      MotorSettingModel? motor51,
      LocationModel? location,
      String? installed_at,
      String? bom,
      bool? is_customized});

  $TankSettingModelCopyWith<$Res>? get tank10;
  $BorewellSettingModelCopyWith<$Res>? get bw10;
  $TankSettingModelCopyWith<$Res>? get tank12;
  $TankSettingModelCopyWith<$Res>? get tank13;
  $TankSettingModelCopyWith<$Res>? get tank14;
  $SensorSettingModelCopyWith<$Res>? get sensor10;
  $SensorSettingModelCopyWith<$Res>? get sensor20;
  $SensorSettingModelCopyWith<$Res>? get sensor21;
  $SensorSettingModelCopyWith<$Res>? get sensor11;
  $SensorSettingModelCopyWith<$Res>? get sensor32;
  $TankSettingModelCopyWith<$Res>? get tank11;
  $MotorSettingModelCopyWith<$Res>? get motor10;
  $MotorSettingModelCopyWith<$Res>? get motor11;
  $MotorSettingModelCopyWith<$Res>? get motor12;
  $MotorSettingModelCopyWith<$Res>? get motor13;
  $MotorSettingModelCopyWith<$Res>? get motor14;
  $ValveSettingModelCopyWith<$Res>? get valve10;
  $ValveSettingModelCopyWith<$Res>? get valve11;
  $ValveSettingModelCopyWith<$Res>? get valve12;
  $ValveSettingModelCopyWith<$Res>? get valve13;
  $ValveSettingModelCopyWith<$Res>? get valve14;
  $ValveSettingModelCopyWith<$Res>? get valve20;
  $ValveSettingModelCopyWith<$Res>? get valve21;
  $ValveSettingModelCopyWith<$Res>? get valve22;
  $ValveSettingModelCopyWith<$Res>? get valve30;
  $ValveSettingModelCopyWith<$Res>? get valve31;
  $ValveSettingModelCopyWith<$Res>? get valve32;
  $ValveSettingModelCopyWith<$Res>? get valve33;
  $ValveSettingModelCopyWith<$Res>? get valve40;
  $ValveSettingModelCopyWith<$Res>? get valve41;
  $ValveSettingModelCopyWith<$Res>? get valve42;
  $ValveSettingModelCopyWith<$Res>? get valve43;
  $ValveSettingModelCopyWith<$Res>? get valve44;
  $ValveSettingModelCopyWith<$Res>? get valve50;
  $MotorSettingModelCopyWith<$Res>? get filter10;
  $MotorSettingModelCopyWith<$Res>? get filter11;
  $MotorSettingModelCopyWith<$Res>? get filter20;
  $MotorSettingModelCopyWith<$Res>? get filter30;
  $TankSettingModelCopyWith<$Res>? get tank20;
  $TankSettingModelCopyWith<$Res>? get tank21;
  $TankSettingModelCopyWith<$Res>? get tank22;
  $MotorSettingModelCopyWith<$Res>? get motor20;
  $MotorSettingModelCopyWith<$Res>? get motor21;
  $MotorSettingModelCopyWith<$Res>? get motor22;
  $TankSettingModelCopyWith<$Res>? get tank30;
  $TankSettingModelCopyWith<$Res>? get tank31;
  $TankSettingModelCopyWith<$Res>? get tank32;
  $TankSettingModelCopyWith<$Res>? get tank40;
  $TankSettingModelCopyWith<$Res>? get tank41;
  $TankSettingModelCopyWith<$Res>? get tank50;
  $TankSettingModelCopyWith<$Res>? get tank51;
  $MotorSettingModelCopyWith<$Res>? get motor30;
  $MotorSettingModelCopyWith<$Res>? get motor31;
  $MotorSettingModelCopyWith<$Res>? get motor40;
  $MotorSettingModelCopyWith<$Res>? get motor41;
  $MotorSettingModelCopyWith<$Res>? get motor50;
  $MotorSettingModelCopyWith<$Res>? get motor51;
  $LocationModelCopyWith<$Res>? get location;
}

/// @nodoc
class _$DivisionSettingModelCopyWithImpl<$Res,
        $Val extends DivisionSettingModel>
    implements $DivisionSettingModelCopyWith<$Res> {
  _$DivisionSettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? motor_number = null,
    Object? tank_number = null,
    Object? name = null,
    Object? password = null,
    Object? id = null,
    Object? tank10 = freezed,
    Object? bw10 = freezed,
    Object? tank12 = freezed,
    Object? tank13 = freezed,
    Object? tank14 = freezed,
    Object? sensor10 = freezed,
    Object? sensor20 = freezed,
    Object? sensor21 = freezed,
    Object? sensor11 = freezed,
    Object? sensor32 = freezed,
    Object? tank_id = freezed,
    Object? motor_id = freezed,
    Object? bw_id = freezed,
    Object? filter_id = freezed,
    Object? valve_id = freezed,
    Object? tank11 = freezed,
    Object? motor10 = freezed,
    Object? motor11 = freezed,
    Object? motor12 = freezed,
    Object? motor13 = freezed,
    Object? motor14 = freezed,
    Object? valve10 = freezed,
    Object? valve11 = freezed,
    Object? valve12 = freezed,
    Object? valve13 = freezed,
    Object? valve14 = freezed,
    Object? valve20 = freezed,
    Object? valve21 = freezed,
    Object? valve22 = freezed,
    Object? valve30 = freezed,
    Object? valve31 = freezed,
    Object? valve32 = freezed,
    Object? valve33 = freezed,
    Object? valve40 = freezed,
    Object? valve41 = freezed,
    Object? valve42 = freezed,
    Object? valve43 = freezed,
    Object? valve44 = freezed,
    Object? valve50 = freezed,
    Object? filter10 = freezed,
    Object? filter11 = freezed,
    Object? filter20 = freezed,
    Object? filter30 = freezed,
    Object? tank20 = freezed,
    Object? tank21 = freezed,
    Object? tank22 = freezed,
    Object? valveNumber = freezed,
    Object? has_valve = freezed,
    Object? motor20 = freezed,
    Object? motor21 = freezed,
    Object? motor22 = freezed,
    Object? tank30 = freezed,
    Object? tank31 = freezed,
    Object? tank32 = freezed,
    Object? tank40 = freezed,
    Object? tank41 = freezed,
    Object? tank50 = freezed,
    Object? tank51 = freezed,
    Object? motor30 = freezed,
    Object? motor31 = freezed,
    Object? motor40 = freezed,
    Object? motor41 = freezed,
    Object? motor50 = freezed,
    Object? motor51 = freezed,
    Object? location = freezed,
    Object? installed_at = freezed,
    Object? bom = freezed,
    Object? is_customized = freezed,
  }) {
    return _then(_value.copyWith(
      motor_number: null == motor_number
          ? _value.motor_number
          : motor_number // ignore: cast_nullable_to_non_nullable
              as int,
      tank_number: null == tank_number
          ? _value.tank_number
          : tank_number // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      tank10: freezed == tank10
          ? _value.tank10
          : tank10 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      bw10: freezed == bw10
          ? _value.bw10
          : bw10 // ignore: cast_nullable_to_non_nullable
              as BorewellSettingModel?,
      tank12: freezed == tank12
          ? _value.tank12
          : tank12 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank13: freezed == tank13
          ? _value.tank13
          : tank13 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank14: freezed == tank14
          ? _value.tank14
          : tank14 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      sensor10: freezed == sensor10
          ? _value.sensor10
          : sensor10 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor20: freezed == sensor20
          ? _value.sensor20
          : sensor20 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor21: freezed == sensor21
          ? _value.sensor21
          : sensor21 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor11: freezed == sensor11
          ? _value.sensor11
          : sensor11 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor32: freezed == sensor32
          ? _value.sensor32
          : sensor32 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      tank_id: freezed == tank_id
          ? _value.tank_id
          : tank_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      motor_id: freezed == motor_id
          ? _value.motor_id
          : motor_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      bw_id: freezed == bw_id
          ? _value.bw_id
          : bw_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      filter_id: freezed == filter_id
          ? _value.filter_id
          : filter_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      valve_id: freezed == valve_id
          ? _value.valve_id
          : valve_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      tank11: freezed == tank11
          ? _value.tank11
          : tank11 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      motor10: freezed == motor10
          ? _value.motor10
          : motor10 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor11: freezed == motor11
          ? _value.motor11
          : motor11 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor12: freezed == motor12
          ? _value.motor12
          : motor12 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor13: freezed == motor13
          ? _value.motor13
          : motor13 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor14: freezed == motor14
          ? _value.motor14
          : motor14 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      valve10: freezed == valve10
          ? _value.valve10
          : valve10 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve11: freezed == valve11
          ? _value.valve11
          : valve11 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve12: freezed == valve12
          ? _value.valve12
          : valve12 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve13: freezed == valve13
          ? _value.valve13
          : valve13 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve14: freezed == valve14
          ? _value.valve14
          : valve14 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve20: freezed == valve20
          ? _value.valve20
          : valve20 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve21: freezed == valve21
          ? _value.valve21
          : valve21 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve22: freezed == valve22
          ? _value.valve22
          : valve22 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve30: freezed == valve30
          ? _value.valve30
          : valve30 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve31: freezed == valve31
          ? _value.valve31
          : valve31 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve32: freezed == valve32
          ? _value.valve32
          : valve32 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve33: freezed == valve33
          ? _value.valve33
          : valve33 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve40: freezed == valve40
          ? _value.valve40
          : valve40 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve41: freezed == valve41
          ? _value.valve41
          : valve41 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve42: freezed == valve42
          ? _value.valve42
          : valve42 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve43: freezed == valve43
          ? _value.valve43
          : valve43 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve44: freezed == valve44
          ? _value.valve44
          : valve44 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve50: freezed == valve50
          ? _value.valve50
          : valve50 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      filter10: freezed == filter10
          ? _value.filter10
          : filter10 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      filter11: freezed == filter11
          ? _value.filter11
          : filter11 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      filter20: freezed == filter20
          ? _value.filter20
          : filter20 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      filter30: freezed == filter30
          ? _value.filter30
          : filter30 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      tank20: freezed == tank20
          ? _value.tank20
          : tank20 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank21: freezed == tank21
          ? _value.tank21
          : tank21 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank22: freezed == tank22
          ? _value.tank22
          : tank22 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      valveNumber: freezed == valveNumber
          ? _value.valveNumber
          : valveNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      has_valve: freezed == has_valve
          ? _value.has_valve
          : has_valve // ignore: cast_nullable_to_non_nullable
              as bool?,
      motor20: freezed == motor20
          ? _value.motor20
          : motor20 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor21: freezed == motor21
          ? _value.motor21
          : motor21 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor22: freezed == motor22
          ? _value.motor22
          : motor22 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      tank30: freezed == tank30
          ? _value.tank30
          : tank30 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank31: freezed == tank31
          ? _value.tank31
          : tank31 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank32: freezed == tank32
          ? _value.tank32
          : tank32 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank40: freezed == tank40
          ? _value.tank40
          : tank40 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank41: freezed == tank41
          ? _value.tank41
          : tank41 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank50: freezed == tank50
          ? _value.tank50
          : tank50 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank51: freezed == tank51
          ? _value.tank51
          : tank51 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      motor30: freezed == motor30
          ? _value.motor30
          : motor30 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor31: freezed == motor31
          ? _value.motor31
          : motor31 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor40: freezed == motor40
          ? _value.motor40
          : motor40 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor41: freezed == motor41
          ? _value.motor41
          : motor41 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor50: freezed == motor50
          ? _value.motor50
          : motor50 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor51: freezed == motor51
          ? _value.motor51
          : motor51 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as LocationModel?,
      installed_at: freezed == installed_at
          ? _value.installed_at
          : installed_at // ignore: cast_nullable_to_non_nullable
              as String?,
      bom: freezed == bom
          ? _value.bom
          : bom // ignore: cast_nullable_to_non_nullable
              as String?,
      is_customized: freezed == is_customized
          ? _value.is_customized
          : is_customized // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank10 {
    if (_value.tank10 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank10!, (value) {
      return _then(_value.copyWith(tank10: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BorewellSettingModelCopyWith<$Res>? get bw10 {
    if (_value.bw10 == null) {
      return null;
    }

    return $BorewellSettingModelCopyWith<$Res>(_value.bw10!, (value) {
      return _then(_value.copyWith(bw10: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank12 {
    if (_value.tank12 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank12!, (value) {
      return _then(_value.copyWith(tank12: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank13 {
    if (_value.tank13 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank13!, (value) {
      return _then(_value.copyWith(tank13: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank14 {
    if (_value.tank14 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank14!, (value) {
      return _then(_value.copyWith(tank14: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SensorSettingModelCopyWith<$Res>? get sensor10 {
    if (_value.sensor10 == null) {
      return null;
    }

    return $SensorSettingModelCopyWith<$Res>(_value.sensor10!, (value) {
      return _then(_value.copyWith(sensor10: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SensorSettingModelCopyWith<$Res>? get sensor20 {
    if (_value.sensor20 == null) {
      return null;
    }

    return $SensorSettingModelCopyWith<$Res>(_value.sensor20!, (value) {
      return _then(_value.copyWith(sensor20: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SensorSettingModelCopyWith<$Res>? get sensor21 {
    if (_value.sensor21 == null) {
      return null;
    }

    return $SensorSettingModelCopyWith<$Res>(_value.sensor21!, (value) {
      return _then(_value.copyWith(sensor21: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SensorSettingModelCopyWith<$Res>? get sensor11 {
    if (_value.sensor11 == null) {
      return null;
    }

    return $SensorSettingModelCopyWith<$Res>(_value.sensor11!, (value) {
      return _then(_value.copyWith(sensor11: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SensorSettingModelCopyWith<$Res>? get sensor32 {
    if (_value.sensor32 == null) {
      return null;
    }

    return $SensorSettingModelCopyWith<$Res>(_value.sensor32!, (value) {
      return _then(_value.copyWith(sensor32: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank11 {
    if (_value.tank11 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank11!, (value) {
      return _then(_value.copyWith(tank11: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor10 {
    if (_value.motor10 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor10!, (value) {
      return _then(_value.copyWith(motor10: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor11 {
    if (_value.motor11 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor11!, (value) {
      return _then(_value.copyWith(motor11: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor12 {
    if (_value.motor12 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor12!, (value) {
      return _then(_value.copyWith(motor12: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor13 {
    if (_value.motor13 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor13!, (value) {
      return _then(_value.copyWith(motor13: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor14 {
    if (_value.motor14 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor14!, (value) {
      return _then(_value.copyWith(motor14: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve10 {
    if (_value.valve10 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve10!, (value) {
      return _then(_value.copyWith(valve10: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve11 {
    if (_value.valve11 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve11!, (value) {
      return _then(_value.copyWith(valve11: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve12 {
    if (_value.valve12 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve12!, (value) {
      return _then(_value.copyWith(valve12: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve13 {
    if (_value.valve13 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve13!, (value) {
      return _then(_value.copyWith(valve13: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve14 {
    if (_value.valve14 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve14!, (value) {
      return _then(_value.copyWith(valve14: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve20 {
    if (_value.valve20 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve20!, (value) {
      return _then(_value.copyWith(valve20: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve21 {
    if (_value.valve21 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve21!, (value) {
      return _then(_value.copyWith(valve21: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve22 {
    if (_value.valve22 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve22!, (value) {
      return _then(_value.copyWith(valve22: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve30 {
    if (_value.valve30 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve30!, (value) {
      return _then(_value.copyWith(valve30: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve31 {
    if (_value.valve31 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve31!, (value) {
      return _then(_value.copyWith(valve31: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve32 {
    if (_value.valve32 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve32!, (value) {
      return _then(_value.copyWith(valve32: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve33 {
    if (_value.valve33 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve33!, (value) {
      return _then(_value.copyWith(valve33: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve40 {
    if (_value.valve40 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve40!, (value) {
      return _then(_value.copyWith(valve40: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve41 {
    if (_value.valve41 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve41!, (value) {
      return _then(_value.copyWith(valve41: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve42 {
    if (_value.valve42 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve42!, (value) {
      return _then(_value.copyWith(valve42: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve43 {
    if (_value.valve43 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve43!, (value) {
      return _then(_value.copyWith(valve43: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve44 {
    if (_value.valve44 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve44!, (value) {
      return _then(_value.copyWith(valve44: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValveSettingModelCopyWith<$Res>? get valve50 {
    if (_value.valve50 == null) {
      return null;
    }

    return $ValveSettingModelCopyWith<$Res>(_value.valve50!, (value) {
      return _then(_value.copyWith(valve50: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get filter10 {
    if (_value.filter10 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.filter10!, (value) {
      return _then(_value.copyWith(filter10: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get filter11 {
    if (_value.filter11 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.filter11!, (value) {
      return _then(_value.copyWith(filter11: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get filter20 {
    if (_value.filter20 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.filter20!, (value) {
      return _then(_value.copyWith(filter20: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get filter30 {
    if (_value.filter30 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.filter30!, (value) {
      return _then(_value.copyWith(filter30: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank20 {
    if (_value.tank20 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank20!, (value) {
      return _then(_value.copyWith(tank20: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank21 {
    if (_value.tank21 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank21!, (value) {
      return _then(_value.copyWith(tank21: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank22 {
    if (_value.tank22 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank22!, (value) {
      return _then(_value.copyWith(tank22: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor20 {
    if (_value.motor20 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor20!, (value) {
      return _then(_value.copyWith(motor20: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor21 {
    if (_value.motor21 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor21!, (value) {
      return _then(_value.copyWith(motor21: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor22 {
    if (_value.motor22 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor22!, (value) {
      return _then(_value.copyWith(motor22: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank30 {
    if (_value.tank30 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank30!, (value) {
      return _then(_value.copyWith(tank30: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank31 {
    if (_value.tank31 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank31!, (value) {
      return _then(_value.copyWith(tank31: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank32 {
    if (_value.tank32 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank32!, (value) {
      return _then(_value.copyWith(tank32: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank40 {
    if (_value.tank40 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank40!, (value) {
      return _then(_value.copyWith(tank40: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank41 {
    if (_value.tank41 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank41!, (value) {
      return _then(_value.copyWith(tank41: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank50 {
    if (_value.tank50 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank50!, (value) {
      return _then(_value.copyWith(tank50: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TankSettingModelCopyWith<$Res>? get tank51 {
    if (_value.tank51 == null) {
      return null;
    }

    return $TankSettingModelCopyWith<$Res>(_value.tank51!, (value) {
      return _then(_value.copyWith(tank51: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor30 {
    if (_value.motor30 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor30!, (value) {
      return _then(_value.copyWith(motor30: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor31 {
    if (_value.motor31 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor31!, (value) {
      return _then(_value.copyWith(motor31: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor40 {
    if (_value.motor40 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor40!, (value) {
      return _then(_value.copyWith(motor40: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor41 {
    if (_value.motor41 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor41!, (value) {
      return _then(_value.copyWith(motor41: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor50 {
    if (_value.motor50 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor50!, (value) {
      return _then(_value.copyWith(motor50: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MotorSettingModelCopyWith<$Res>? get motor51 {
    if (_value.motor51 == null) {
      return null;
    }

    return $MotorSettingModelCopyWith<$Res>(_value.motor51!, (value) {
      return _then(_value.copyWith(motor51: value) as $Val);
    });
  }

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationModelCopyWith<$Res>? get location {
    if (_value.location == null) {
      return null;
    }

    return $LocationModelCopyWith<$Res>(_value.location!, (value) {
      return _then(_value.copyWith(location: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DivisionSettingModelImplCopyWith<$Res>
    implements $DivisionSettingModelCopyWith<$Res> {
  factory _$$DivisionSettingModelImplCopyWith(_$DivisionSettingModelImpl value,
          $Res Function(_$DivisionSettingModelImpl) then) =
      __$$DivisionSettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int motor_number,
      int tank_number,
      String name,
      String password,
      String id,
      TankSettingModel? tank10,
      BorewellSettingModel? bw10,
      TankSettingModel? tank12,
      TankSettingModel? tank13,
      TankSettingModel? tank14,
      SensorSettingModel? sensor10,
      SensorSettingModel? sensor20,
      SensorSettingModel? sensor21,
      SensorSettingModel? sensor11,
      SensorSettingModel? sensor32,
      List<int>? tank_id,
      List<int>? motor_id,
      List<int>? bw_id,
      List<int>? filter_id,
      List<int>? valve_id,
      TankSettingModel? tank11,
      MotorSettingModel? motor10,
      MotorSettingModel? motor11,
      MotorSettingModel? motor12,
      MotorSettingModel? motor13,
      MotorSettingModel? motor14,
      ValveSettingModel? valve10,
      ValveSettingModel? valve11,
      ValveSettingModel? valve12,
      ValveSettingModel? valve13,
      ValveSettingModel? valve14,
      ValveSettingModel? valve20,
      ValveSettingModel? valve21,
      ValveSettingModel? valve22,
      ValveSettingModel? valve30,
      ValveSettingModel? valve31,
      ValveSettingModel? valve32,
      ValveSettingModel? valve33,
      ValveSettingModel? valve40,
      ValveSettingModel? valve41,
      ValveSettingModel? valve42,
      ValveSettingModel? valve43,
      ValveSettingModel? valve44,
      ValveSettingModel? valve50,
      MotorSettingModel? filter10,
      MotorSettingModel? filter11,
      MotorSettingModel? filter20,
      MotorSettingModel? filter30,
      TankSettingModel? tank20,
      TankSettingModel? tank21,
      TankSettingModel? tank22,
      int? valveNumber,
      bool? has_valve,
      MotorSettingModel? motor20,
      MotorSettingModel? motor21,
      MotorSettingModel? motor22,
      TankSettingModel? tank30,
      TankSettingModel? tank31,
      TankSettingModel? tank32,
      TankSettingModel? tank40,
      TankSettingModel? tank41,
      TankSettingModel? tank50,
      TankSettingModel? tank51,
      MotorSettingModel? motor30,
      MotorSettingModel? motor31,
      MotorSettingModel? motor40,
      MotorSettingModel? motor41,
      MotorSettingModel? motor50,
      MotorSettingModel? motor51,
      LocationModel? location,
      String? installed_at,
      String? bom,
      bool? is_customized});

  @override
  $TankSettingModelCopyWith<$Res>? get tank10;
  @override
  $BorewellSettingModelCopyWith<$Res>? get bw10;
  @override
  $TankSettingModelCopyWith<$Res>? get tank12;
  @override
  $TankSettingModelCopyWith<$Res>? get tank13;
  @override
  $TankSettingModelCopyWith<$Res>? get tank14;
  @override
  $SensorSettingModelCopyWith<$Res>? get sensor10;
  @override
  $SensorSettingModelCopyWith<$Res>? get sensor20;
  @override
  $SensorSettingModelCopyWith<$Res>? get sensor21;
  @override
  $SensorSettingModelCopyWith<$Res>? get sensor11;
  @override
  $SensorSettingModelCopyWith<$Res>? get sensor32;
  @override
  $TankSettingModelCopyWith<$Res>? get tank11;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor10;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor11;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor12;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor13;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor14;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve10;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve11;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve12;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve13;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve14;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve20;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve21;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve22;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve30;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve31;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve32;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve33;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve40;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve41;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve42;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve43;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve44;
  @override
  $ValveSettingModelCopyWith<$Res>? get valve50;
  @override
  $MotorSettingModelCopyWith<$Res>? get filter10;
  @override
  $MotorSettingModelCopyWith<$Res>? get filter11;
  @override
  $MotorSettingModelCopyWith<$Res>? get filter20;
  @override
  $MotorSettingModelCopyWith<$Res>? get filter30;
  @override
  $TankSettingModelCopyWith<$Res>? get tank20;
  @override
  $TankSettingModelCopyWith<$Res>? get tank21;
  @override
  $TankSettingModelCopyWith<$Res>? get tank22;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor20;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor21;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor22;
  @override
  $TankSettingModelCopyWith<$Res>? get tank30;
  @override
  $TankSettingModelCopyWith<$Res>? get tank31;
  @override
  $TankSettingModelCopyWith<$Res>? get tank32;
  @override
  $TankSettingModelCopyWith<$Res>? get tank40;
  @override
  $TankSettingModelCopyWith<$Res>? get tank41;
  @override
  $TankSettingModelCopyWith<$Res>? get tank50;
  @override
  $TankSettingModelCopyWith<$Res>? get tank51;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor30;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor31;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor40;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor41;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor50;
  @override
  $MotorSettingModelCopyWith<$Res>? get motor51;
  @override
  $LocationModelCopyWith<$Res>? get location;
}

/// @nodoc
class __$$DivisionSettingModelImplCopyWithImpl<$Res>
    extends _$DivisionSettingModelCopyWithImpl<$Res, _$DivisionSettingModelImpl>
    implements _$$DivisionSettingModelImplCopyWith<$Res> {
  __$$DivisionSettingModelImplCopyWithImpl(_$DivisionSettingModelImpl _value,
      $Res Function(_$DivisionSettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? motor_number = null,
    Object? tank_number = null,
    Object? name = null,
    Object? password = null,
    Object? id = null,
    Object? tank10 = freezed,
    Object? bw10 = freezed,
    Object? tank12 = freezed,
    Object? tank13 = freezed,
    Object? tank14 = freezed,
    Object? sensor10 = freezed,
    Object? sensor20 = freezed,
    Object? sensor21 = freezed,
    Object? sensor11 = freezed,
    Object? sensor32 = freezed,
    Object? tank_id = freezed,
    Object? motor_id = freezed,
    Object? bw_id = freezed,
    Object? filter_id = freezed,
    Object? valve_id = freezed,
    Object? tank11 = freezed,
    Object? motor10 = freezed,
    Object? motor11 = freezed,
    Object? motor12 = freezed,
    Object? motor13 = freezed,
    Object? motor14 = freezed,
    Object? valve10 = freezed,
    Object? valve11 = freezed,
    Object? valve12 = freezed,
    Object? valve13 = freezed,
    Object? valve14 = freezed,
    Object? valve20 = freezed,
    Object? valve21 = freezed,
    Object? valve22 = freezed,
    Object? valve30 = freezed,
    Object? valve31 = freezed,
    Object? valve32 = freezed,
    Object? valve33 = freezed,
    Object? valve40 = freezed,
    Object? valve41 = freezed,
    Object? valve42 = freezed,
    Object? valve43 = freezed,
    Object? valve44 = freezed,
    Object? valve50 = freezed,
    Object? filter10 = freezed,
    Object? filter11 = freezed,
    Object? filter20 = freezed,
    Object? filter30 = freezed,
    Object? tank20 = freezed,
    Object? tank21 = freezed,
    Object? tank22 = freezed,
    Object? valveNumber = freezed,
    Object? has_valve = freezed,
    Object? motor20 = freezed,
    Object? motor21 = freezed,
    Object? motor22 = freezed,
    Object? tank30 = freezed,
    Object? tank31 = freezed,
    Object? tank32 = freezed,
    Object? tank40 = freezed,
    Object? tank41 = freezed,
    Object? tank50 = freezed,
    Object? tank51 = freezed,
    Object? motor30 = freezed,
    Object? motor31 = freezed,
    Object? motor40 = freezed,
    Object? motor41 = freezed,
    Object? motor50 = freezed,
    Object? motor51 = freezed,
    Object? location = freezed,
    Object? installed_at = freezed,
    Object? bom = freezed,
    Object? is_customized = freezed,
  }) {
    return _then(_$DivisionSettingModelImpl(
      motor_number: null == motor_number
          ? _value.motor_number
          : motor_number // ignore: cast_nullable_to_non_nullable
              as int,
      tank_number: null == tank_number
          ? _value.tank_number
          : tank_number // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      tank10: freezed == tank10
          ? _value.tank10
          : tank10 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      bw10: freezed == bw10
          ? _value.bw10
          : bw10 // ignore: cast_nullable_to_non_nullable
              as BorewellSettingModel?,
      tank12: freezed == tank12
          ? _value.tank12
          : tank12 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank13: freezed == tank13
          ? _value.tank13
          : tank13 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank14: freezed == tank14
          ? _value.tank14
          : tank14 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      sensor10: freezed == sensor10
          ? _value.sensor10
          : sensor10 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor20: freezed == sensor20
          ? _value.sensor20
          : sensor20 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor21: freezed == sensor21
          ? _value.sensor21
          : sensor21 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor11: freezed == sensor11
          ? _value.sensor11
          : sensor11 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      sensor32: freezed == sensor32
          ? _value.sensor32
          : sensor32 // ignore: cast_nullable_to_non_nullable
              as SensorSettingModel?,
      tank_id: freezed == tank_id
          ? _value._tank_id
          : tank_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      motor_id: freezed == motor_id
          ? _value._motor_id
          : motor_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      bw_id: freezed == bw_id
          ? _value._bw_id
          : bw_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      filter_id: freezed == filter_id
          ? _value._filter_id
          : filter_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      valve_id: freezed == valve_id
          ? _value._valve_id
          : valve_id // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      tank11: freezed == tank11
          ? _value.tank11
          : tank11 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      motor10: freezed == motor10
          ? _value.motor10
          : motor10 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor11: freezed == motor11
          ? _value.motor11
          : motor11 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor12: freezed == motor12
          ? _value.motor12
          : motor12 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor13: freezed == motor13
          ? _value.motor13
          : motor13 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor14: freezed == motor14
          ? _value.motor14
          : motor14 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      valve10: freezed == valve10
          ? _value.valve10
          : valve10 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve11: freezed == valve11
          ? _value.valve11
          : valve11 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve12: freezed == valve12
          ? _value.valve12
          : valve12 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve13: freezed == valve13
          ? _value.valve13
          : valve13 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve14: freezed == valve14
          ? _value.valve14
          : valve14 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve20: freezed == valve20
          ? _value.valve20
          : valve20 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve21: freezed == valve21
          ? _value.valve21
          : valve21 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve22: freezed == valve22
          ? _value.valve22
          : valve22 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve30: freezed == valve30
          ? _value.valve30
          : valve30 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve31: freezed == valve31
          ? _value.valve31
          : valve31 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve32: freezed == valve32
          ? _value.valve32
          : valve32 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve33: freezed == valve33
          ? _value.valve33
          : valve33 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve40: freezed == valve40
          ? _value.valve40
          : valve40 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve41: freezed == valve41
          ? _value.valve41
          : valve41 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve42: freezed == valve42
          ? _value.valve42
          : valve42 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve43: freezed == valve43
          ? _value.valve43
          : valve43 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve44: freezed == valve44
          ? _value.valve44
          : valve44 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      valve50: freezed == valve50
          ? _value.valve50
          : valve50 // ignore: cast_nullable_to_non_nullable
              as ValveSettingModel?,
      filter10: freezed == filter10
          ? _value.filter10
          : filter10 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      filter11: freezed == filter11
          ? _value.filter11
          : filter11 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      filter20: freezed == filter20
          ? _value.filter20
          : filter20 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      filter30: freezed == filter30
          ? _value.filter30
          : filter30 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      tank20: freezed == tank20
          ? _value.tank20
          : tank20 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank21: freezed == tank21
          ? _value.tank21
          : tank21 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank22: freezed == tank22
          ? _value.tank22
          : tank22 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      valveNumber: freezed == valveNumber
          ? _value.valveNumber
          : valveNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      has_valve: freezed == has_valve
          ? _value.has_valve
          : has_valve // ignore: cast_nullable_to_non_nullable
              as bool?,
      motor20: freezed == motor20
          ? _value.motor20
          : motor20 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor21: freezed == motor21
          ? _value.motor21
          : motor21 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor22: freezed == motor22
          ? _value.motor22
          : motor22 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      tank30: freezed == tank30
          ? _value.tank30
          : tank30 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank31: freezed == tank31
          ? _value.tank31
          : tank31 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank32: freezed == tank32
          ? _value.tank32
          : tank32 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank40: freezed == tank40
          ? _value.tank40
          : tank40 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank41: freezed == tank41
          ? _value.tank41
          : tank41 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank50: freezed == tank50
          ? _value.tank50
          : tank50 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      tank51: freezed == tank51
          ? _value.tank51
          : tank51 // ignore: cast_nullable_to_non_nullable
              as TankSettingModel?,
      motor30: freezed == motor30
          ? _value.motor30
          : motor30 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor31: freezed == motor31
          ? _value.motor31
          : motor31 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor40: freezed == motor40
          ? _value.motor40
          : motor40 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor41: freezed == motor41
          ? _value.motor41
          : motor41 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor50: freezed == motor50
          ? _value.motor50
          : motor50 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      motor51: freezed == motor51
          ? _value.motor51
          : motor51 // ignore: cast_nullable_to_non_nullable
              as MotorSettingModel?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as LocationModel?,
      installed_at: freezed == installed_at
          ? _value.installed_at
          : installed_at // ignore: cast_nullable_to_non_nullable
              as String?,
      bom: freezed == bom
          ? _value.bom
          : bom // ignore: cast_nullable_to_non_nullable
              as String?,
      is_customized: freezed == is_customized
          ? _value.is_customized
          : is_customized // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DivisionSettingModelImpl implements _DivisionSettingModel {
  const _$DivisionSettingModelImpl(
      {this.motor_number = 0,
      this.tank_number = 0,
      this.name = "",
      this.password = "",
      this.id = "",
      this.tank10,
      this.bw10,
      this.tank12,
      this.tank13,
      this.tank14,
      this.sensor10,
      this.sensor20,
      this.sensor21,
      this.sensor11,
      this.sensor32,
      final List<int>? tank_id,
      final List<int>? motor_id,
      final List<int>? bw_id,
      final List<int>? filter_id,
      final List<int>? valve_id,
      this.tank11,
      this.motor10,
      this.motor11,
      this.motor12,
      this.motor13,
      this.motor14,
      this.valve10,
      this.valve11,
      this.valve12,
      this.valve13,
      this.valve14,
      this.valve20,
      this.valve21,
      this.valve22,
      this.valve30,
      this.valve31,
      this.valve32,
      this.valve33,
      this.valve40,
      this.valve41,
      this.valve42,
      this.valve43,
      this.valve44,
      this.valve50,
      this.filter10,
      this.filter11,
      this.filter20,
      this.filter30,
      this.tank20,
      this.tank21,
      this.tank22,
      this.valveNumber,
      this.has_valve,
      this.motor20,
      this.motor21,
      this.motor22,
      this.tank30,
      this.tank31,
      this.tank32,
      this.tank40,
      this.tank41,
      this.tank50,
      this.tank51,
      this.motor30,
      this.motor31,
      this.motor40,
      this.motor41,
      this.motor50,
      this.motor51,
      this.location,
      this.installed_at,
      this.bom,
      this.is_customized})
      : _tank_id = tank_id,
        _motor_id = motor_id,
        _bw_id = bw_id,
        _filter_id = filter_id,
        _valve_id = valve_id;

  factory _$DivisionSettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DivisionSettingModelImplFromJson(json);

  @override
  @JsonKey()
  final int motor_number;
  @override
  @JsonKey()
  final int tank_number;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String password;
  @override
  @JsonKey()
  final String id;
  @override
  final TankSettingModel? tank10;
  @override
  final BorewellSettingModel? bw10;
  @override
  final TankSettingModel? tank12;
  @override
  final TankSettingModel? tank13;
  @override
  final TankSettingModel? tank14;
  @override
  final SensorSettingModel? sensor10;
  @override
  final SensorSettingModel? sensor20;
  @override
  final SensorSettingModel? sensor21;
  @override
  final SensorSettingModel? sensor11;
  @override
  final SensorSettingModel? sensor32;
  final List<int>? _tank_id;
  @override
  List<int>? get tank_id {
    final value = _tank_id;
    if (value == null) return null;
    if (_tank_id is EqualUnmodifiableListView) return _tank_id;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _motor_id;
  @override
  List<int>? get motor_id {
    final value = _motor_id;
    if (value == null) return null;
    if (_motor_id is EqualUnmodifiableListView) return _motor_id;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _bw_id;
  @override
  List<int>? get bw_id {
    final value = _bw_id;
    if (value == null) return null;
    if (_bw_id is EqualUnmodifiableListView) return _bw_id;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _filter_id;
  @override
  List<int>? get filter_id {
    final value = _filter_id;
    if (value == null) return null;
    if (_filter_id is EqualUnmodifiableListView) return _filter_id;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _valve_id;
  @override
  List<int>? get valve_id {
    final value = _valve_id;
    if (value == null) return null;
    if (_valve_id is EqualUnmodifiableListView) return _valve_id;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final TankSettingModel? tank11;
  @override
  final MotorSettingModel? motor10;
  @override
  final MotorSettingModel? motor11;
  @override
  final MotorSettingModel? motor12;
  @override
  final MotorSettingModel? motor13;
  @override
  final MotorSettingModel? motor14;
  @override
  final ValveSettingModel? valve10;
  @override
  final ValveSettingModel? valve11;
  @override
  final ValveSettingModel? valve12;
  @override
  final ValveSettingModel? valve13;
  @override
  final ValveSettingModel? valve14;
  @override
  final ValveSettingModel? valve20;
  @override
  final ValveSettingModel? valve21;
  @override
  final ValveSettingModel? valve22;
  @override
  final ValveSettingModel? valve30;
  @override
  final ValveSettingModel? valve31;
  @override
  final ValveSettingModel? valve32;
  @override
  final ValveSettingModel? valve33;
  @override
  final ValveSettingModel? valve40;
  @override
  final ValveSettingModel? valve41;
  @override
  final ValveSettingModel? valve42;
  @override
  final ValveSettingModel? valve43;
  @override
  final ValveSettingModel? valve44;
  @override
  final ValveSettingModel? valve50;
  @override
  final MotorSettingModel? filter10;
  @override
  final MotorSettingModel? filter11;
  @override
  final MotorSettingModel? filter20;
  @override
  final MotorSettingModel? filter30;
  @override
  final TankSettingModel? tank20;
  @override
  final TankSettingModel? tank21;
  @override
  final TankSettingModel? tank22;
  @override
  final int? valveNumber;
  @override
  final bool? has_valve;
  @override
  final MotorSettingModel? motor20;
  @override
  final MotorSettingModel? motor21;
  @override
  final MotorSettingModel? motor22;
  @override
  final TankSettingModel? tank30;
  @override
  final TankSettingModel? tank31;
  @override
  final TankSettingModel? tank32;
  @override
  final TankSettingModel? tank40;
  @override
  final TankSettingModel? tank41;
  @override
  final TankSettingModel? tank50;
  @override
  final TankSettingModel? tank51;
  @override
  final MotorSettingModel? motor30;
  @override
  final MotorSettingModel? motor31;
  @override
  final MotorSettingModel? motor40;
  @override
  final MotorSettingModel? motor41;
  @override
  final MotorSettingModel? motor50;
  @override
  final MotorSettingModel? motor51;
  @override
  final LocationModel? location;
  @override
  final String? installed_at;
  @override
  final String? bom;
  @override
  final bool? is_customized;

  @override
  String toString() {
    return 'DivisionSettingModel(motor_number: $motor_number, tank_number: $tank_number, name: $name, password: $password, id: $id, tank10: $tank10, bw10: $bw10, tank12: $tank12, tank13: $tank13, tank14: $tank14, sensor10: $sensor10, sensor20: $sensor20, sensor21: $sensor21, sensor11: $sensor11, sensor32: $sensor32, tank_id: $tank_id, motor_id: $motor_id, bw_id: $bw_id, filter_id: $filter_id, valve_id: $valve_id, tank11: $tank11, motor10: $motor10, motor11: $motor11, motor12: $motor12, motor13: $motor13, motor14: $motor14, valve10: $valve10, valve11: $valve11, valve12: $valve12, valve13: $valve13, valve14: $valve14, valve20: $valve20, valve21: $valve21, valve22: $valve22, valve30: $valve30, valve31: $valve31, valve32: $valve32, valve33: $valve33, valve40: $valve40, valve41: $valve41, valve42: $valve42, valve43: $valve43, valve44: $valve44, valve50: $valve50, filter10: $filter10, filter11: $filter11, filter20: $filter20, filter30: $filter30, tank20: $tank20, tank21: $tank21, tank22: $tank22, valveNumber: $valveNumber, has_valve: $has_valve, motor20: $motor20, motor21: $motor21, motor22: $motor22, tank30: $tank30, tank31: $tank31, tank32: $tank32, tank40: $tank40, tank41: $tank41, tank50: $tank50, tank51: $tank51, motor30: $motor30, motor31: $motor31, motor40: $motor40, motor41: $motor41, motor50: $motor50, motor51: $motor51, location: $location, installed_at: $installed_at, bom: $bom, is_customized: $is_customized)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DivisionSettingModelImpl &&
            (identical(other.motor_number, motor_number) ||
                other.motor_number == motor_number) &&
            (identical(other.tank_number, tank_number) ||
                other.tank_number == tank_number) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.tank10, tank10) || other.tank10 == tank10) &&
            (identical(other.bw10, bw10) || other.bw10 == bw10) &&
            (identical(other.tank12, tank12) || other.tank12 == tank12) &&
            (identical(other.tank13, tank13) || other.tank13 == tank13) &&
            (identical(other.tank14, tank14) || other.tank14 == tank14) &&
            (identical(other.sensor10, sensor10) ||
                other.sensor10 == sensor10) &&
            (identical(other.sensor20, sensor20) ||
                other.sensor20 == sensor20) &&
            (identical(other.sensor21, sensor21) ||
                other.sensor21 == sensor21) &&
            (identical(other.sensor11, sensor11) ||
                other.sensor11 == sensor11) &&
            (identical(other.sensor32, sensor32) ||
                other.sensor32 == sensor32) &&
            const DeepCollectionEquality().equals(other._tank_id, _tank_id) &&
            const DeepCollectionEquality().equals(other._motor_id, _motor_id) &&
            const DeepCollectionEquality().equals(other._bw_id, _bw_id) &&
            const DeepCollectionEquality()
                .equals(other._filter_id, _filter_id) &&
            const DeepCollectionEquality().equals(other._valve_id, _valve_id) &&
            (identical(other.tank11, tank11) || other.tank11 == tank11) &&
            (identical(other.motor10, motor10) || other.motor10 == motor10) &&
            (identical(other.motor11, motor11) || other.motor11 == motor11) &&
            (identical(other.motor12, motor12) || other.motor12 == motor12) &&
            (identical(other.motor13, motor13) || other.motor13 == motor13) &&
            (identical(other.motor14, motor14) || other.motor14 == motor14) &&
            (identical(other.valve10, valve10) || other.valve10 == valve10) &&
            (identical(other.valve11, valve11) || other.valve11 == valve11) &&
            (identical(other.valve12, valve12) || other.valve12 == valve12) &&
            (identical(other.valve13, valve13) || other.valve13 == valve13) &&
            (identical(other.valve14, valve14) || other.valve14 == valve14) &&
            (identical(other.valve20, valve20) || other.valve20 == valve20) &&
            (identical(other.valve21, valve21) || other.valve21 == valve21) &&
            (identical(other.valve22, valve22) || other.valve22 == valve22) &&
            (identical(other.valve30, valve30) || other.valve30 == valve30) &&
            (identical(other.valve31, valve31) || other.valve31 == valve31) &&
            (identical(other.valve32, valve32) || other.valve32 == valve32) &&
            (identical(other.valve33, valve33) || other.valve33 == valve33) &&
            (identical(other.valve40, valve40) || other.valve40 == valve40) &&
            (identical(other.valve41, valve41) || other.valve41 == valve41) &&
            (identical(other.valve42, valve42) || other.valve42 == valve42) &&
            (identical(other.valve43, valve43) || other.valve43 == valve43) &&
            (identical(other.valve44, valve44) || other.valve44 == valve44) &&
            (identical(other.valve50, valve50) || other.valve50 == valve50) &&
            (identical(other.filter10, filter10) ||
                other.filter10 == filter10) &&
            (identical(other.filter11, filter11) ||
                other.filter11 == filter11) &&
            (identical(other.filter20, filter20) ||
                other.filter20 == filter20) &&
            (identical(other.filter30, filter30) ||
                other.filter30 == filter30) &&
            (identical(other.tank20, tank20) || other.tank20 == tank20) &&
            (identical(other.tank21, tank21) || other.tank21 == tank21) &&
            (identical(other.tank22, tank22) || other.tank22 == tank22) &&
            (identical(other.valveNumber, valveNumber) ||
                other.valveNumber == valveNumber) &&
            (identical(other.has_valve, has_valve) ||
                other.has_valve == has_valve) &&
            (identical(other.motor20, motor20) || other.motor20 == motor20) &&
            (identical(other.motor21, motor21) || other.motor21 == motor21) &&
            (identical(other.motor22, motor22) || other.motor22 == motor22) &&
            (identical(other.tank30, tank30) || other.tank30 == tank30) &&
            (identical(other.tank31, tank31) || other.tank31 == tank31) &&
            (identical(other.tank32, tank32) || other.tank32 == tank32) &&
            (identical(other.tank40, tank40) || other.tank40 == tank40) &&
            (identical(other.tank41, tank41) || other.tank41 == tank41) &&
            (identical(other.tank50, tank50) || other.tank50 == tank50) &&
            (identical(other.tank51, tank51) || other.tank51 == tank51) &&
            (identical(other.motor30, motor30) || other.motor30 == motor30) &&
            (identical(other.motor31, motor31) || other.motor31 == motor31) &&
            (identical(other.motor40, motor40) || other.motor40 == motor40) &&
            (identical(other.motor41, motor41) || other.motor41 == motor41) &&
            (identical(other.motor50, motor50) || other.motor50 == motor50) &&
            (identical(other.motor51, motor51) || other.motor51 == motor51) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.installed_at, installed_at) ||
                other.installed_at == installed_at) &&
            (identical(other.bom, bom) || other.bom == bom) &&
            (identical(other.is_customized, is_customized) ||
                other.is_customized == is_customized));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        motor_number,
        tank_number,
        name,
        password,
        id,
        tank10,
        bw10,
        tank12,
        tank13,
        tank14,
        sensor10,
        sensor20,
        sensor21,
        sensor11,
        sensor32,
        const DeepCollectionEquality().hash(_tank_id),
        const DeepCollectionEquality().hash(_motor_id),
        const DeepCollectionEquality().hash(_bw_id),
        const DeepCollectionEquality().hash(_filter_id),
        const DeepCollectionEquality().hash(_valve_id),
        tank11,
        motor10,
        motor11,
        motor12,
        motor13,
        motor14,
        valve10,
        valve11,
        valve12,
        valve13,
        valve14,
        valve20,
        valve21,
        valve22,
        valve30,
        valve31,
        valve32,
        valve33,
        valve40,
        valve41,
        valve42,
        valve43,
        valve44,
        valve50,
        filter10,
        filter11,
        filter20,
        filter30,
        tank20,
        tank21,
        tank22,
        valveNumber,
        has_valve,
        motor20,
        motor21,
        motor22,
        tank30,
        tank31,
        tank32,
        tank40,
        tank41,
        tank50,
        tank51,
        motor30,
        motor31,
        motor40,
        motor41,
        motor50,
        motor51,
        location,
        installed_at,
        bom,
        is_customized
      ]);

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DivisionSettingModelImplCopyWith<_$DivisionSettingModelImpl>
      get copyWith =>
          __$$DivisionSettingModelImplCopyWithImpl<_$DivisionSettingModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DivisionSettingModelImplToJson(
      this,
    );
  }
}

abstract class _DivisionSettingModel implements DivisionSettingModel {
  const factory _DivisionSettingModel(
      {final int motor_number,
      final int tank_number,
      final String name,
      final String password,
      final String id,
      final TankSettingModel? tank10,
      final BorewellSettingModel? bw10,
      final TankSettingModel? tank12,
      final TankSettingModel? tank13,
      final TankSettingModel? tank14,
      final SensorSettingModel? sensor10,
      final SensorSettingModel? sensor20,
      final SensorSettingModel? sensor21,
      final SensorSettingModel? sensor11,
      final SensorSettingModel? sensor32,
      final List<int>? tank_id,
      final List<int>? motor_id,
      final List<int>? bw_id,
      final List<int>? filter_id,
      final List<int>? valve_id,
      final TankSettingModel? tank11,
      final MotorSettingModel? motor10,
      final MotorSettingModel? motor11,
      final MotorSettingModel? motor12,
      final MotorSettingModel? motor13,
      final MotorSettingModel? motor14,
      final ValveSettingModel? valve10,
      final ValveSettingModel? valve11,
      final ValveSettingModel? valve12,
      final ValveSettingModel? valve13,
      final ValveSettingModel? valve14,
      final ValveSettingModel? valve20,
      final ValveSettingModel? valve21,
      final ValveSettingModel? valve22,
      final ValveSettingModel? valve30,
      final ValveSettingModel? valve31,
      final ValveSettingModel? valve32,
      final ValveSettingModel? valve33,
      final ValveSettingModel? valve40,
      final ValveSettingModel? valve41,
      final ValveSettingModel? valve42,
      final ValveSettingModel? valve43,
      final ValveSettingModel? valve44,
      final ValveSettingModel? valve50,
      final MotorSettingModel? filter10,
      final MotorSettingModel? filter11,
      final MotorSettingModel? filter20,
      final MotorSettingModel? filter30,
      final TankSettingModel? tank20,
      final TankSettingModel? tank21,
      final TankSettingModel? tank22,
      final int? valveNumber,
      final bool? has_valve,
      final MotorSettingModel? motor20,
      final MotorSettingModel? motor21,
      final MotorSettingModel? motor22,
      final TankSettingModel? tank30,
      final TankSettingModel? tank31,
      final TankSettingModel? tank32,
      final TankSettingModel? tank40,
      final TankSettingModel? tank41,
      final TankSettingModel? tank50,
      final TankSettingModel? tank51,
      final MotorSettingModel? motor30,
      final MotorSettingModel? motor31,
      final MotorSettingModel? motor40,
      final MotorSettingModel? motor41,
      final MotorSettingModel? motor50,
      final MotorSettingModel? motor51,
      final LocationModel? location,
      final String? installed_at,
      final String? bom,
      final bool? is_customized}) = _$DivisionSettingModelImpl;

  factory _DivisionSettingModel.fromJson(Map<String, dynamic> json) =
      _$DivisionSettingModelImpl.fromJson;

  @override
  int get motor_number;
  @override
  int get tank_number;
  @override
  String get name;
  @override
  String get password;
  @override
  String get id;
  @override
  TankSettingModel? get tank10;
  @override
  BorewellSettingModel? get bw10;
  @override
  TankSettingModel? get tank12;
  @override
  TankSettingModel? get tank13;
  @override
  TankSettingModel? get tank14;
  @override
  SensorSettingModel? get sensor10;
  @override
  SensorSettingModel? get sensor20;
  @override
  SensorSettingModel? get sensor21;
  @override
  SensorSettingModel? get sensor11;
  @override
  SensorSettingModel? get sensor32;
  @override
  List<int>? get tank_id;
  @override
  List<int>? get motor_id;
  @override
  List<int>? get bw_id;
  @override
  List<int>? get filter_id;
  @override
  List<int>? get valve_id;
  @override
  TankSettingModel? get tank11;
  @override
  MotorSettingModel? get motor10;
  @override
  MotorSettingModel? get motor11;
  @override
  MotorSettingModel? get motor12;
  @override
  MotorSettingModel? get motor13;
  @override
  MotorSettingModel? get motor14;
  @override
  ValveSettingModel? get valve10;
  @override
  ValveSettingModel? get valve11;
  @override
  ValveSettingModel? get valve12;
  @override
  ValveSettingModel? get valve13;
  @override
  ValveSettingModel? get valve14;
  @override
  ValveSettingModel? get valve20;
  @override
  ValveSettingModel? get valve21;
  @override
  ValveSettingModel? get valve22;
  @override
  ValveSettingModel? get valve30;
  @override
  ValveSettingModel? get valve31;
  @override
  ValveSettingModel? get valve32;
  @override
  ValveSettingModel? get valve33;
  @override
  ValveSettingModel? get valve40;
  @override
  ValveSettingModel? get valve41;
  @override
  ValveSettingModel? get valve42;
  @override
  ValveSettingModel? get valve43;
  @override
  ValveSettingModel? get valve44;
  @override
  ValveSettingModel? get valve50;
  @override
  MotorSettingModel? get filter10;
  @override
  MotorSettingModel? get filter11;
  @override
  MotorSettingModel? get filter20;
  @override
  MotorSettingModel? get filter30;
  @override
  TankSettingModel? get tank20;
  @override
  TankSettingModel? get tank21;
  @override
  TankSettingModel? get tank22;
  @override
  int? get valveNumber;
  @override
  bool? get has_valve;
  @override
  MotorSettingModel? get motor20;
  @override
  MotorSettingModel? get motor21;
  @override
  MotorSettingModel? get motor22;
  @override
  TankSettingModel? get tank30;
  @override
  TankSettingModel? get tank31;
  @override
  TankSettingModel? get tank32;
  @override
  TankSettingModel? get tank40;
  @override
  TankSettingModel? get tank41;
  @override
  TankSettingModel? get tank50;
  @override
  TankSettingModel? get tank51;
  @override
  MotorSettingModel? get motor30;
  @override
  MotorSettingModel? get motor31;
  @override
  MotorSettingModel? get motor40;
  @override
  MotorSettingModel? get motor41;
  @override
  MotorSettingModel? get motor50;
  @override
  MotorSettingModel? get motor51;
  @override
  LocationModel? get location;
  @override
  String? get installed_at;
  @override
  String? get bom;
  @override
  bool? get is_customized;

  /// Create a copy of DivisionSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DivisionSettingModelImplCopyWith<_$DivisionSettingModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

LocationModel _$LocationModelFromJson(Map<String, dynamic> json) {
  return _LocationModel.fromJson(json);
}

/// @nodoc
mixin _$LocationModel {
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;

  /// Serializes this LocationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationModelCopyWith<LocationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationModelCopyWith<$Res> {
  factory $LocationModelCopyWith(
          LocationModel value, $Res Function(LocationModel) then) =
      _$LocationModelCopyWithImpl<$Res, LocationModel>;
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class _$LocationModelCopyWithImpl<$Res, $Val extends LocationModel>
    implements $LocationModelCopyWith<$Res> {
  _$LocationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_value.copyWith(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocationModelImplCopyWith<$Res>
    implements $LocationModelCopyWith<$Res> {
  factory _$$LocationModelImplCopyWith(
          _$LocationModelImpl value, $Res Function(_$LocationModelImpl) then) =
      __$$LocationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class __$$LocationModelImplCopyWithImpl<$Res>
    extends _$LocationModelCopyWithImpl<$Res, _$LocationModelImpl>
    implements _$$LocationModelImplCopyWith<$Res> {
  __$$LocationModelImplCopyWithImpl(
      _$LocationModelImpl _value, $Res Function(_$LocationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_$LocationModelImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationModelImpl implements _LocationModel {
  const _$LocationModelImpl({this.latitude = 27.325, this.longitude = 85.045});

  factory _$LocationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationModelImplFromJson(json);

  @override
  @JsonKey()
  final double latitude;
  @override
  @JsonKey()
  final double longitude;

  @override
  String toString() {
    return 'LocationModel(latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationModelImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude);

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationModelImplCopyWith<_$LocationModelImpl> get copyWith =>
      __$$LocationModelImplCopyWithImpl<_$LocationModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationModelImplToJson(
      this,
    );
  }
}

abstract class _LocationModel implements LocationModel {
  const factory _LocationModel(
      {final double latitude, final double longitude}) = _$LocationModelImpl;

  factory _LocationModel.fromJson(Map<String, dynamic> json) =
      _$LocationModelImpl.fromJson;

  @override
  double get latitude;
  @override
  double get longitude;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationModelImplCopyWith<_$LocationModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
