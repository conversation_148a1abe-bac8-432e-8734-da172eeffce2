import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'alert_dialogs.dart';

extension AsyncValueUI on AsyncValue {
  void showAlertDialogOnError(
      {required BuildContext context, required WidgetRef ref, required String key, VoidCallback? onLogoutCall}) {
    if (!isRefreshing && hasError) {

    }
  }

  void showForgetPasswordAlertDialogOnError(
      {required BuildContext context, required WidgetRef ref, required String key, VoidCallback? onLogoutCall}) {
    if (!isRefreshing && hasError) {

    }
  }

  String _errorMessage(Object? error) {

      return error.toString();

  }
}
