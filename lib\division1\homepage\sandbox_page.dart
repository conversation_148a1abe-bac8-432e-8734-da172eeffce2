import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/homepage/motor_list_page.dart';
import 'package:si/division1/homepage/valve_list_page.dart';

import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../services/shared_preferences_service.dart';
import '../provider/division_provider.dart';
import 'tank_list_page.dart';

class SandBoxPage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    //final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    return siteSetting.when(
        success: (setting, message) {
          return BaseScaffold(
              showLeftIcon: false,
              appbarText: setting?.name ?? "",
              showAction: true,
              onActionClick:() async {
                final setting = await context.push("/location");

              },
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: gapH8,
                  ),
                  SliverToBoxAdapter(
                    child: TankList(),
                  ),
                  SliverToBoxAdapter(
                    child: gapH16,
                  ),
                  SliverToBoxAdapter(
                    child: MotorList(),
                  ),
                  SliverToBoxAdapter(
                    child: gapH16,
                  ),
                  if(setting?.valve_id!=null && setting!.valve_id!.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ValveListPage(),
                  ),
                  SliverToBoxAdapter(
                    child: gapH16,
                  ),
                ],
              ));
        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }
}
