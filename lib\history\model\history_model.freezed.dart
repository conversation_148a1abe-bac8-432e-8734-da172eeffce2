// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Historymodel _$HistorymodelFromJson(Map<String, dynamic> json) {
  return _Historymodel.fromJson(json);
}

/// @nodoc
mixin _$Historymodel {
  int get loc_id => throw _privateConstructorUsedError;
  int get status => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this Historymodel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Historymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HistorymodelCopyWith<Historymodel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistorymodelCopyWith<$Res> {
  factory $HistorymodelCopyWith(
          Historymodel value, $Res Function(Historymodel) then) =
      _$HistorymodelCopyWithImpl<$Res, Historymodel>;
  @useResult
  $Res call(
      {int loc_id, int status, @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$HistorymodelCopyWithImpl<$Res, $Val extends Historymodel>
    implements $HistorymodelCopyWith<$Res> {
  _$HistorymodelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Historymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loc_id = null,
    Object? status = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      loc_id: null == loc_id
          ? _value.loc_id
          : loc_id // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistorymodelImplCopyWith<$Res>
    implements $HistorymodelCopyWith<$Res> {
  factory _$$HistorymodelImplCopyWith(
          _$HistorymodelImpl value, $Res Function(_$HistorymodelImpl) then) =
      __$$HistorymodelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int loc_id, int status, @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$HistorymodelImplCopyWithImpl<$Res>
    extends _$HistorymodelCopyWithImpl<$Res, _$HistorymodelImpl>
    implements _$$HistorymodelImplCopyWith<$Res> {
  __$$HistorymodelImplCopyWithImpl(
      _$HistorymodelImpl _value, $Res Function(_$HistorymodelImpl) _then)
      : super(_value, _then);

  /// Create a copy of Historymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loc_id = null,
    Object? status = null,
    Object? time = freezed,
  }) {
    return _then(_$HistorymodelImpl(
      loc_id: null == loc_id
          ? _value.loc_id
          : loc_id // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistorymodelImpl implements _Historymodel {
  const _$HistorymodelImpl(
      {this.loc_id = 0,
      this.status = 0,
      @TimestampNullableConverter() this.time});

  factory _$HistorymodelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistorymodelImplFromJson(json);

  @override
  @JsonKey()
  final int loc_id;
  @override
  @JsonKey()
  final int status;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'Historymodel(loc_id: $loc_id, status: $status, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistorymodelImpl &&
            (identical(other.loc_id, loc_id) || other.loc_id == loc_id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, loc_id, status, time);

  /// Create a copy of Historymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HistorymodelImplCopyWith<_$HistorymodelImpl> get copyWith =>
      __$$HistorymodelImplCopyWithImpl<_$HistorymodelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistorymodelImplToJson(
      this,
    );
  }
}

abstract class _Historymodel implements Historymodel {
  const factory _Historymodel(
      {final int loc_id,
      final int status,
      @TimestampNullableConverter() final DateTime? time}) = _$HistorymodelImpl;

  factory _Historymodel.fromJson(Map<String, dynamic> json) =
      _$HistorymodelImpl.fromJson;

  @override
  int get loc_id;
  @override
  int get status;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of Historymodel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HistorymodelImplCopyWith<_$HistorymodelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LevelHistorymodel _$LevelHistorymodelFromJson(Map<String, dynamic> json) {
  return _LevelHistorymodel.fromJson(json);
}

/// @nodoc
mixin _$LevelHistorymodel {
  int get loc_id => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this LevelHistorymodel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LevelHistorymodelCopyWith<LevelHistorymodel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LevelHistorymodelCopyWith<$Res> {
  factory $LevelHistorymodelCopyWith(
          LevelHistorymodel value, $Res Function(LevelHistorymodel) then) =
      _$LevelHistorymodelCopyWithImpl<$Res, LevelHistorymodel>;
  @useResult
  $Res call(
      {int loc_id, int level, @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$LevelHistorymodelCopyWithImpl<$Res, $Val extends LevelHistorymodel>
    implements $LevelHistorymodelCopyWith<$Res> {
  _$LevelHistorymodelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loc_id = null,
    Object? level = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      loc_id: null == loc_id
          ? _value.loc_id
          : loc_id // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LevelHistorymodelImplCopyWith<$Res>
    implements $LevelHistorymodelCopyWith<$Res> {
  factory _$$LevelHistorymodelImplCopyWith(_$LevelHistorymodelImpl value,
          $Res Function(_$LevelHistorymodelImpl) then) =
      __$$LevelHistorymodelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int loc_id, int level, @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$LevelHistorymodelImplCopyWithImpl<$Res>
    extends _$LevelHistorymodelCopyWithImpl<$Res, _$LevelHistorymodelImpl>
    implements _$$LevelHistorymodelImplCopyWith<$Res> {
  __$$LevelHistorymodelImplCopyWithImpl(_$LevelHistorymodelImpl _value,
      $Res Function(_$LevelHistorymodelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loc_id = null,
    Object? level = null,
    Object? time = freezed,
  }) {
    return _then(_$LevelHistorymodelImpl(
      loc_id: null == loc_id
          ? _value.loc_id
          : loc_id // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LevelHistorymodelImpl implements _LevelHistorymodel {
  const _$LevelHistorymodelImpl(
      {this.loc_id = 0,
      this.level = 0,
      @TimestampNullableConverter() this.time});

  factory _$LevelHistorymodelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LevelHistorymodelImplFromJson(json);

  @override
  @JsonKey()
  final int loc_id;
  @override
  @JsonKey()
  final int level;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'LevelHistorymodel(loc_id: $loc_id, level: $level, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LevelHistorymodelImpl &&
            (identical(other.loc_id, loc_id) || other.loc_id == loc_id) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, loc_id, level, time);

  /// Create a copy of LevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LevelHistorymodelImplCopyWith<_$LevelHistorymodelImpl> get copyWith =>
      __$$LevelHistorymodelImplCopyWithImpl<_$LevelHistorymodelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LevelHistorymodelImplToJson(
      this,
    );
  }
}

abstract class _LevelHistorymodel implements LevelHistorymodel {
  const factory _LevelHistorymodel(
          {final int loc_id,
          final int level,
          @TimestampNullableConverter() final DateTime? time}) =
      _$LevelHistorymodelImpl;

  factory _LevelHistorymodel.fromJson(Map<String, dynamic> json) =
      _$LevelHistorymodelImpl.fromJson;

  @override
  int get loc_id;
  @override
  int get level;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of LevelHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LevelHistorymodelImplCopyWith<_$LevelHistorymodelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AllHistorymodel _$AllHistorymodelFromJson(Map<String, dynamic> json) {
  return _AllHistorymodel.fromJson(json);
}

/// @nodoc
mixin _$AllHistorymodel {
  int get loc_id => throw _privateConstructorUsedError;
  int get float => throw _privateConstructorUsedError;
  int get motor => throw _privateConstructorUsedError;
  int get timer => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  double get current => throw _privateConstructorUsedError;
  @TimestampNullableConverter()
  DateTime? get time => throw _privateConstructorUsedError;

  /// Serializes this AllHistorymodel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AllHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AllHistorymodelCopyWith<AllHistorymodel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllHistorymodelCopyWith<$Res> {
  factory $AllHistorymodelCopyWith(
          AllHistorymodel value, $Res Function(AllHistorymodel) then) =
      _$AllHistorymodelCopyWithImpl<$Res, AllHistorymodel>;
  @useResult
  $Res call(
      {int loc_id,
      int float,
      int motor,
      int timer,
      int level,
      double current,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class _$AllHistorymodelCopyWithImpl<$Res, $Val extends AllHistorymodel>
    implements $AllHistorymodelCopyWith<$Res> {
  _$AllHistorymodelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AllHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loc_id = null,
    Object? float = null,
    Object? motor = null,
    Object? timer = null,
    Object? level = null,
    Object? current = null,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      loc_id: null == loc_id
          ? _value.loc_id
          : loc_id // ignore: cast_nullable_to_non_nullable
              as int,
      float: null == float
          ? _value.float
          : float // ignore: cast_nullable_to_non_nullable
              as int,
      motor: null == motor
          ? _value.motor
          : motor // ignore: cast_nullable_to_non_nullable
              as int,
      timer: null == timer
          ? _value.timer
          : timer // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as double,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AllHistorymodelImplCopyWith<$Res>
    implements $AllHistorymodelCopyWith<$Res> {
  factory _$$AllHistorymodelImplCopyWith(_$AllHistorymodelImpl value,
          $Res Function(_$AllHistorymodelImpl) then) =
      __$$AllHistorymodelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int loc_id,
      int float,
      int motor,
      int timer,
      int level,
      double current,
      @TimestampNullableConverter() DateTime? time});
}

/// @nodoc
class __$$AllHistorymodelImplCopyWithImpl<$Res>
    extends _$AllHistorymodelCopyWithImpl<$Res, _$AllHistorymodelImpl>
    implements _$$AllHistorymodelImplCopyWith<$Res> {
  __$$AllHistorymodelImplCopyWithImpl(
      _$AllHistorymodelImpl _value, $Res Function(_$AllHistorymodelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AllHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loc_id = null,
    Object? float = null,
    Object? motor = null,
    Object? timer = null,
    Object? level = null,
    Object? current = null,
    Object? time = freezed,
  }) {
    return _then(_$AllHistorymodelImpl(
      loc_id: null == loc_id
          ? _value.loc_id
          : loc_id // ignore: cast_nullable_to_non_nullable
              as int,
      float: null == float
          ? _value.float
          : float // ignore: cast_nullable_to_non_nullable
              as int,
      motor: null == motor
          ? _value.motor
          : motor // ignore: cast_nullable_to_non_nullable
              as int,
      timer: null == timer
          ? _value.timer
          : timer // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as double,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AllHistorymodelImpl implements _AllHistorymodel {
  const _$AllHistorymodelImpl(
      {this.loc_id = 0,
      this.float = 0,
      this.motor = 0,
      this.timer = 0,
      this.level = 0,
      this.current = 0.0,
      @TimestampNullableConverter() this.time});

  factory _$AllHistorymodelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AllHistorymodelImplFromJson(json);

  @override
  @JsonKey()
  final int loc_id;
  @override
  @JsonKey()
  final int float;
  @override
  @JsonKey()
  final int motor;
  @override
  @JsonKey()
  final int timer;
  @override
  @JsonKey()
  final int level;
  @override
  @JsonKey()
  final double current;
  @override
  @TimestampNullableConverter()
  final DateTime? time;

  @override
  String toString() {
    return 'AllHistorymodel(loc_id: $loc_id, float: $float, motor: $motor, timer: $timer, level: $level, current: $current, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllHistorymodelImpl &&
            (identical(other.loc_id, loc_id) || other.loc_id == loc_id) &&
            (identical(other.float, float) || other.float == float) &&
            (identical(other.motor, motor) || other.motor == motor) &&
            (identical(other.timer, timer) || other.timer == timer) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, loc_id, float, motor, timer, level, current, time);

  /// Create a copy of AllHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AllHistorymodelImplCopyWith<_$AllHistorymodelImpl> get copyWith =>
      __$$AllHistorymodelImplCopyWithImpl<_$AllHistorymodelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AllHistorymodelImplToJson(
      this,
    );
  }
}

abstract class _AllHistorymodel implements AllHistorymodel {
  const factory _AllHistorymodel(
          {final int loc_id,
          final int float,
          final int motor,
          final int timer,
          final int level,
          final double current,
          @TimestampNullableConverter() final DateTime? time}) =
      _$AllHistorymodelImpl;

  factory _AllHistorymodel.fromJson(Map<String, dynamic> json) =
      _$AllHistorymodelImpl.fromJson;

  @override
  int get loc_id;
  @override
  int get float;
  @override
  int get motor;
  @override
  int get timer;
  @override
  int get level;
  @override
  double get current;
  @override
  @TimestampNullableConverter()
  DateTime? get time;

  /// Create a copy of AllHistorymodel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AllHistorymodelImplCopyWith<_$AllHistorymodelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
