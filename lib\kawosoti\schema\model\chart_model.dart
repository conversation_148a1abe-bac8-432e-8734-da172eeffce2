import 'package:fl_chart/fl_chart.dart';

class ChartModel{
  ChartModel({
    this.t1List,
    this.t2List,
    this.t3List,
    this.t4List,
    this.t5List,
    this.chList,
    this.phList,
    this.ohList,
    this.bwList,
    this.minX,
    this.minY,
    this.maxX,
    this.maxY
  }) ;

  final List<FlSpot>? t1List;
  final List<FlSpot>? t2List;
  final List<FlSpot>? t3List;
  final List<FlSpot>? t4List;
  final List<FlSpot>? t5List;
  final List<FlSpot>? chList;
  final List<FlSpot>? phList;
  final List<FlSpot>? ohList;
  final List<FlSpot>? bwList;
  final double? minX;
  final double? minY;
  final double? maxX;
  final double? maxY;

}