

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../../constants/app_sizes.dart';
import '../../provider/dashboard_provider.dart';
import 'new_tank_unit.dart';

class TankList extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final setting = ref.read(localStorageProvider).getSettings();
    final List<Widget> widgetList = List.generate(setting.tank_id!.length, (index) {
      return NewTankUnit(
          ref.read(userState).role ?? 1,
          setting.tank_id![index].toString(),
          getTankModel(setting.tank_id![index].toString(), setting),
          140,
          MediaQuery.of(context).size.width / 4  - 10);
  });
    return Column(
      children: _buildRows(widgetList),
    );

}

List<Widget> _buildRows(List<Widget> widgetList) {
  List<Widget> rows = [];
  for (int i = 0; i < widgetList.length; i += 3) {
    int end = i + 3 > widgetList.length ? widgetList.length : i + 3;
    rows.add(
      Column(
        children: [
          gapH8,
          Row(
            mainAxisAlignment: end - i == 1
                ? MainAxisAlignment.center
                : MainAxisAlignment.spaceEvenly,
            children: widgetList.sublist(i, end),
          ),
          gapH8
        ],
      ),
    );
  }
  return rows;
}
}




TankSettingModel getTankModel(String tankId, DivisionSettingModel setting){
  if(tankId=="10"){
    return setting.tank10!;
  }else if(tankId=="11"){
    return setting.tank11!;
  }else if(tankId=="12"){
    return setting.tank12!;
  }else if(tankId=="13"){
    return setting.tank13!;
  }else if(tankId=="14"){
    return setting.tank14!;
  } else if(tankId=="20"){
    return setting.tank20!;
  }else if(tankId=="21"){
    return setting.tank21!;
  }else if(tankId=="30"){
    return setting.tank30!;
  }else if(tankId=="31"){
    return setting.tank31!;
  }else if(tankId=="40"){
    return setting.tank40!;
  }else if(tankId=="41"){
    return setting.tank41!;
  }else if(tankId=="50"){
    return setting.tank50!;
  }else if(tankId=="51"){
    return setting.tank51!;
  }else{
    return setting.tank10!;
  }
}