

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../../model/dashboard_model.dart';
import '../../utils/format.dart';

class TankMonitor extends HookConsumerWidget{
  TankMonitor(this.dashboardModel, this.site);
  final DashboardModel dashboardModel;
  final String site;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double perc = (((dashboardModel.actualHeight ?? 1) -(dashboardModel.level ?? 0))/(dashboardModel.actualHeight ?? 1)).abs()*100;
    return InkWell(
      onDoubleTap: (){
        if(ref.read(userState).role==3){
          if(site=="site1"){
            context.push('/siteOne/history');
          }else{
            context.push('/siteTwo/history');
          }
        }
      },
      child: Stack(
        children: [
          SfRadialGauge(axes: <RadialAxis>[
            RadialAxis(
                minimum: 0,
                maximum: 100,
                radiusFactor: 0.8,
                ranges: <GaugeRange>[
                  GaugeRange(
                    startValue: 0,
                    endValue: 30,
                    color: Colors.green,
                    startWidth: 10,
                    endWidth: 10,
                  ),
                  GaugeRange(
                      startValue: 30, endValue: 60, color: Colors.orange),
                  GaugeRange(
                      startValue: 60, endValue: 100, color: Colors.red)
                ],
                pointers: <GaugePointer>[
                  NeedlePointer(
                    value: perc ?? 0,
                    needleLength: 0.5,
                    enableAnimation: true,
                    enableDragging: true,
                  )
                ],
                annotations: <GaugeAnnotation>[
                  GaugeAnnotation(
                      widget: Container(
                          child: Text(perc.floor().toString() + " %",
                              style: TextStyle(
                                  fontSize: 25,
                                  fontWeight: FontWeight.bold))),
                      angle: 90,
                      positionFactor: 0.5)
                ])
          ]),
          Align(
            alignment: Alignment.bottomRight,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Text(
                Format.date(dashboardModel.level_updated_at!),
                style: TextStyle(color: Colors.black),
              ),
            ),
          )
        ],
      ),
    );
  }

}