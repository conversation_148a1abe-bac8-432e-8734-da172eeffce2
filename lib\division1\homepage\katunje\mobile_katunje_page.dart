

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../../constants/app_sizes.dart';
import '../../model/division_setting_model.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';

class MobileKatunjePage extends HookConsumerWidget {
  const MobileKatunjePage(this.setting, {super.key});

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.read(sharedPreferencesServiceProvider);
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            height: 16,
          ),
        ),
        SliverToBoxAdapter(
          child: SingleChildScrollView(
            scrollDirection : Axis.horizontal,
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '10',motorModel: null,motorSettingModel: setting.motor10,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '11',motorModel: null,motorSettingModel: setting.motor11,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '12',motorModel: null,motorSettingModel: setting.motor12,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '20',motorModel: null,motorSettingModel: setting.motor20,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '21',motorModel: null,motorSettingModel: setting.motor21,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '22',motorModel: null,motorSettingModel: setting.motor22,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '30',motorModel: null,motorSettingModel: setting.motor30,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      NewMotorUnit(motorId: '31',motorModel: null,motorSettingModel: setting.motor31,ratio: 3,),
                      Container(
                        height: 50,width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Image.asset("assets/icons/katunje_filter.webp",
                      height: 250,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 250,
                    color: Colors.black,
                  ),
                   Container(
                    width: 120,
                    child: const Center(
                      child: Text("Filter Unit",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                gapW8,
                Column(
                  children: [
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    ),
                    NewTankUnit(prefs.getUserRole(), "10", setting.tank10!, 170, 110,setting: setting,),
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
                gapW16,
                Column(
                  children: [
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    ),
                    NewTankUnit(prefs.getUserRole(), "20", setting.tank20!, 170, 110, setting: setting,),
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
                gapW16,
                Column(
                  children: [
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    ),
                    NewTankUnit(prefs.getUserRole(), "30", setting.tank30!, 170, 110,
                      setting: setting,
                      ),
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
                gapW16,
                Column(
                  children: [
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    ),
                    NewTankUnit(prefs.getUserRole(), "11", setting.tank11!, 170, 110,
                      setting: setting,
                    ),
                    Container(
                      width: 20,
                      height: 40,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            height: 60,
            color: Colors.blueAccent,
            child: const Center(
              child: Text(
                "Distribution",
                style: TextStyle(color: Colors.white,fontSize: 18),
              ),
            ),
          ),
        )
      ],
    );

  }
}