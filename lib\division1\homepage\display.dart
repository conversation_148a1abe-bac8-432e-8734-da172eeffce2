import 'dart:developer';
import 'dart:io';

import 'package:digital_lcd_number/digital_lcd_number.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:progress_stepper/progress_stepper.dart';
import 'package:si/division1/homepage/collection_water_volume.dart';
import 'package:si/division1/homepage/flow_reading_box.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/motor_model.dart';
import 'package:si/division1/model/motor_setting_model.dart';

import '../../constants/app_sizes.dart';
import '../provider/division_provider.dart';
import 'motor_moving.dart';

class DisplayWidget extends HookConsumerWidget {
  DisplayWidget(
      {this.motorName = 'Motor',
      this.motorState = 2,
      this.motorId = 'motor1',
        this.motorModel,
      this.roleType = 1,
        this.voltage = 0.0,
      this.motorAmps = 0.0, this.motorSettingModel});

  final String motorName;

  final int motorState;

  final MotorModel? motorModel;

  final String motorId;

  final int roleType;

  final double motorAmps;
  final double? voltage;

  final MotorSettingModel? motorSettingModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
     final motorOn = motorModel?.mobile_status == 1 && motorModel?.device_status==1 && motorModel?.output_status==1;
    return Container(
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border.all(color: Colors.black),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(4.0),
            child:  Text(
              motorName ?? 'Motor',

              style: TextStyle(color: motorState ==1 ?  Colors.green :Colors.grey, fontSize: 12),
            ),
          ),

          Container(
            width: double.infinity,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.all(Radius.circular(12)),
            ),
            child: MotorMoving(motorModel?.output_status == 1, motorModel,
                motorSettingModel,
                faultStatus:
                motorModel?.VoltageFaultStatus ==1),
          ),

          Padding(
            padding: const EdgeInsets.all(4.0),
            child: !(motorSettingModel?.has_voltage ?? false)? Text(
              motorOn
                  ? motorAmps.toStringAsFixed(2) + ' A'
                  : '0.00 A',
              style: TextStyle(
                  color:
                      motorState == 1 ? Colors.green : Colors.grey),
            ) : Text(
              motorOn
                  ? motorAmps.toStringAsFixed(2) + ' A/'+ ((voltage ?? 0.0) + (motorSettingModel?.CF ?? 0.0)).toStringAsFixed(2) + ' V'
                  : '0.00 A/' + ((voltage ?? 0.0) + (motorSettingModel?.CF ?? 0.0)).toStringAsFixed(2) + ' V',
              style: TextStyle(
                  color:
                      motorOn ? Colors.green : Colors.grey),
            ),
          ),

          ProgressStepper(
            width: 80,
            height: 15,
            stepCount: 3,
            builder: ( context,index, widthOfStep) {
              if (index == 1) {
                return ProgressStepWithArrow(
                  width: widthOfStep,
                  height: 15,
                  defaultColor: Colors.grey,
                  progressColor: Colors.green,
                  borderWidth: 1,
                  wasCompleted: motorModel?.mobile_status==1,
                  child: Center(
                    child: Text(
                      index.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ),
                );
              }
              return ProgressStepWithChevron(
                width: widthOfStep,
                height: 15,
                defaultColor: Colors.grey,
                progressColor: Colors.green,
                borderWidth: 1,
                wasCompleted: index==2 ? motorModel?.device_status==1 : index ==3 ? motorModel?.output_status==1 : false,
                child: Center(
                  child: Text(
                    index.toString(),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          ),
          gapH8,
          Consumer(builder: (context, WidgetRef ref, child) {
            final userActive = ref
                    .watch(userProfileProvider)
                    .asData
                    ?.value
                    ?.role ??
                5;
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: FlutterSwitch(
                    width: 60.0,
                    height: 25.0,
                    activeColor: Colors.green,
                    inactiveColor: Colors.grey,
                    valueFontSize: 12.0,
                    toggleSize: 25.0,
                    value: motorModel?.device_status == 1 && motorModel?.mobile_status == 1 && motorModel?.output_status==1,
                    borderRadius: 30.0,
                    padding: 4.0,
                    showOnOff: true,
                    onToggle: (val) async {
                      EasyLoading.show();
                      if (await hasNetwork()) {
                        if (userActive == 0) {
                          EasyLoading.showError(
                              'The user is inactive. Please contact concern admins for activating the user.');
                        } else if (userActive == 1 ) {
                          EasyLoading.showError(
                              'You do not have enough permission. Please contact concern admins for activating the user.');
                        } else if (userActive == 2) {
                          if (motorState != 3) {
                            await ref
                                .read(divisionRtdbServiceProvider)
                                .startMotor(motorId);
                          } else {
                            await ref
                                .read(divisionRtdbServiceProvider)
                                .stopMotor(motorId);
                          }
                        } else if (userActive == 3 || userActive == 4) {
                          if (motorState != 1) {
                            await ref
                                .read(divisionRtdbServiceProvider)
                                .startMotor(motorId);
                          } else {
                            await ref
                                .read(divisionRtdbServiceProvider)
                                .stopMotor(motorId);
                          }
                        }else{
                          EasyLoading.showError(
                              'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                        }
                      } else {
                        EasyLoading.showError(
                            'No internet Connection');
                      }
                    }),
              ),
            );
          }),
          gapH8,
        ],
      ),
    );

  }

  Future<bool> hasNetwork() async {
    try {
      final result = await InternetAddress.lookup('example.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
