

import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/settings/label_text.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:simple_chips_input/select_chips_input.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';


class AddSitePage extends HookConsumerWidget {
  AddSitePage( );

  final GlobalKey<FormState> _formAddKey = GlobalKey<FormState>();

  Future<void> _handleSiteSetting(
      BuildContext context,
      WidgetRef ref, {
        required TextEditingController siteIdController,
        required TextEditingController siteNameController,
        required String tankIds,
        required String motorIds,
        required String borewellIds,
        required String valveIds,
      }) async {

    final repo = ref.read(divisionRepositoryProvider);
    final rtdbrepo = ref.read(divisionRtdbServiceProvider);
    final settingRepo = ref.read(divisionRepositoryProvider);
    final siteId = ref.read(sharedPreferencesServiceProvider).getSiteId();

        // final email = emailController.text;
        // final password = passwordController.text;
        // Logger().d('Email : $email \nPassword: $password');
        try {
          EasyLoading.show(status: 'Adding..');
          List<int> tankIntList = [];
          for(var tank in tankIds.split(";")){
            if(tank.isNotEmpty){
              tankIntList.add(int.parse(tank));
            }
          }
          List<int> motorIntList = [];
          for(var motor in motorIds.split(";")){
            if(motor.isNotEmpty){
              motorIntList.add(int.parse(motor));
            }
          }

          List<int> borewellIntlist = [];
          for(var borewell in borewellIds.split(";")){
            if(borewell.isNotEmpty){
              motorIntList.add(int.parse(borewell));
            }
          }

          List<int> valveIntList = [];
          for(var valve in valveIds.split(";")){
            if(valve.isNotEmpty){
              valveIntList.add(int.parse(valve));
            }
          }
          await settingRepo.addNewSite("site"+siteIdController.text, siteNameController.text,tankIntList, motorIntList, borewellIntlist, valveIntList);
          await rtdbrepo.addMotorTank("site"+siteIdController.text, tankIntList, motorIntList, borewellIntlist, valveIntList);
          EasyLoading.showSuccess('Site Added successfully');
          Navigator.pop(context,true);

        } catch (e) {
          EasyLoading.showError(e.toString());
          //Logger().e('create user from email', e);
        }
        FocusScope.of(context).unfocus();

  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteIdController = useTextEditingController();
    final siteNameController = useTextEditingController();
    final motorIds = useState("");
    final tankIds = useState("");
    final borewellIds = useState("");
    final valveIds = useState("");

    useEffect(() {
      // print('SessionDEt : ${sessionModel.id}');
    /*  actualHeightController.text = tankSettingModel.actual_height.toString();
      tankNameController.text = tankSettingModel.name;
      minHeightController.text = tankSettingModel.min_height.toString();
      maxHeightController.text = tankSettingModel.max_height.toString();
      uploadTimeController.text = tankSettingModel.upload_time.toString();*/
      return null;
    }, const []);

    return BaseScaffold(
      showAppBar: true,
      appbarText: "Add Site",
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [

                    _buildTextFormFieldSection(context, ref,
                      siteIdController,
                      siteNameController,
                     ),
                    gapH16,
                    LabelText(label: "Select Tank Id"),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SelectChipsInput(
                        chipsText: const ['10', '11', '12', '13', '14','15','20', '21', '22', '23','30', '31', '40', '41', '50', '51', '60', '61'],
                        separatorCharacter: ";",
                        selectedChipTextStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                        ),
                        unselectedChipTextStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        onTap: (p0, p1) {
                          tankIds.value = p0;
                        },
                        prefixIcons: null,
                        selectedSuffixIcon: const Padding(
                          padding: EdgeInsets.only(right: 2.0,left:8.0),
                          child: Icon(
                            Icons.check_box,
                            size: 20.0,
                          ),
                        ),
                        widgetContainerDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.0),
                          color: Colors.green[100]!.withOpacity(0.5),
                        ),
                        unselectedChipDecoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        selectedChipDecoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                    LabelText(label: "Select Motor Id"),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SelectChipsInput(
                        chipsText: const ['10', '11', '12', '13','14','15', '20', '21', '22', '23','30', '31', '40', '41', '50', '51', '60', '61'],
                        separatorCharacter: ";",
                        selectedChipTextStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                        ),
                        unselectedChipTextStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        onTap: (p0, p1) {
                            motorIds.value = p0;
                        },
                        prefixIcons: null,
                        selectedSuffixIcon: const Padding(
                          padding: EdgeInsets.only(right: 2.0,left:8.0),
                          child: Icon(
                            Icons.check_box,
                            size: 20.0,
                          ),
                        ),
                        widgetContainerDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.0),
                          color: Colors.green[100]!.withOpacity(0.5),
                        ),
                        unselectedChipDecoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        selectedChipDecoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                    LabelText(label: "Select Borewell Id"),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SelectChipsInput(
                        chipsText: const ['10', '11', '12', '13','14','15', '20', '21', '22', '23','30', '31', '40', '41', '50', '51', '60', '61'],
                        separatorCharacter: ";",
                        selectedChipTextStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                        ),
                        unselectedChipTextStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        onTap: (p0, p1) {
                          borewellIds.value = p0;
                        },
                        prefixIcons: null,
                        selectedSuffixIcon: const Padding(
                          padding: EdgeInsets.only(right: 2.0,left:8.0),
                          child: Icon(
                            Icons.check_box,
                            size: 20.0,
                          ),
                        ),
                        widgetContainerDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.0),
                          color: Colors.green[100]!.withOpacity(0.5),
                        ),
                        unselectedChipDecoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        selectedChipDecoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                    LabelText(label: "Select Valve Id"),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SelectChipsInput(
                        chipsText: const ['10', '11', '12', '13','14', '20', '21', '22','30', '31','32', '40', '41', '42'],
                        separatorCharacter: ";",
                        selectedChipTextStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                        ),
                        unselectedChipTextStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        onTap: (p0, p1) {
                          valveIds.value = p0;
                        },
                        prefixIcons: null,
                        selectedSuffixIcon: const Padding(
                          padding: EdgeInsets.only(right: 2.0,left:8.0),
                          child: Icon(
                            Icons.check_box,
                            size: 20.0,
                          ),
                        ),
                        widgetContainerDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.0),
                          color: Colors.green[100]!.withOpacity(0.5),
                        ),
                        unselectedChipDecoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        selectedChipDecoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24.0, vertical: 24),
                      child: PrimaryButton(
                        text: 'Add Site',
                        height: 54,
                        onPressed: () async {

                          _handleSiteSetting(context,ref,
                              siteIdController: siteIdController,
                              siteNameController: siteNameController,
                            tankIds: tankIds.value,
                            motorIds: motorIds.value,
                            borewellIds: borewellIds.value,
                            valveIds: valveIds.value
                             );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

        ],
      ),

    );
  }

  Widget _buildTextFormFieldSection(
      BuildContext context,
      WidgetRef ref,
      TextEditingController siteIdController,
      TextEditingController siteNameController,
      ) {
    // final FocusNode? pwdFocusNode = FocusNode();

    // EdgeFunctionController edgeFunctionController = EdgeFunctionController();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        gapH16,
        LabelText(label: "Enter site Id"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "siteId",
              controller: siteIdController,
              hintText: "Site Id",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),
        LabelText(label: "Enter Site Name"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "siteName",
              controller: siteNameController,
              hintText: "Site name",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.text,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),

      ],
    );
  }
}

