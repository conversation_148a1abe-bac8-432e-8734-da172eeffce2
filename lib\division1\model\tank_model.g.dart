// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tank_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TankModelImpl _$$TankModelImplFromJson(Map<String, dynamic> json) =>
    _$TankModelImpl(
      float_status: (json['float_status'] as num?)?.toInt() ?? 0,
      level: (json['level'] as num?)?.toInt() ?? 1,
      actual_height: (json['actual_height'] as num?)?.toInt() ?? 450,
    );

Map<String, dynamic> _$$TankModelImplToJson(_$TankModelImpl instance) =>
    <String, dynamic>{
      'float_status': instance.float_status,
      'level': instance.level,
      'actual_height': instance.actual_height,
    };

_$BwModelImpl _$$BwModelImplFromJson(Map<String, dynamic> json) =>
    _$BwModelImpl(
      level: (json['level'] as num?)?.toDouble() ?? 1,
      time: (json['time'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$BwModelImplToJson(_$BwModelImpl instance) =>
    <String, dynamic>{
      'level': instance.level,
      'time': instance.time,
    };

_$OutputInputVolumeModelImpl _$$OutputInputVolumeModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OutputInputVolumeModelImpl(
      mins: (json['mins'] as num?)?.toInt() ?? 0,
      random: (json['random'] as num?)?.toInt() ?? 0,
      time: (json['time'] as num?)?.toInt() ?? 0,
      status: (json['status'] as num?)?.toInt() ?? 0,
      volume: (json['volume'] as num?)?.toInt() ?? 0,
      rate: (json['rate'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$OutputInputVolumeModelImplToJson(
        _$OutputInputVolumeModelImpl instance) =>
    <String, dynamic>{
      'mins': instance.mins,
      'random': instance.random,
      'time': instance.time,
      'status': instance.status,
      'volume': instance.volume,
      'rate': instance.rate,
    };
