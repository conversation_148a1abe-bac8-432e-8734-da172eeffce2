

import 'package:hooks_riverpod/hooks_riverpod.dart';

class SettingState {
  SettingState({
    this.value = const AsyncValue.data(null),
  });

  final AsyncValue<void> value;

  bool get isLoading => value.isLoading;

  SettingState copyWith({
    AsyncValue<void>? value,
  }) {
    return SettingState(
      value: value ?? this.value,
    );
  }

  @override
  String toString() =>
      'EmailPasswordSignInState( value: $value)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SettingState &&
        other.value == value;
  }

  @override
  int get hashCode =>  value.hashCode;
}