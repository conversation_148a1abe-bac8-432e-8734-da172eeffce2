import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';

import '../../../utils/format.dart';
import '../../provider/auth_provider.dart';

class ItemUser extends HookConsumerWidget {
  const ItemUser(this.userProfile);

  final UserModel? userProfile;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isActive = useState<bool>(
        userProfile?.role == 2);
    return Container(
      height: 150,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Card(
          clipBehavior: Clip.antiAlias,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
                child: Row(
                  children: [
                    Expanded(child: Text("Name: "+(userProfile?.fullName ?? ''),style: TextStyle(color: Colors.black,fontSize: 14),)),
                    Expanded(child: Text("Email: "+(userProfile?.email ?? ''),style: TextStyle(color: Colors.black,fontSize: 14),)),

                  ],
                ),
              ),
              HookConsumer(builder:
                  (BuildContext context, WidgetRef ref, Widget? child) {
                    final showNotification = useState<bool>(
                        userProfile?.send_notification ?? false);
                return Row(
                  children: [
                    Expanded(
                      child: ListTile(
                        title: Text(
                          'Is Active',style: TextStyle(color: Colors.black,fontSize: 14),
                        ),
                        trailing: CupertinoSwitch(
                          value: isActive.value,
                          onChanged: (bool value) async {
                              isActive.value = value;
                              await ref
                                  .read(divisionRepositoryProvider)
                                  .updateActiveStatus(userProfile?.id ?? '', value);

                          },
                        ),
                        onTap: () {
                            isActive.value = !isActive.value;
                        },
                      ),
                    ),
                    Expanded(
                      child: ListTile(
                        title: Text(
                          'Notify',style: TextStyle(color: Colors.black,fontSize: 14),
                        ),
                        trailing: CupertinoSwitch(
                          value: showNotification.value,
                          onChanged: (bool value) async {
                            showNotification.value = value;
                            await ref
                                .read(divisionRepositoryProvider)
                                .updateNotificationStatus(userProfile?.id ?? '', value);

                          },
                        ),
                        onTap: () {
                          if(isActive.value){
                            showNotification.value = !showNotification.value;
                          }
                        },
                      ),
                    )
                  ],
                );
              }),
            ],
          )),
    );
  }
}
