import 'dart:async';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Gauge imports
import 'package:syncfusion_flutter_gauges/gauges.dart';

/// Local imports

/// Renders the gauge axis default sample.
class OverHeadSensor extends StatefulHookConsumerWidget {
  /// Renders default radial gauge widget
  const OverHeadSensor({Key? key}) : super(key: key);

  @override
  _OverHeadState createState() => _OverHeadState();
}

class _OverHeadState extends ConsumerState<OverHeadSensor> {
  _OverHeadState();

  late Timer _timer;
  int _value = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250,
      child: Stack(
        children: [
          _buildWidgetPointerExample(context),
          Align(
              alignment: Alignment.topCenter,
              child: Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Text("Overhead sensor"),
              )),
          Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text("Overhead sensor"),
              )),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _startTimer() {
    if (mounted) {
      _timer = Timer.periodic(const Duration(milliseconds: 30), (Timer timer) {
        _incrementPointerValue();
      });
    }
  }

  void _incrementPointerValue() {
    setState(() {
      if (_value == 60) {
        _timer.cancel();
      } else {
        _value++;
      }
    });
  }

  /// Returns the default axis gauge
  SfRadialGauge _buildWidgetPointerExample(BuildContext context) {
    return SfRadialGauge(
      axes: <RadialAxis>[
        RadialAxis(
          interval: 10,
          labelOffset: 0.1,
          tickOffset: 0.125,
          minorTicksPerInterval: 0,
          labelsPosition: ElementsPosition.outside,
          offsetUnit: GaugeSizeUnit.factor,
          showAxisLine: false,
          showLastLabel: true,
          radiusFactor: 0.6,
          maximum: 100,
          pointers: <GaugePointer>[
            WidgetPointer(
                offset:  -20,
                value: _value.toDouble(),
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(40),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          color: Colors.grey,
                          blurRadius: 4.0,
                        ),
                      ],
                      border: Border.all(
                        color:  Colors.black.withOpacity(0.1),)),
                  height: 45,
                  width:45,
                  child: Center(
                    child: Row(
                      children: <Widget>[
                        const Padding(padding: EdgeInsets.fromLTRB(5, 0, 0, 0)),
                        Container(
                            width: 20,
                            height: 30,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: ExactAssetImage('icons/temperature_indicator_light.png'),
                                fit: BoxFit.fill,
                              ),
                            )),
                        Center(
                          child: Text(
                            '$_value',
                            style: TextStyle(
                              color: const Color.fromRGBO(126, 126, 126, 1),
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ))
          ],
          ranges: <GaugeRange>[
            GaugeRange(
              startValue: 0,
              endValue: 35,
              color: const Color.fromRGBO(237, 34, 35, 1),
            ),
            GaugeRange(
              startValue: 35,
              endValue: 70,
              color: const Color.fromRGBO(251, 190, 32, 1),
            ),
            GaugeRange(
              startValue: 70,
              endValue: 100,
              color: const Color.fromRGBO(74, 177, 70, 1),
            )
          ],
        )
      ],
    );
  }
}