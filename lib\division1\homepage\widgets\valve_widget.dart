

import 'dart:io';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:progress_stepper/progress_stepper.dart';
import 'package:si/utils/app_dimension.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../../utils/format.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';

class ValveWidget extends HookConsumerWidget{

  ValveWidget({ required this.name,
  required this.valveId, required this.active,required this.role, required this.ratio,required this.valveModel});

  final String name;
  final String valveId;
  final int role;
  final double ratio;
  final bool active;


  final MotorModel? valveModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.read(sharedPreferencesServiceProvider);


    return GestureDetector(
      onLongPress: () async {
        if(role>3){

        }
      },

      child:  Container(
        height: 180,
        width: MediaQuery.of(context).size.width / ratio ,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(color: valveModel?.device_status==3 ?   Colors.green : Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child:  Text(
                 name,
                style: TextStyle(color: valveModel?.device_status==3  ?  Colors.green :Colors.grey, fontSize: 12),
              ),
            ),

            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Image.asset(active && valveModel?.device_status==3 ?  "assets/icons/valve_on.png" :"assets/icons/valve.png", height: 60, width: 60,),
            ),

            gapH8,
            Consumer(builder: (context, WidgetRef ref, child) {
              final userActive = ref
                  .watch(userProfileProvider)
                  .asData
                  ?.value
                  ?.role ??
                  5;
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FlutterSwitch(
                      width: 60.0,
                      height: 25.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 25.0,
                      value: active && valveModel?.device_status == 3 && valveModel?.mobile_status == 3 ,
                      borderRadius: 30.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {
                        if(active!=null && active!){
                          EasyLoading.show();
                          if (await hasNetwork()) {
                            if (userActive == 0) {
                              EasyLoading.showError(
                                  'Something wrong with your device.');
                            } else if (userActive == 1 ) {
                              EasyLoading.showError(
                                  'You do not have enough permission. Please contact concern admins for activating the user.');
                            } else if (userActive == 2) {
                              if (valveModel?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startValve(valveId);
                               // await ref.read(divisionRepositoryProvider).
                               // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopValve(valveId);
                              }
                             // await ref.read(divisionRepositoryProvider).
                             // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                            } else if (userActive >=3) {
                              if (valveModel?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startValve(valveId);
                               // await ref.read(divisionRepositoryProvider).
                              //  addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopValve(valveId);
                              //  await ref.read(divisionRepositoryProvider).
                               // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              }
                            }else{
                              EasyLoading.showError(
                                  'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                            }
                          } else {
                            EasyLoading.showError(
                                'No internet Connection');
                          }
                        }
                      }),
                ),
              );
            }),
            gapH8,
            Text(Format.dateFromTimeStamp(valveModel?.time ?? DateTime.now().millisecondsSinceEpoch), style: TextStyle(color: Colors.black, fontSize: 12),)


          ],
        ),
      ),
    );

  }

}

class CommonPassingValveWidget extends HookConsumerWidget{

  CommonPassingValveWidget({ required this.name,
    required this.valveId, required this.active,required this.role, required this.ratio,required this.valveModel});

  final String name;
  final String valveId;
  final int role;
  final double ratio;
  final bool active;


  final MotorModel? valveModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.read(sharedPreferencesServiceProvider);

    return GestureDetector(
      onLongPress: () async {
        if(role>3){
          final setting = await context.push("/home/<USER>", extra: valveId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },

      child:  Container(
        height: 160,
        width: MediaQuery.of(context).size.width / ratio ,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(color: valveModel?.device_status==3 ?   Colors.green : Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child:  Text(
                name,
                style: TextStyle(color: valveModel?.device_status==3  ?  Colors.green :Colors.grey, fontSize: 12),
              ),
            ),

            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Image.asset(active && valveModel?.device_status==3 ?  "assets/icons/valve_on.png" :"assets/icons/valve.png", height: 60, width: 60,),
            ),

            gapH8,
            ProgressStepper(
              width: 40,
              height: 15,
              stepCount: 2,
              builder: ( context,index, widthOfStep) {
                if (index == 1) {
                  return ProgressStepWithArrow(
                    width: widthOfStep,
                    height: 15,
                    defaultColor: Colors.grey,
                    progressColor: Colors.green,
                    borderWidth: 1,
                    wasCompleted: valveModel?.mobile_status == 3,
                    child: Center(
                      child: Text(
                        index.toString(),
                        style: const TextStyle(
                            color: Colors.white,fontSize: 8
                        ),
                      ),
                    ),
                  );
                }
                return ProgressStepWithChevron(
                  width: widthOfStep,
                  height: 15,
                  defaultColor: Colors.grey,
                  progressColor: Colors.green,
                  borderWidth: 1,
                  wasCompleted: valveModel?.device_status == 3,
                  child: Center(
                    child: Text(
                      index.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8
                      ),
                    ),
                  ),
                );
              },
            ),
            Consumer(builder: (context, WidgetRef ref, child) {
              final userActive = ref
                  .watch(userProfileProvider)
                  .asData
                  ?.value
                  ?.role ??
                  5;
              return Center(

                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FlutterSwitch(
                      width: 60.0,
                      height: 25.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 25.0,
                      value: active && valveModel?.device_status == 3 && valveModel?.mobile_status == 3 ,
                      borderRadius: 30.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {
                        if(active!=null && active!){
                          EasyLoading.show();
                          if (await hasNetwork()) {
                            if (userActive == 0) {
                              EasyLoading.showError(
                                  'Something wrong with your device.');
                            } else if (userActive == 1 ) {
                              EasyLoading.showError(
                                  'You do not have enough permission. Please contact concern admins for activating the user.');
                            } else if (userActive == 2) {
                              if (valveModel?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startValve(valveId);
                                // await ref.read(divisionRepositoryProvider).
                                // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopValve(valveId);
                              }
                              // await ref.read(divisionRepositoryProvider).
                              // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                            } else if (userActive >=3) {
                              if (valveModel?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startValve(valveId);
                                // await ref.read(divisionRepositoryProvider).
                                //  addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopValve(valveId);
                                //  await ref.read(divisionRepositoryProvider).
                                // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              }
                            }else{
                              EasyLoading.showError(
                                  'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                            }
                          } else {
                            EasyLoading.showError(
                                'No internet Connection');
                          }
                        }
                      }),
                ),
              );
            }),


          ],
        ),
      ),
    );

  }

}



class CommonValveWidget extends HookConsumerWidget{

  CommonValveWidget({ required this.name,
    required this.valveId, required this.active,required this.role, required this.ratio,required this.valveModel});

  final String name;
  final String valveId;
  final int role;
  final double ratio;
  final bool active;


  final MotorModel? valveModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.read(sharedPreferencesServiceProvider);
    final valveState = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider(valveId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valveState.value = MotorModel(
                output_status: datasnapshot['output_status'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valveState.value.device_status == valveState.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return GestureDetector(
      onLongPress: () async {
        if(role>3){
          final setting = await context.push("/home/<USER>", extra: valveId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },

      child:  Container(
        height: 150,
        width: MediaQuery.of(context).size.width / ratio ,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(color: valveModel?.device_status==3 ?   Colors.green : Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child:  Text(
                name,
                style: TextStyle(color: valveState.value?.device_status==3  ?  Colors.green :Colors.grey, fontSize: 12),
              ),
            ),

            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Image.asset(active && valveState.value?.device_status==3 ?  "assets/icons/valve_on.png" :"assets/icons/valve.png", height: 60, width: 60,),
            ),

            gapH8,
            Consumer(builder: (context, WidgetRef ref, child) {
              final userActive = ref
                  .watch(userProfileProvider)
                  .asData
                  ?.value
                  ?.role ??
                  5;
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FlutterSwitch(
                      width: 60.0,
                      height: 25.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 25.0,
                      value: active && valveState.value?.device_status == 3 && valveState.value?.mobile_status == 3 ,
                      borderRadius: 30.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {
                        if(active!=null && active!){
                          EasyLoading.show();
                          if (await hasNetwork()) {
                            if (userActive == 0) {
                              EasyLoading.showError(
                                  'Something wrong with your device.');
                            } else if (userActive == 1 ) {
                              EasyLoading.showError(
                                  'You do not have enough permission. Please contact concern admins for activating the user.');
                            } else if (userActive == 2) {
                              if (valveState.value?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startValve(valveId);
                                // await ref.read(divisionRepositoryProvider).
                                // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopValve(valveId);
                              }
                              // await ref.read(divisionRepositoryProvider).
                              // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                            } else if (userActive >=3) {
                              if (valveState.value?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startValve(valveId);
                                // await ref.read(divisionRepositoryProvider).
                                //  addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopValve(valveId);
                                //  await ref.read(divisionRepositoryProvider).
                                // addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              }
                            }else{
                              EasyLoading.showError(
                                  'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                            }
                          } else {
                            EasyLoading.showError(
                                'No internet Connection');
                          }
                        }
                      }),
                ),
              );
            }),
            ProgressStepper(
              width: 40,
              height: 10,
              stepCount: 2,
              builder: ( context,index, widthOfStep) {
                if (index == 1) {
                  return ProgressStepWithArrow(
                    width: widthOfStep,
                    height: 10,
                    defaultColor: Colors.grey,
                    progressColor: Colors.green,
                    borderWidth: 1,
                    wasCompleted: valveModel?.mobile_status == 3,
                    child: Center(
                      child: Text(
                        index.toString(),
                        style: const TextStyle(
                            color: Colors.white,fontSize: 8
                        ),
                      ),
                    ),
                  );
                }
                return ProgressStepWithChevron(
                  width: widthOfStep,
                  height: 10,
                  defaultColor: Colors.grey,
                  progressColor: Colors.green,
                  borderWidth: 1,
                  wasCompleted: valveModel?.device_status == 3,
                  child: Center(
                    child: Text(
                      index.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8
                      ),
                    ),
                  ),
                );
              },
            ),

          ],
        ),
      ),
    );

  }

}



class ValveHaripurWebWidget extends HookConsumerWidget{

  ValveHaripurWebWidget({required this.size, required this.appDimension, required this.name,
    required this.valveId, required this.role, required this.motorModel});

  final Size size;

  final AppDimension appDimension;

  final String name;

  final String valveId;

  final int role;

  final MotorModel? motorModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return  Container(
      height: 140,
      child: Column(
        children: [
          Container(
              height: 20,
              child: Text(name)
          ),
          Image.asset(
            motorModel?.output_status==5 ?
            "assets/icons/valve_error.png" : motorModel?.output_status==3 ?
            "assets/icons/valve_on.png" : motorModel?.output_status==4 ?
            "assets/icons/valve_loading.png" : "assets/icons/valve.png",
            height: appDimension.valveHeight(
                x: size.width, y: size.height),
            width: appDimension.valveWidth(
                x: size.width, y: size.height),
          ),
          SizedBox(height: 10),
          Consumer(
              builder: (context,ref,child) {
                final prefs = ref.read(sharedPreferencesServiceProvider);

                final userActive = ref
                    .watch(userProfileProvider)
                    .asData
                    ?.value
                    ?.role ??
                    5;
                return FlutterSwitch(
                    width: 50.0,
                    height: 20.0,
                    activeColor: Colors.green,
                    inactiveColor: Colors.grey,
                    valueFontSize: 10.0,
                    toggleSize: 20.0,
                    value: motorModel?.device_status == 3 && motorModel?.mobile_status == 3,
                    borderRadius: 30.0,
                    padding: 4.0,
                    showOnOff: true,
                    onToggle: (val) async {
                      EasyLoading.show();
                      if (userActive == 0) {
                        EasyLoading.showError(
                            'Something wrong with your device.');
                      } else if (userActive == 1 ) {
                        EasyLoading.showError(
                            'You do not have enough permission. Please contact concern admins for activating the user.');
                      } else if (userActive == 2) {
                        if (motorModel?.device_status != 3) {
                          await ref
                              .read(divisionRtdbServiceProvider)
                              .startValve(valveId);
                          await ref.read(divisionRepositoryProvider).
                          addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                        } else {
                          await ref
                              .read(divisionRtdbServiceProvider)
                              .stopValve(valveId);
                        }
                        await ref.read(divisionRepositoryProvider).
                        addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                      } else if (userActive >=3) {
                        if (motorModel?.device_status != 3) {
                          await ref
                              .read(divisionRtdbServiceProvider)
                              .startValve(valveId);
                          await ref.read(divisionRepositoryProvider).
                          addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                        } else {
                          await ref
                              .read(divisionRtdbServiceProvider)
                              .stopValve(valveId);
                          await ref.read(divisionRepositoryProvider).
                          addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                        }
                      }else{
                        EasyLoading.showError(
                            'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                      }
                    });
              }
          ),
        ],
      ),
    );
  }

}

class HaripurValveWidget extends HookConsumerWidget{

  HaripurValveWidget({required this.role,required this.size,required this.valveId, required this.appDimension, required this.name, required this.motorModel});

  final Size size;

  final AppDimension appDimension;

  final String name;

  final String valveId;

  final int role;

  final MotorModel? motorModel;


  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return  GestureDetector(
      onLongPress: () async {
        if(role>=3){
          final setting = await context.push("/home/<USER>", extra: valveId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },
      child: Container(
        height: 115,
        child: Column(
          children: [
            Container(
                height: 15,
                width:appDimension.valveWidth(
                    x: size.width, y: size.height) ,
                child: Text(name+valveId, style: TextStyle(fontSize: 10),maxLines: 2,)
            ),
            Image.asset(
              motorModel?.output_status==5 ?
              "assets/icons/valve_error.png" : motorModel?.output_status==3 ?
              "assets/icons/valve_on.png" : motorModel?.output_status==4 ?
              "assets/icons/valve_loading.png" : "assets/icons/valve.png",
              height: appDimension.valveHeight(
                  x: size.width, y: size.height),
              width: appDimension.valveWidth(
                  x: size.width, y: size.height),
            ),
            SizedBox(height: 10),
            Consumer(
              builder: (context, WidgetRef ref, child) {
                final prefs = ref.read(sharedPreferencesServiceProvider);

                final userActive = ref
                    .watch(userProfileProvider)
                    .asData
                    ?.value
                    ?.role ??
                    5;
                return FlutterSwitch(
                    width: 45.0,
                    height: 20.0,
                    activeColor: Colors.green,
                    inactiveColor: Colors.grey,
                    valueFontSize: 10.0,
                    toggleSize: 20.0,
                    value: motorModel?.device_status == 3,
                    borderRadius: 30.0,
                    padding: 4.0,
                    showOnOff: true,
                    onToggle: (val) async {
                        EasyLoading.show();
                          if (userActive == 0) {
                            EasyLoading.showError(
                                'Something wrong with your device.');
                          } else if (userActive == 1 ) {
                            EasyLoading.showError(
                                'You do not have enough permission. Please contact concern admins for activating the user.');
                          } else if (userActive == 2) {
                            if (motorModel?.device_status != 3) {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .startValve(valveId);
                              await ref.read(divisionRepositoryProvider).
                              addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                            } else {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .stopValve(valveId);
                            }
                            await ref.read(divisionRepositoryProvider).
                            addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                          } else if (userActive >=3) {
                            if (motorModel?.device_status != 3) {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .startValve(valveId);
                              await ref.read(divisionRepositoryProvider).
                              addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                            } else {
                              await ref
                                  .read(divisionRtdbServiceProvider)
                                  .stopValve(valveId);
                              await ref.read(divisionRepositoryProvider).
                              addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                            }
                          }else{
                            EasyLoading.showError(
                                'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                          }
                      }

                    );
              }
            ),
          ],
        ),
      ),
    );
  }

}



class SindhulpalchowkValveWidget extends HookConsumerWidget{

  SindhulpalchowkValveWidget({required this.role,required this.size,required this.valveId,
    required this.appDimension, required this.name, required this.valveModel, required this.showProgress});

  final Size size;

  final SindhupalchowkDimension appDimension;

  final String name;

  final String valveId;

  final int role;

  final MotorModel? valveModel;

  final bool showProgress;


  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return  GestureDetector(
      onLongPress: () async {
        /*if(role>3){
          final setting = await context.push("/home/<USER>", extra: valveId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }*/
      },
      child: Container(
          height: appDimension.valveHeight(x: size.width, y: size.height),
          width: appDimension.valveWidth(x: size.width, y: size.height),
          child: Stack(
            children: [
              Center(child: Image.asset((valveModel?.device_status == 3 && valveId == "10") ? "assets/icons/valve_on.png" :
              (valveModel?.device_status != 3 && valveId == "11") ? "assets/icons/valve_on.png" : "assets/icons/valve.png", height: 100, width: 100,)),
              if(showProgress)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    gapH8,
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: CircularProgressIndicator(
                        backgroundColor: Colors.white,
                        color: Colors.greenAccent,
                        strokeCap: StrokeCap.round,
                        strokeWidth: 5,
                        value: 0.75,
                      ),
                    ),
                  ],
                ),
              )
            ],
          )),
    );
  }

}


Future<bool> hasNetwork() async {
  try {
    final result = await InternetAddress.lookup('google.com');
    return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
  } on SocketException catch (_) {
    return false;
  }
}

class ScadaValveWidget extends HookConsumerWidget{

  ScadaValveWidget({required this.role,required this.size,required this.valveId,
    required this.height, required this.width,
    required this.name, required this.valveModel, required this.showProgress});

  final Size size;

  final double height;

  final double width;

  final String name;

  final String valveId;

  final int role;

  final MotorModel? valveModel;

  final bool showProgress;


  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return  GestureDetector(
      onLongPress: () async {
        /*if(role>3){
          final setting = await context.push("/home/<USER>", extra: valveId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }*/
      },
      child: Container(
          height: height,
          width: width,
          child: Stack(
            children: [
              Center(child: Image.asset((valveModel?.device_status == 3 && valveId == "10") ? "assets/icons/valve_on.png" :
              (valveModel?.device_status != 3 && valveId == "11") ? "assets/icons/valve_on.png" : "assets/icons/valve.png", height: 100, width: 100,)),
              if(showProgress)
                const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      gapH8,
                      SizedBox(
                        height: 40,
                        width: 40,
                        child: CircularProgressIndicator(
                          backgroundColor: Colors.white,
                          color: Colors.greenAccent,
                          strokeCap: StrokeCap.round,
                          strokeWidth: 5,
                          value: 0.75,
                        ),
                      ),
                    ],
                  ),
                )
            ],
          )),
    );
  }

}


