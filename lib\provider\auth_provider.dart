

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/model/dashboard_model.dart';

import '../app_auth.dart';
import '../repository/auth_repository.dart';

final firebaseAuthProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);
final authStateChangesProviderAuth = StreamProvider<User?>((ref) => ref.watch(firebaseAuthProvider).authStateChanges());
final authRepositoryProvider = Provider<IAuthRepository>((ref) => AuthRepository(ref));
final authStateNotifierProviderAuth = StateNotifierProvider<AuthStateNotifier, User?>((ref) => AuthStateNotifier(ref));


