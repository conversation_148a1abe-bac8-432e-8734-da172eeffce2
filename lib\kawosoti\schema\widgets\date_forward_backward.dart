import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../utils/format.dart';

class DateForwardBackward extends HookConsumerWidget {
  const DateForwardBackward({Key? key, required this.onDateChange})
      : super(key: key);

  final Function(DateTime date) onDateChange;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final date = useState(DateTime.now());
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: IconButton(
              onPressed: () {
                date.value = DateTime.now();
                onDateChange(date.value);
              },
              icon: Icon(Icons.refresh_outlined, color: Colors.black)),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: IconButton(
              onPressed: () {
                date.value = date.value.subtract(Duration(days: 1));
                onDateChange(date.value);
              },
              icon: Icon(Icons.chevron_left_outlined, color: Colors.black)),
        ),
        InkWell(
          onTap: () async {
            final DateTime? pickedDate = await showDatePicker(
              context: context,
              initialDate: date.value,
              firstDate: DateTime(2020),
              lastDate: DateTime(2030),
            );

            if (pickedDate != null) {
              date.value = pickedDate;
              onDateChange(date.value);
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Text(
                  Format.getDayDateOnly(date.value),
                  style: TextStyle(fontSize: 16, color: Colors.black),
                ),
                SizedBox(width: 4),
                Icon(Icons.calendar_today, size: 16, color: Colors.black),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: IconButton(
              onPressed: () {
                date.value = date.value.add(Duration(days: 1));
                onDateChange(date.value);
              },
              icon: Icon(Icons.chevron_right_outlined, color: Colors.black)),
        ),
      ],
    );
  }
}

class MonthForwardBackward extends HookConsumerWidget {
  const MonthForwardBackward({Key? key, required this.onDateChange})
      : super(key: key);

  final Function(DateTime date) onDateChange;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final date = useState(DateTime.now());
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: IconButton(
              onPressed: () {
                date.value = DateTime.now();
                onDateChange(date.value);
              },
              icon: Icon(Icons.refresh_outlined, color: Colors.black)),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: IconButton(
              onPressed: () {
                date.value = DateTime(date.value.year, date.value.month - 1, 1);
                onDateChange(date.value);
              },
              icon: Icon(Icons.chevron_left_outlined, color: Colors.black)),
        ),
        InkWell(
          onTap: () async {
            final DateTime? pickedDate = await showDatePicker(
              context: context,
              initialDate: date.value,
              firstDate: DateTime(2020),
              lastDate: DateTime(2030),
            );

            if (pickedDate != null) {
              // Keep the day as 1 since this is a month picker
              date.value = DateTime(pickedDate.year, pickedDate.month, 1);
              onDateChange(date.value);
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Text(
                  Format.getDateOnly(date.value),
                  style: TextStyle(fontSize: 16, color: Colors.black),
                ),
                SizedBox(width: 4),
                Icon(Icons.calendar_today, size: 16, color: Colors.black),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: IconButton(
              onPressed: () {
                date.value = DateTime(date.value.year, date.value.month + 1);
                onDateChange(date.value);
              },
              icon: Icon(Icons.chevron_right_outlined, color: Colors.black)),
        ),
      ],
    );
  }
}
