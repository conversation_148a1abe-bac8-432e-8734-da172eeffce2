
import 'package:flutter/material.dart';
import 'package:graphite/graphite.dart';
import 'package:si/kawosoti/schema/widgets/chlorine_widget.dart';
import 'package:si/kawosoti/schema/widgets/ph_widget.dart';
import 'package:si/kawosoti/schema/widgets/turbidity_widget.dart';
import 'package:si/kawosoti/schema/widgets/water_source.dart';

class Data {
  final String title;
  final Widget child;

  Data(
      {
        required this.title,
        required this.child});
}

Map<String, Data> datas = {
  "source": Data(
      title: "Source",
      child: WaterSource()),
  "turbidity1": Data(
      title: "Turbidity 1",
      child: TurbidyWidget(name:"Turbidity 1", sensorValue: "1500 NP")),
  "turbidity2": Data(
      title: "Turbidity 2",
      child: TurbidyWidget(name:"Turbidity 2", sensorValue: "1200 NP")),
  "turbidity3": Data(
      title: "Turbidity 3",
      child: TurbidyWidget(name:"Turbidity 3", sensorValue: "900 NP")),
  "turbidity4": Data(
      title: "Turbidity 4",
      child: TurbidyWidget(name: "Turbidity 4", sensorValue: "400 NP")),
  "chlorine": Data(
      title: "Chlorine",
      child: ChlorineWidget(name: "Chlorine", sensorValue: "0.34")),
  "ph": Data(
      title: "PH Scale",
      child: PhWidget(name: "PH Scale", sensorValue: "7.2")),
  "sedimentOne": Data(
      title: "Sediment 1",
      child: SedimentTank()),
  "sedimentTwo": Data(
      title: "Sediment 2",
      child: SedimentTank()),
  "sedimentThree": Data(
      title: "Sediment 3",
      child: SedimentTank()),
  "sedimentFour": Data(
      title: "Sediment 4",
      child: SedimentTank()),
  "sedimentFive": Data(
      title: "Sediment 5",
      child: SedimentTank()),
  "overhead": Data(
      title: "Overhead Tank",
      child: Column(
        children: [
          Tank(),
          SizedBox(height: 5,),
          Text("24%",style: TextStyle(fontSize: 18,color: Colors.black),),

        ],
      ))
};

List<NodeInput> imagePresets = [
  NodeInput(
    id: "source",
    size: const NodeSize(width: 100, height: 100),
    next: [
      EdgeInput(outcome: "turbidity1", type: EdgeArrowType.one),
    ],
  ),
  NodeInput(
      id: "turbidity1",
      size: const NodeSize(width: 110, height: 140),
      next: [
        EdgeInput(outcome: "sedimentOne", type: EdgeArrowType.one),
        EdgeInput(outcome: "sedimentTwo", type: EdgeArrowType.one),
        EdgeInput(outcome: "sedimentThree", type: EdgeArrowType.one),
      ]),
  NodeInput(
      id: "sedimentOne",
      size: const NodeSize(width: 150, height: 130),
      next: [EdgeInput(outcome: "turbidity2", type: EdgeArrowType.one)]),
  NodeInput(
      id: "sedimentTwo",
      size: const NodeSize(width: 150, height: 130),
      next: [EdgeInput(outcome: "turbidity2", type: EdgeArrowType.one)]),
  NodeInput(
      id: "sedimentThree",
      size: const NodeSize(width: 150, height: 130),
      next: [EdgeInput(outcome: "turbidity2", type: EdgeArrowType.one)]),
  NodeInput(
      id: "turbidity2",
      size: const NodeSize(width: 110, height: 140),
      next: [EdgeInput(outcome: "sedimentFour", type: EdgeArrowType.one)]),
  NodeInput(
      id: "sedimentFour",
      size: const NodeSize(width: 150, height: 130),
      next: [EdgeInput(outcome: "turbidity3", type: EdgeArrowType.one)]),
  NodeInput(
      id: "turbidity3",
      size: const NodeSize(width: 110, height: 140),
      next: [EdgeInput(outcome: "sedimentFive", type: EdgeArrowType.one)]),
  NodeInput(
      id: "sedimentFive",
      size: const NodeSize(width: 150, height: 130),
      next: [
        EdgeInput(outcome: "chlorine", type: EdgeArrowType.one),
        EdgeInput(outcome: "turbidity4", type: EdgeArrowType.one),
        EdgeInput(outcome: "ph", type: EdgeArrowType.one),
      ]),
  NodeInput(
      id: "turbidity4",
      size: const NodeSize(width: 110, height: 140),
      next: [
        EdgeInput(outcome: "overhead", type: EdgeArrowType.one),
      ]),
  NodeInput(
      id: "chlorine",
      size: const NodeSize(width: 110, height: 140),
      next: [EdgeInput(outcome: "sedimentFour", type: EdgeArrowType.one)]),
  NodeInput(
      id: "ph",
      size: const NodeSize(width: 110, height: 140),
      next: [EdgeInput(outcome: "sedimentFour", type: EdgeArrowType.one)]),
  NodeInput(
      id: "overhead", size: const NodeSize(width: 120, height: 160), next: []),
];

class CurrentNodeInfo {
  final NodeInput node;
  final Rect rect;
  final Data data;

  CurrentNodeInfo({required this.node, required this.rect, required this.data});
}