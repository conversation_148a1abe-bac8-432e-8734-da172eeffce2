

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import '../widgets/inactive_valve_widget.dart';

class MobileDeukotPage extends HookConsumerWidget{

  final DivisionSettingModel? setting;

  const MobileDeukotPage(this.setting);

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final motor10State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            height: 30,
            color: Colors.blueAccent,
            child: Center(
              child: Text(
                "मुलको पानी",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  Container(
                    height: 30,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(prefs.getUserRole(), "10", setting!.tank10!, 200, 150),
                  gapH24,
                ],
              ),
              Container(
                height: 20,
                width: 40,
                color: Colors.blueAccent,
              ),
              Column(
                children: [
                  gapH16,
                  NewMotorUnit(
                    motorId: '10',
                    motorSettingModel: setting!.motor10!,
                    motorModel: motor10State.value,
                    ratio: 3,
                  ),
                  Container(
                    height: 63,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                ],
              ),

            ],
          ),
          Container(
            height: 20,
            width: double.infinity,
            color: Colors.blueAccent,
          ),
          Column(
            children: [
              Container(
                height: 30,
                width: 20,
                color: Colors.blueAccent,
              ),
              NewTankUnit(prefs.getUserRole(), "11", setting!.tank11!, 200, 300),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  Container(
                    height: 30,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  InactiveValveWidget(name: '3" inch', ratio: 3),
                ],
              ),
              gapW16,
              Column(
                children: [
                  Container(
                    height: 30,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  InactiveValveWidget(name: '3" inch', ratio: 3),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

}