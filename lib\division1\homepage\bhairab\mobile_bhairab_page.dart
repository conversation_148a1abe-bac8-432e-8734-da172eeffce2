

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import '../widgets/inactive_valve_widget.dart';
import '../widgets/tank_widget.dart';

class MobileBhairabPage extends HookConsumerWidget{

  final DivisionSettingModel setting;

  const MobileBhairabPage(this.setting);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);

    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());
    final motor12State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor12State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor12State.value.device_status == motor12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });



    return SingleChildScrollView(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                children: [
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewMotorUnit(
                    motorId: '10',
                    motorSettingModel: setting!.motor10!,
                    motorModel: motor10State.value,
                    ratio: 3.5,
                  ),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                ],
              ),
              Column(
                children: [
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewMotorUnit(
                    motorId: '11',
                    motorSettingModel: setting!.motor11!,
                    motorModel: motor11State.value,
                    ratio: 3.5,
                  ),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                ],
              ),
              Column(
                children: [
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                  NewMotorUnit(
                    motorId: '12',
                    motorSettingModel: setting!.motor12!,
                    motorModel: motor12State.value,
                    ratio: 3.5,
                  ),
                  Container(
                    height: 40,
                    width: 20,
                    color: Colors.blueAccent,
                  ),
                ],
              ),
            ],
          ),
          Container(
            height: 30,
            width: double.infinity,
            color: Colors.blueAccent,
          ),
          Container(
            height: 30,
            width: 20,
            color: Colors.blueAccent,
          ),
          TankBlock(
            tankSettingModel: setting!.tank10!,
            roleType: prefs.getUserRole(),
            tankId: "10",
            height: 250,
            width: 170,
          ),
        ],
      ),
    );


  }

}