import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/division1/homepage/filter_motor_widget.dart';
import 'package:si/division1/homepage/valve_widget.dart';
import 'package:si/division1/homepage/widgets/source_widget.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../constants/app_sizes.dart';
import '../model/motor_model.dart';
import '../provider/division_provider.dart';
import 'display.dart';
import 'filter_plant.dart';
import 'new_tank_unit.dart';

class FaiDhokaPage extends HookConsumerWidget {
  const FaiDhokaPage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor1State = useState<MotorModel>(MotorModel());
    final motor2State = useState<MotorModel>(MotorModel());
    final motor3State = useState<MotorModel>(MotorModel());
    final filter1State = useState<MotorModel>(MotorModel());
    final filter2State = useState<MotorModel>(MotorModel());
    final filter3State = useState<MotorModel>(MotorModel());
    final valve1State = useState<MotorModel>(MotorModel());
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor1'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor1State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor1State.value.device_status == motor1State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor2'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor2State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor2State.value.device_status == motor2State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('motor3'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor3State.value = MotorModel(
                motorAmps: datasnapshot['motorAmps'],
                device_status: datasnapshot['device_status'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                VoltageFaultStatus: datasnapshot['VoltageFaultStatus'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor3State.value.device_status == motor3State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider('valve1'),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve1State.value = MotorModel(
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve1State.value.device_status ==
              valve1State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return siteSetting.when(
        data: (data) {
          prefs.setMotorNumber(data?.motor_number ?? 1);
          prefs.setTankNumber(data?.tank_number ?? 1);
          prefs.setTankName("tankone", data?.tank10?.name ?? "");
          prefs.setTankName("tanktwo", data?.tank20?.name ?? "");
          prefs.setTankName("tankthree", data?.tank30?.name ?? "");
          prefs.setTankName("tankfour", data?.tank40?.name ?? "");
          prefs.setTankName("tankfive", data?.tank50?.name ?? "");
          return BaseScaffold(
            appbarText: "Sandbox",
            showLeftIcon: false,
            showAction: false,
            onActionClick: () async {
              EasyLoading.show();
              final response = await ref
                  .read(divisionRepositoryProvider)
                  .getSiteSetting(prefs.getSiteId());
              ref
                  .read(sharedPreferencesServiceProvider)
                  .setSiteNae(response?.name ?? '');
              EasyLoading.dismiss();
              context.push("/home/<USER>", extra: response);
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  gapH8,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        children: [
                          Container(
                            height: 200,
                            width: 150,
                            child: DisplayWidget(
                              motorName: data!.motor10?.name ?? 'Motor 1',
                              motorModel: motor1State.value,
                              motorState: motor1State.value.device_status,
                              motorId: 'motor1',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor1State.value.voltage,
                              motorAmps: motor1State.value.motorAmps!,
                              motorSettingModel: data!.motor10!,
                            ),
                          ),
                          Container(
                            height: 35,
                            width: 20,
                            color: motor1State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Container(
                            height: 200,
                            width: 150,
                            child: DisplayWidget(
                              motorName: data!.motor20?.name ?? 'Motor 2',
                              motorModel: motor2State.value,
                              motorState: motor2State.value.device_status,
                              motorId: 'motor2',
                              roleType: ref.read(userState).role ?? 1,
                              voltage: motor2State.value.voltage,
                              motorAmps: motor2State.value.motorAmps!,
                              motorSettingModel: data!.motor20!,
                            ),
                          ),
                          Container(
                            height: 35,
                            width: 20,
                            color: motor2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                          ),
                        ],
                      ),
                    ],
                  ),
                  SourceWidget("झरना"),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 5,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex:2,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    height: 35,
                                    width: 20,
                                    color: motor1State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                  ),
                                  Container(
                                    height: 180,
                                    width: 110,
                                    child: FilterDisplayWidget(
                                      filterName: data!.filter10?.name ?? 'Filter 1',
                                      roleType: ref.read(userState).role ?? 1,
                                      filterId: 'filter1',
                                    ),
                                  ),
                                  Container(
                                    height: 35,
                                    width: 20,
                                    color: motor1State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                  ),
                                  Container(
                                    height: 170,
                                    width: MediaQuery.of(context).size.width / 4 ,
                                    decoration: BoxDecoration(
                                        border: Border.all(color: Colors.black)),
                                    child: TankBlockWidget(
                                        ref.read(userState).role ?? 1,
                                        'tank1',
                                        data!.tank10!,
                                        140,
                                        MediaQuery.of(context).size.width / 4  - 10),
                                  ),
                                  Container(
                                    height: 220,
                                    width: 20,
                                    color: Colors.blueAccent ,
                                  ),
                                  Container(
                                    height: 30,
                                    width: double.infinity,
                                    color: Colors.blueAccent ,
                                  ),
                                  Container(
                                    height: 200,
                                    width: 20,
                                    color: Colors.blueAccent ,
                                  ),
                                  Container(
                                    height: 170,
                                    width: MediaQuery.of(context).size.width / 4 ,
                                    decoration: BoxDecoration(
                                        border: Border.all(color: Colors.black)),
                                    child: TankBlockWidget(
                                        ref.read(userState).role ?? 1,
                                        'tank4',
                                        data!.tank40!,
                                        140,
                                        MediaQuery.of(context).size.width / 4  - 10),
                                  ),
                                  Container(
                                    height: 80,
                                    width: 20,
                                    color: Colors.blueAccent,
                                  ),

                                  Container(
                                    height: 20,
                                    width: 20,
                                    color:  Colors.blueAccent ,
                                  ),
                                ],
                              ),

                            ),
                            Expanded(
                              flex: 3,
                              child: Column(
                                children: [
                                  Container(
                                    height: 35,
                                    width: 20,
                                    color: filter2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                  ),
                                  Container(
                                    height: 180,
                                    width: 110,
                                    child: FilterDisplayWidget(
                                      filterName: data!.filter20?.name ?? 'Filter 2',
                                      roleType: ref.read(userState).role ?? 1,
                                      filterId: 'filter2',
                                    ),
                                  ),
                                  Container(
                                    height: 35,
                                    width: 20,
                                    color: filter2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                  ),
                                  Padding(
                                    padding: horPadding16,
                                    child: Padding(
                                      padding: const EdgeInsets.only(right: 24.0),
                                      child: Container(
                                        height: 20,
                                        width: double.infinity,
                                        color: filter2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                      ),
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(left: 16.0),
                                        child: Container(
                                          height: 200,
                                          width: 20,
                                          color: filter2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                        ),
                                      ),

                                      Expanded(
                                        child:  Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                          child: Column(
                                            children: [
                                              Container(
                                                height: 30,
                                                width: 20,
                                                color: filter2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                              ),
                                              Container(
                                                height: 150,
                                                width: MediaQuery.of(context).size.width / 3 ,
                                                decoration: BoxDecoration(
                                                    border: Border.all(color: Colors.black)),
                                                child: TankBlockWidget(
                                                    ref.read(userState).role ?? 1,
                                                    'tank2',
                                                    data!.tank20!,
                                                    120,
                                                    MediaQuery.of(context).size.width / 3 - 20),
                                              ),
                                              Align(
                                                alignment: Alignment.topRight,
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                                  child: Container(
                                                    height: 20,
                                                    width: 20,
                                                    color: Colors.blueAccent,
                                                  ),
                                                ),
                                              ),

                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child:  Column(
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(left:16.0),
                                              child: ValveDisplayWidget(
                                                height: 150,
                                              ),
                                            ),
                                            Stack(
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsets.only(left: 16.0),
                                                  child: Align(
                                                    alignment: Alignment.topLeft,
                                                    child: Container(
                                                      height: 50,
                                                      width: 20,
                                                      color: valve1State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                                    ),
                                                  ),

                                                ),
                                                Positioned(
                                                  bottom: 0,
                                                  left: 0,
                                                  child: Container(
                                                    height: 30,
                                                    width: 20,
                                                    color: valve1State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                                  ),
                                                ),
                                              ],
                                            ),

                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(right: 24.0),
                                        child: Container(
                                          height: 220,
                                          width: 20,
                                          color: Colors.blueAccent,
                                        ),
                                      ),


                                    ],
                                  ),
                                  Container(
                                    height: 200,
                                    width: 125,
                                    child: DisplayWidget(
                                      motorName: data!.motor30?.name ?? 'Motor 3',
                                      motorModel: motor3State.value,
                                      motorState: motor3State.value.device_status,
                                      motorId: 'motor3',
                                      roleType: ref.read(userState).role ?? 1,
                                      voltage: motor3State.value.voltage,
                                      motorAmps: motor3State.value.motorAmps!,
                                      motorSettingModel: data!.motor30!,
                                    ),
                                  ),
                                  Container(
                                    height: 30,
                                    width: 20,
                                    color: filter2State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                                  ),
                                  Container(
                                    height: 180,
                                    width: MediaQuery.of(context).size.width / 3 ,
                                    decoration: BoxDecoration(
                                        border: Border.all(color: Colors.black)),
                                    child: TankBlockWidget(
                                        ref.read(userState).role ?? 1,
                                        'tank3',
                                        data!.tank30!,
                                        150,
                                        MediaQuery.of(context).size.width / 3  - 10),
                                  ),

                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Column(
                            children: [
                              Container(
                                height: 35,
                                width: 20,
                                color: motor1State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                              ),
                              Container(
                                height: 180,
                                width: 110,
                                child: FilterDisplayWidget(
                                  filterName: data!.filter30?.name ?? 'Filter 3',
                                  roleType: ref.read(userState).role ?? 1,
                                  filterId: 'filter3',
                                ),
                              ),
                              Container(
                                height: 400,
                                width: 0,
                                color: filter3State.value.device_status == 1 ? Colors.blueAccent : Colors.grey,
                              ),
                              Container(
                                height: 200,
                                width: MediaQuery.of(context).size.width / 3 ,
                                decoration: BoxDecoration(
                                    border: Border.all(color: Colors.black)),
                                child: TankBlockWidget(
                                    ref.read(userState).role ?? 1,
                                    'tank5',
                                    data!.tank50!,
                                    170,
                                    MediaQuery.of(context).size.width / 3  - 10),
                              ),
                              Container(
                                height: 325,
                                width: 20,
                                color: Colors.blueAccent,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    height: 20,
                    width: double.infinity,
                    color: Colors.blueAccent ,
                  ),

                  gapH32,
                ],
              ),
            ),
          );
        },
        error: (er, st) {
          return Container();
        },
        loading: () => const LoadingIndicator());
  }
}
