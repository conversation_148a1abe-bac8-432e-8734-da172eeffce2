

import 'dart:math';
import 'dart:developer' as developer;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:progress_stepper/progress_stepper.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:text_scroll/text_scroll.dart';

import '../../../constants/app_sizes.dart';
import '../../../utils/app_dimension.dart';
import '../../../utils/format.dart';
import '../../model/motor_model.dart';
import '../../model/motor_setting_model.dart';
import '../../provider/division_provider.dart';
import 'valve_widget.dart';

class MotorPainter extends HookConsumerWidget {

   MotorPainter({
     this.isRight = true,
    this.height = 210,
    this.width = 160,
     required this.motorId,
     required this.motorModel,
     required this.motorSettingModel,
     this.active = true
});

  final bool isRight;
  final double height;
  final double width;
   final String motorId;
   final MotorSettingModel? motorSettingModel;
   final MotorModel? motorModel;
   final bool? active;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(
            color: Colors.black, // Border color
            width: 2.0,        // Border width
          ),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 0,
              left: 0,
              child: Image.asset(
                "assets/icons/underground.png",
                height: 50,
                width: width,
                fit: BoxFit.fill,
              ),
            ),
            Positioned(
              bottom: 50,
              left: width / 2 - 20,
              child: Container(
                width: 20,
                height: 20,
                color: Colors.blue,
              ),
            ),
            Positioned(
              bottom: 65,
              left: 0,
              right: 20,
              child: RamechhapMotorMove(motorModel: motorModel!,motorSettingModel: motorSettingModel!,faultStatus: motorModel?.VoltageFaultStatus==1,),
            ),
            Positioned(
              bottom: 90,
              left: isRight ? width / 2 + 15 : 0,
              child: Container(
                width: isRight ? 80 : 40,
                height: 12,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFF363636),      // Minimal black (Light Gray)
                      Colors.white,           // White color
                      Color(0xFF363636),      // Minimal black (Light Gray)
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  border: const Border(
                    top: BorderSide(color: Colors.black, width: 2),
                    left: BorderSide.none,
                    bottom: BorderSide(color: Colors.black, width: 2),
                    right: BorderSide.none, // No border on the right
                  ),

                ),
              ),
            ),
            Positioned(
              top: 8,
              left: 4,
              right: 4,
              child: ControlPanel(motorSettingModel: motorSettingModel!,motorModel: motorModel!,
              motorId: motorId,),
            ),
          ],
        ),
      ),
    );
  }
}



class ControlPanel extends HookConsumerWidget {

  const ControlPanel({required this.motorModel,required this.motorId, required this.motorSettingModel, this.active = true});

  final MotorModel motorModel;
  final MotorSettingModel motorSettingModel;
  final bool active;
  final String motorId;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = motorModel.VoltageFaultStatus==1 ? "Voltage Fault"  : "Normal";
    return Container(
      height: 60,
      width: 100,
      decoration: const BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.black38, Colors.black38],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),

        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        children: [
          Expanded(
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(motorSettingModel.name, style: TextStyle(color: Colors.white, fontSize: 12),maxLines: 1, textAlign: TextAlign.center,),
                    Text("${motorModel.motorAmps}A, ${motorModel.voltage}V,",
                      style: const TextStyle(color: Colors.white, fontSize: 10),textAlign: TextAlign.center,
                      maxLines: 2,
                    ),
                    //Text("22.35A/220.45V", style: TextStyle(color: Colors.black, fontSize: 12),),
                    // Text("Normal", style: TextStyle(color: Colors.black, fontSize: 12),),


                  ],
                ),
              )),
          Consumer(builder: (context, WidgetRef ref, child) {
            final userActive = ref
                .watch(userProfileProvider)
                .asData
                ?.value
                ?.role ??
                5;
            final prefs = ref.read(sharedPreferencesServiceProvider);
            return Center(
              child: Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FlutterSwitch(
                        width: 40.0,
                        height: 20.0,
                        activeColor: Colors.green,
                        inactiveColor: Colors.grey,
                        valueFontSize: 8.0,
                        toggleSize: 15.0,
                        value: motorModel.device_status ==3 && motorModel.mobile_status == 3 ,
                        borderRadius: 24.0,
                        padding: 2.0,
                        showOnOff: true,
                        onToggle: (val) async {
                          if(active!=null && active!){
                            EasyLoading.show();
                              if (userActive == 0) {
                                EasyLoading.showError(
                                    'Something wrong with your device.');
                              } else if (userActive == 1 ) {
                                EasyLoading.showError(
                                    'You do not have enough permission. Please contact concern admins for activating the user.');
                              } else if (userActive == 2) {
                                if (motorModel?.device_status != 3) {
                                  developer.log("Motor Id: $motorId");
                                  developer.log("Motor  Model: $motorModel");
                                  await ref
                                      .read(divisionRtdbServiceProvider)
                                      .startMotor(motorId);
                                  await ref.read(divisionRepositoryProvider).
                                  addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                                } else {
                                  await ref
                                      .read(divisionRtdbServiceProvider)
                                      .stopMotor(motorId);
                                }
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              } else if (userActive >=3) {
                                if (motorModel?.device_status != 3) {
                                  await ref
                                      .read(divisionRtdbServiceProvider)
                                      .startMotor(motorId);
                                  await ref.read(divisionRepositoryProvider).
                                  addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                                } else {
                                  await ref
                                      .read(divisionRtdbServiceProvider)
                                      .stopMotor(motorId);
                                  await ref.read(divisionRepositoryProvider).
                                  addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                                }
                              }else{
                                EasyLoading.showError(
                                    'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                              }

                          }
                        }),
                    gapH4,
                    ProgressStepper(
                      width: 40,
                      height: 15,
                      stepCount: 2,
                      builder: ( context,index, widthOfStep) {
                        if (index == 1) {
                          return ProgressStepWithArrow(
                            width: widthOfStep,
                            height: 15,
                            defaultColor: Colors.grey,
                            progressColor: Colors.green,
                            borderWidth: 1,
                            wasCompleted: motorModel.mobile_status == 3,
                            child: Center(
                              child: Text(
                                index.toString(),
                                style: const TextStyle(
                                  color: Colors.white,fontSize: 10
                                ),
                              ),
                            ),
                          );
                        }
                        return ProgressStepWithChevron(
                          width: widthOfStep,
                          height: 15,
                          defaultColor: Colors.grey,
                          progressColor: Colors.green,
                          borderWidth: 1,
                          wasCompleted: motorModel.device_status == 3,
                          child: Center(
                            child: Text(
                              index.toString(),
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}


class MotorMove extends HookConsumerWidget {
  MotorMove({required this.motorModel, required this.motorSettingModel,
   required this.faultStatus});

  final MotorModel motorModel;
  final bool faultStatus;
  final MotorSettingModel motorSettingModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 45,
      width: 45,
      decoration: new BoxDecoration(
          color: Colors.transparent,
          shape: BoxShape.circle,
        border: Border.all(color: faultStatus ?  Colors.red   : ((motorModel.motorAmps ?? 0.0)>(motorSettingModel.current_threshold ?? 0.0)) ? Colors.green : Colors.grey ,width: 5)),
      child: Stack(
        children: [
          Visibility(
            visible: !((motorModel.motorAmps ?? 0.0)>(motorSettingModel.current_threshold ?? 0.0)),
            child: Center(
              child: Container(
                height: 30,
                width: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: faultStatus ? Colors.red  : Colors.grey,
                ),
              ),
            ),
          ),
          Visibility(
            visible: ! ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          Visibility(
            visible: ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: SpinKitDualRing(
                color: faultStatus ? Colors.red  : Colors.green,
                size: 30.0,
              ),
            ),
          ),
          Visibility(
            visible: ((motorModel?.motorAmps ?? 0.0) > (motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: LoadingAnimationWidget.beat(
                color: faultStatus ? Colors.red  : Colors.green,
                size: 12,
              ),
            ),
          ),
          Visibility(
            visible: ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: LoadingAnimationWidget.hexagonDots(
                color: faultStatus ? Colors.red  : Colors.green,
                size: 25,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RamechhapMotorMove extends HookConsumerWidget {
  RamechhapMotorMove({required this.motorModel, required this.motorSettingModel,
    required this.faultStatus});

  final MotorModel motorModel;
  final bool faultStatus;
  final MotorSettingModel motorSettingModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 60,
      width: 60,
      decoration: new BoxDecoration(
          color: Colors.transparent,
          shape: BoxShape.circle,
          border: Border.all(color: faultStatus ?  Colors.red   : ((motorModel.motorAmps ?? 0.0)>(motorSettingModel.current_threshold ?? 0.0)) ? Colors.green : Colors.grey ,width: 5)),
      child: Stack(
        children: [
          Visibility(
            visible: !((motorModel.motorAmps ?? 0.0)>(motorSettingModel.current_threshold ?? 0.0)),
            child: Center(
              child: Container(
                height: 45,
                width: 45,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: faultStatus ? Colors.red  : Colors.grey,
                ),
              ),
            ),
          ),
          Visibility(
            visible: ! ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: Container(
                height: 35,
                width: 35,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          Visibility(
            visible: ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: SpinKitDualRing(
                color: faultStatus ? Colors.red  : Colors.green,
                size: 35.0,
              ),
            ),
          ),
          Visibility(
            visible: ((motorModel?.motorAmps ?? 0.0) > (motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: LoadingAnimationWidget.beat(
                color: faultStatus ? Colors.red  : Colors.green,
                size: 16,
              ),
            ),
          ),
          Visibility(
            visible: ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
            child: Center(
              child: LoadingAnimationWidget.hexagonDots(
                color: faultStatus ? Colors.red  : Colors.green,
                size: 35,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

