

import 'package:firebase_database/firebase_database.dart';

import '../schema/model/site_model.dart';
import '../schema/model/user_model.dart';


abstract class ISiteFirestoreRepository {
  Future<List<SiteFireStoreModel?>> getSiteHistoryData({required String siteId,DateTime? date, DateTime? startAfterDate});
  Future<List<SiteFireStoreModel?>> getChartData({required String siteId,DateTime? date});
  Future<int> getLength({required String siteId,DateTime dateTime});
  Future<SiteNameModel> getNames();
  Stream<DatabaseEvent> readSite(String siteId);
  Future<UserModel> getUsers(String userId);
}