import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/alertdialog/alert_dialogs.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../../provider/auth_provider.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../widgets/motor_widget.dart';
import '../widgets/tank_widget.dart';

class WebHaripurPage extends HookConsumerWidget {
  const WebHaripurPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var size = MediaQuery.of(context).size;
    final appDimension = ref.read(dimensionProvider);
    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());
    final motor12State = useState<MotorModel>(MotorModel());
    final motor13State = useState<MotorModel>(MotorModel());
    final valve10State = useState<MotorModel>(MotorModel());
    final valve11State = useState<MotorModel>(MotorModel());
    final valve12State = useState<MotorModel>(MotorModel());
    final prefs = ref.read(sharedPreferencesServiceProvider);

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        motor10State.value = MotorModel(
            motorAmps: datasnapshot['current'],
            output_status: datasnapshot['output_status'],
            voltage: datasnapshot['voltage'],
            time: datasnapshot['time'],
            VoltageFaultStatus: datasnapshot['voltage_fault'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (motor10State.value.device_status ==
          motor10State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        motor11State.value = MotorModel(
            motorAmps: datasnapshot['current'],
            output_status: datasnapshot['output_status'],
            voltage: datasnapshot['voltage'],
            time: datasnapshot['time'],
            VoltageFaultStatus: datasnapshot['voltage_fault'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (motor11State.value.device_status ==
          motor11State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        motor12State.value = MotorModel(
            motorAmps: datasnapshot['current'],
            output_status: datasnapshot['output_status'],
            voltage: datasnapshot['voltage'],
            time: datasnapshot['time'],
            VoltageFaultStatus: datasnapshot['voltage_fault'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (motor12State.value.device_status ==
          motor12State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("13"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor13State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor13State.value.device_status ==
              motor13State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("10"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        valve10State.value = MotorModel(
            output_status: datasnapshot['output_status'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (valve10State.value.device_status ==
          valve10State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("11"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        valve11State.value = MotorModel(
            output_status: datasnapshot['output_status'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (valve11State.value.device_status ==
          valve11State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("12"),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        valve12State.value = MotorModel(
            output_status: datasnapshot['output_status'],
            device_status: datasnapshot['device_status'],
            mobile_status: datasnapshot['mobile_status']);
      }
      if (valve12State.value.device_status ==
          valve12State.value.mobile_status) {
        EasyLoading.dismiss();
      }
    });

    return BaseScaffold(
        showAppBar: false,
        showLeftIcon: false,
        child: Center(
          child: Container(
            height: size.height,
            width: size.width,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF87CEFA), // Light Blue (Sky color)
                  Color(0xFFE0FFFF),
                  Color(0xFF87CEFA), // Light Blue (Sky color)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Positioned(
                        right: 32,
                        top: 8,
                        child: IconButton(
                          icon: const Icon(Icons.logout_outlined),
                          onPressed: () async {
                            final bool didRequestSignOut =
                                await showAlertDialog(
                                  context: context,
                                  title: 'Logout',
                                  content:
                                  'Are you sure you want to logout?',
                                  cancelActionText: 'Cancel',
                                  defaultActionText: 'Yes',
                                ) ??
                                    false;
                            if (didRequestSignOut == true) {
                              try {
                                EasyLoading.show(status: 'Logging Out');
                                await ref
                                    .read(authRepositoryProvider)
                                    .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                                await ref
                                    .read(authRepositoryProvider)
                                    .signOut();
                                // MyApp.of(context).authService.authenticated = true;
                                // onLoginCallback?.call(true);
                                // AutoRouter.of(context).push(const DashboardRouter());
                                EasyLoading.dismiss();
                                context.replace('/login');
                              } catch (err) {
                                print(err);
                                // debugPrint("Error :$err");
                              }
                            }

                          },
                        ),
                      ),
                      Positioned(
                        left: 32,
                        top: 8,
                        child: Row(
                          children: [
                            const Column(
                              children: [
                                Text("हरिपुर खानेपानी", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                                Text("हरिपुर, सर्लाही ", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                              ],
                            ),
                            const SizedBox(width: 16,),
                            Image.asset("assets/icons/ic_logo.png", height: 60, width: 60,),
                          ],
                        ),
                      ),
                      Positioned(
                        top: appDimension.grassesY(
                            x: size.width, y: size.height),
                        left: 0,
                        right: 0,
                        child: Image.asset("assets/icons/grasses.webp",
                            height: size.height < 800.0
                                ? 10.0
                                : size.height < 900
                                    ? 20
                                    : size.height < 1000
                                        ? 40
                                        : 50,
                            width: double.infinity,
                            fit: BoxFit.fill),
                      ),
                      Positioned(
                          key: const ValueKey('M1'),
                          top: appDimension.m1StartingPointY(
                              x: size.width, y: size.height),
                          left: appDimension.m1StartingPointX(
                              x: size.width, y: size.height),
                          child: MotorPainter(
                            motorId: "10",
                            motorModel: motor10State.value,
                            motorSettingModel: setting.motor10!,
                          )),
                      Positioned(
                        key: const ValueKey('T1'),
                        left: appDimension.t1X(x: size.width, y: size.height),
                        top: appDimension.t1Y(x: size.width, y: size.height),
                        child: Column(
                          children: [
                            TankBlock(
                              tankId: "10",
                              tankSettingModel: setting.tank10!,
                              height: appDimension.heightTank(
                                  x: size.width, y: size.height),
                              width: appDimension.widthTank(
                                  x: size.width, y: size.height),
                            ),
                            SizedBox(
                              width: appDimension.widthStand(
                                  x: size.width, y: size.height),
                              height: appDimension.heightStand(
                                  x: size.width, y: size.height),
                              child: Image.asset(
                                "assets/icons/stand.png",
                                fit: BoxFit.fill,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                          key: const ValueKey('L5'),
                          top: appDimension.l5Y(x: size.width, y: size.height),
                          left: appDimension.l5X(x: size.width, y: size.height),
                          child: VerticalPipeline(
                            name: "L5",
                            topFlow: true,
                            isFlowing:true,
                            length: appDimension.l5Pipelength(
                                x: size.width, y: size.height),
                            isTop: false,
                          )),
                      Positioned(
                          key: const ValueKey('L6'),
                          top: appDimension.l6Y(x: size.width, y: size.height),
                          left: appDimension.l6X(x: size.width, y: size.height),
                          child: VerticalPipeline(
                            name: "L6",
                            isFlowing:true,
                            length: appDimension.l5Pipelength(
                                x: size.width, y: size.height),
                            isBottom: false,
                          )),
                      Positioned(
                          key: const ValueKey('L7'),
                          top: appDimension.l7Y(x: size.width, y: size.height),
                          left: appDimension.l7X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L7",
                              isFlowing:true,
                              length: appDimension.L8Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L9'),
                          top: appDimension.l9Y(x: size.width, y: size.height),
                          left: appDimension.l9X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L9",
                              length: appDimension.L9Pipelength(
                                  x: size.width, y: size.height),
                              isFlowing: valve10State.value.device_status==3 && valve10State.value.mobile_status==3,
                              isLeft: false,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L10'),
                          top: appDimension.l10Y(x: size.width, y: size.height),
                          left:
                              appDimension.l10X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L10",
                              length: appDimension.L9Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing: valve11State.value.device_status==3 && valve11State.value.mobile_status==3,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L8'),
                          top: appDimension.l8Y(x: size.width, y: size.height),
                          left: appDimension.l8X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L8",
                              length: appDimension.L8Pipelength(
                                  x: size.width, y: size.height),
                              isFlowing:true,
                              isLeft: false,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L4'),
                          top: appDimension.l4Y(x: size.width, y: size.height),
                          left: appDimension.l4X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L4",
                              length: appDimension.L4Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing:true,
                              isRight: false)),
                      Positioned(
                        key: const ValueKey('V1'),
                        left: appDimension.v1X(x: size.width, y: size.height),
                        top: appDimension.v1Y(x: size.width, y: size.height),
                        child: ValveHaripurWebWidget(
                            size: size,
                            appDimension: appDimension,
                            name: setting.valve10!.name,
                            valveId: "10",
                            role: prefs.getUserRole(),
                            motorModel: valve10State.value),
                      ),
                      Positioned(
                        key: ValueKey('V2'),
                        left: appDimension.v2X(x: size.width, y: size.height),
                        top: appDimension.v2Y(x: size.width, y: size.height),
                        child: ValveHaripurWebWidget(
                            size: size,
                            appDimension: appDimension,
                            name: setting.valve11!.name,
                            valveId: "11",
                            role: prefs.getUserRole(),
                            motorModel: valve11State.value),
                      ),
                      Positioned(
                          key: const ValueKey('L3'),
                          top: appDimension.l3Y(x: size.width, y: size.height),
                          left: appDimension.l3X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L3",
                              singleFlow: false,
                              isFlowing: motor10State.value.device_status == 3 &&
                                  motor10State.value.mobile_status == 3 || motor11State.value.device_status == 3 &&
                                  motor11State.value.mobile_status == 3,
                              length: appDimension.L3Pipelength(
                                  x: size.width, y: size.height),
                              isRight: false)),
                      Positioned(
                        key: const ValueKey('L2'),
                        top: appDimension.l2Y(x: size.width, y: size.height),
                        left: appDimension.l2X(x: size.width, y: size.height),
                        child: VerticalPipeline(
                            length: appDimension.L2Pipelength(
                                x: size.width, y: size.height),
                            name: "L2",
                            isFlowing: motor10State.value.device_status == 3 &&
                                motor10State.value.mobile_status == 3 || motor11State.value.device_status == 3 &&
                                motor11State.value.mobile_status == 3,
                            singleFlow: false,
                            isTop: false),
                      ),
                      Positioned(
                          key: const ValueKey('L1'),
                          top: appDimension.l1Y(x: size.width, y: size.height),
                          left: appDimension.l1X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                            length: appDimension.L1Pipelength(
                                x: size.width, y: size.height),
                            name: "L1",
                            isRight: false,
                            isLeft: false,
                            singleFlow: false,
                            isFlowing: motor10State.value.device_status == 3 &&
                                motor10State.value.mobile_status == 3,
                          )),
                      Positioned(
                        key: const ValueKey('D1'),
                        top: appDimension.d1Y(x: size.width, y: size.height),
                        left: appDimension.d1X(x: size.width, y: size.height),
                        child: Image.asset(
                          valve11State.value.device_status==3 ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
                          height: 60,
                          width: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                          key: const ValueKey('L1-A'),
                          top: appDimension.l21Y(x: size.width, y: size.height),
                          left: appDimension.m3StartingPointX(
                              x: size.width, y: size.height) -
                              appDimension.L21Pipelength(
                                  x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L1-A",
                              length: appDimension.L1Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing:  motor11State.value.device_status == 3 &&
                                  motor11State.value.mobile_status == 3,
                              leftFlow: true,
                              isRight: false)),
                      Positioned(
                        key: const ValueKey('D2'),
                        top: appDimension.d2Y(x: size.width, y: size.height),
                        left: appDimension.d2X(x: size.width, y: size.height),
                        child: Image.asset(
                          valve10State.value.device_status==3 ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
                          height: 60,
                          width: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                          key: const ValueKey('M2'),
                          top: appDimension.m3StartingPointY(
                              x: size.width, y: size.height) +
                              1,
                          left: appDimension.m3StartingPointX(
                              x: size.width, y: size.height) -
                              3,
                          child: MotorPainter(
                            isRight: false,
                            motorId: "11",
                            motorModel: motor11State.value,
                            motorSettingModel: setting.motor11!,
                          )),
                    ],
                  ),
                ),
                Expanded(
                  child: Stack(
                    children: [
                      Positioned(
                        right: 32,
                        top: 8,
                        child: Row(
                          children: [
                            const Column(
                              children: [
                                Text("Powered by", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                                Text("स्वदेशी इनोवेशन, काठमाडौँ ", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                              ],
                            ),
                            const SizedBox(width: 16,),
                            Image.asset("assets/icons/ic_logo_splash.png", height: 60, width: 60,),
                          ],
                        ),
                      ),
                      Positioned(
                        top: appDimension.m2StartingPointY(
                                x: size.width, y: size.height) +
                            appDimension.heightMotor(
                                x: size.width, y: size.height),
                        left: 0,
                        right: 0,
                        child: Image.asset("assets/icons/grasses.webp",
                            height: size.height < 800.0
                                ? 10.0
                                : size.height < 900
                                    ? 20
                                    : size.height < 1000
                                        ? 40
                                        : 50,
                            width: double.infinity,
                            fit: BoxFit.fill),
                      ),
                      Positioned(
                          key: const ValueKey('M3'),
                          top: appDimension.m2StartingPointY(
                              x: size.width, y: size.height),
                          left: appDimension.m2StartingPointX(
                              x: size.width, y: size.height),
                          child: MotorPainter(
                            motorId: "12",
                            motorModel: motor12State.value,
                            motorSettingModel: setting.motor12!,
                          )),
                      Positioned(
                          key: const ValueKey('M4'),
                          top: appDimension.m3StartingPointY(
                                  x: size.width, y: size.height) +
                              1,
                          left: appDimension.m3StartingPointX(
                                  x: size.width, y: size.height) -
                              3,
                          child: MotorPainter(
                            isRight: false,
                            motorId: "13",
                            motorModel: motor13State.value,
                            motorSettingModel: setting.motor13!,
                          )),
                      Positioned(
                          key: const ValueKey('L22'),
                          top: appDimension.m1StartingPointY(
                                  x: size.width, y: size.height) +
                              appDimension.heightMotor(
                                  x: size.width, y: size.height) -
                              appDimension.heightStand(
                                  x: size.width, y: size.height) -
                              appDimension.heightTank(
                                  x: size.width, y: size.height) +
                              appDimension.L2Tolerance(
                                  x: size.width, y: size.height),
                          left: appDimension.m2StartingPointX(
                                  x: size.width, y: size.height) +
                              appDimension.widthMotor(
                                  x: size.width, y: size.height) +
                              appDimension.L21Pipelength(
                                  x: size.width, y: size.height) -
                              6,
                          child: VerticalPipeline(
                            name: "L22",
                            isTop: false,
                            singleFlow: false,
                            isFlowing: motor12State.value.device_status == 3 &&
                                motor12State.value.mobile_status == 3 ||
                                motor13State.value.device_status == 3 &&
                                    motor13State.value.mobile_status == 3 ,
                            length: appDimension.L2Pipelength(
                                x: size.width, y: size.height),
                          )),
                      Positioned(
                          key: const ValueKey('L21'),
                          top: appDimension.l21Y(x: size.width, y: size.height),
                          left:
                              appDimension.l21X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L21",
                              length: appDimension.L21Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,

                              isFlowing:motor12State.value.device_status == 3 &&
                                  motor12State.value.mobile_status == 3,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L21-A'),
                          top: appDimension.l21Y(x: size.width, y: size.height),
                          left: appDimension.m3StartingPointX(
                                  x: size.width, y: size.height) -
                              appDimension.L21Pipelength(
                                  x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L21-A",
                              length: appDimension.L21Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing:  motor13State.value.device_status == 3 &&
                                  motor13State.value.mobile_status == 3,
                              leftFlow: true,
                              isRight: false)),
                      Positioned(
                        key: const ValueKey('T2'),
                        left: appDimension.m2StartingPointX(
                                x: size.width, y: size.height) +
                            appDimension.widthMotor(
                                x: size.width, y: size.height) +
                            appDimension.L21Pipelength(
                                x: size.width, y: size.height) +
                            appDimension.L31Pipelength(
                                x: size.width, y: size.height) -
                            25,
                        top: appDimension.m1StartingPointY(
                                x: size.width, y: size.height) +
                            appDimension.heightMotor(
                                x: size.width, y: size.height) -
                            appDimension.heightStand(
                                x: size.width, y: size.height) -
                            appDimension.heightTank(
                                x: size.width, y: size.height),
                        child: Column(
                          children: [
                            TankBlock(
                              tankId: "11",
                              tankSettingModel: setting.tank11!,
                              height: appDimension.heightTank(
                                  x: size.width, y: size.height),
                              width: appDimension.widthTank(
                                  x: size.width, y: size.height),
                            ),
                            SizedBox(
                              width: appDimension.widthStand(
                                  x: size.width, y: size.height),
                              height: appDimension.heightStand(
                                  x: size.width, y: size.height),
                              child: Image.asset(
                                "assets/icons/stand.png",
                                fit: BoxFit.fill,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                          key: const ValueKey('L31'),
                          top: appDimension.l31Y(x: size.width, y: size.height),
                          left: appDimension.m2StartingPointX(
                                  x: size.width, y: size.height) +
                              appDimension.widthMotor(
                                  x: size.width, y: size.height) +
                              appDimension.L21Pipelength(
                                  x: size.width, y: size.height) -
                              6,
                          child: HorizontalPipeline(
                            length: appDimension.L31Pipelength(
                                x: size.width, y: size.height),
                            name: "L31",
                            isFlowing:  motor12State.value.device_status == 3 &&
                                motor12State.value.mobile_status == 3  ||
                                motor13State.value.device_status == 3 &&
                                    motor13State.value.mobile_status == 3,
                            isRight: false,
                            singleFlow: false,
                          )),
                      Positioned(
                          key: const ValueKey('L51'),
                          top: appDimension.l51Y(x: size.width, y: size.height),
                          left:
                              appDimension.l51X(x: size.width, y: size.height),
                          child: VerticalPipeline(
                            name: "L51",
                            topFlow: true,
                            isFlowing: true,
                            length: appDimension.l5Pipelength(
                                x: size.width, y: size.height),
                          )),
                      Positioned(
                          key: const ValueKey('L91'),
                          top: appDimension.l91Y(x: size.width, y: size.height),
                          left:
                              appDimension.l91X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L91",
                              length: appDimension.L9Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing: valve12State.value.device_status==3 && valve12State.value.mobile_status==3,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L81'),
                          top: appDimension.l81Y(x: size.width, y: size.height),
                          left:
                              appDimension.l81X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L81",
                              length: appDimension.L8Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing:true,
                              isRight: false)),
                      Positioned(
                          key: const ValueKey('L41'),
                          top: appDimension.l41Y(x: size.width, y: size.height),
                          left:
                              appDimension.l41X(x: size.width, y: size.height),
                          child: HorizontalPipeline(
                              name: "L41",
                              length: appDimension.L4Pipelength(
                                  x: size.width, y: size.height),
                              isLeft: false,
                              isFlowing:true,
                              isRight: false)),
                      Positioned(
                        key: const ValueKey('V3'),
                        left: appDimension.v3X(x: size.width, y: size.height),
                        top: appDimension.v3Y(x: size.width, y: size.height),
                        child: ValveHaripurWebWidget(
                            size: size,
                            appDimension: appDimension,
                            name: setting.valve12!.name,
                            valveId: "12",
                            role: prefs.getUserRole(),
                            motorModel: valve12State.value),
                      ),
                      Positioned(
                          key: const ValueKey('D3'),
                          top: appDimension.d3Y(x: size.width, y: size.height),
                          left: appDimension.d3X(x: size.width, y: size.height),
                          child: Image.asset(
                            valve12State.value.device_status==3 ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
                            height: 60,
                            width: 100,
                            fit: BoxFit.cover,
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

/*class WebTalkhu2Page extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final size = MediaQuery
        .of(context)
        .size;
    log(size.height.toString());
    log(size.width.toString());

    return BaseScaffold(
        showLeftIcon: false,
        showAppBar: false,
        appbarText: 'तल्कु खानेपानी',
        child: Center(
          child: Container(
            height: 720,
            width: 1500,
            color: Colors.grey.shade300,
            child: Stack(
              children: [
                Positioned(
                  left: 450,
                  top: 170,
                  child: TankBlock(
                    height: 200,
                    width: 220,
                  ),
                ),
                Positioned.fill(
                  child: CustomPaint(
                    painter: SCADAPainter(),
                  ),
                ),
                Positioned(
                  left: 1260,
                  top: 435,
                  child: Image.asset(
                    "assets/icons/valve.png",
                    height: 60,
                    width: 60,
                  ),
                ),
                Positioned(top: 100, left: 60, child: MotorPainter()),
                Positioned(top: 350, left: 60, child: MotorPainter()),
                Positioned(
                  left: 450,
                  top: 390,
                  child: TankBlock(
                    height: 200,
                    width: 220,
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

class LinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Define the Paint for the line
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 4;

    // Draw a line based on widget's size
    canvas.drawLine(
      Offset(size.width * 0.2, 0), // Start at the middle-left
      Offset(20, size.height), // End at the middle-right
      paint,
    );
    canvas.drawLine(
      Offset(size.width * 0.2, 0), // Start at the middle-left
      Offset(size.width / 2 - 20, size.height), // End at the middle-right
      paint,
    );
    canvas.drawLine(
      Offset(size.width * 0.8, 0), // Start at the middle-left
      Offset(size.width - 20, size.height), // End at the middle-right
      paint,
    );
    canvas.drawLine(
      Offset(size.width * 0.8, 0), // Start at the middle-left
      Offset(size.width / 2 + 20, size.height), // End at the middle-right
      paint,
    );
    canvas.drawLine(
      Offset(size.width / 2, 0), // Start at the middle-left
      Offset(size.width / 2, size.height), // End at the middle-right
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}*/

/*

// Pipe and Tank Painter
class SCADAPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final pipePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 20
      ..style = PaintingStyle.stroke;

    final pipePaintStroke = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final pipePaint2 = Paint()
      ..color = Colors.blue
      ..strokeWidth = 20
      ..style = PaintingStyle.stroke;

    final pipePaint3 = Paint()
      ..color = Colors.blue
      ..strokeWidth = 20
      ..style = PaintingStyle.stroke;

    final pipePaint4 = Paint()
      ..color = Colors.blue
      ..strokeWidth = 20
      ..style = PaintingStyle.stroke;

    final pipePaint5 = Paint()
      ..color = Colors.blue
      ..strokeWidth = 20
      ..style = PaintingStyle.stroke;

    // Define a rectangle-like line shape
    double thickness = 20.0; // Line thickness
    double strokeWidth = 2.0; // Stroke thickness

    // Define the main filled line path
    Path linePath = Path()
      ..moveTo(5, 20) // Top-left of line
      ..lineTo(150, 20) // Top-right
      ..lineTo(150, 40) // Bottom-right
      ..lineTo(5, 40) // Bottom-left
      ..close(); // Close the path to make a filled shape

    // Paint for fill (blue)
    final Paint fillPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    // Paint for stroke on top and bottom
    final Paint strokePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    // Draw the filled blue line
    canvas.drawPath(linePath, fillPaint);
    // Draw the top stroke
    canvas.drawLine(
      Offset(5, 20), // Start of top stroke
      Offset(150, 20), // End of top stroke
      strokePaint,
    );

    canvas.drawLine(
      Offset(5, 40), // Start of top stroke
      Offset(150, 40), // End of top stroke
      strokePaint,
    );

    // Pipes
    canvas.drawLine(const Offset(220, 205), Offset(350, 205), pipePaint2);
    canvas.drawLine(const Offset(220, 455), Offset(350, 455), pipePaint3);
    canvas.drawLine(const Offset(350, 195), Offset(350, 465), pipePaint4);
    canvas.drawLine(const Offset(350, 240), Offset(450, 240), pipePaint);
    canvas.drawLine(const Offset(360, 250), Offset(450, 250), pipePaintStroke);
    canvas.drawLine(const Offset(360, 230), Offset(450, 230), pipePaintStroke);

    canvas.drawLine(const Offset(670, 250), Offset(750, 250), pipePaint5);
    canvas.drawLine(const Offset(750, 240), Offset(750, 460), pipePaint5);
    canvas.drawLine(const Offset(760, 460), Offset(670, 460), pipePaint5);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class PipeWithArrowPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint pipePaint = Paint()
      ..color = Colors.grey.shade600
      ..strokeWidth = 20
      ..style = PaintingStyle.stroke;

    // Start at the bottom left of the canvas
    final Offset startPoint = Offset(50, size.height / 2);

    // Create the Path for the pipe
    Path pipePath = Path();

    // Move to the start point
    pipePath.moveTo(startPoint.dx, startPoint.dy);

    // Draw the first horizontal segment
    final Offset endPoint1 = Offset(startPoint.dx + 100, startPoint.dy);
    pipePath.lineTo(endPoint1.dx, endPoint1.dy);

    // Draw the circular bend at the corner
    final Rect arcBounds =
    Rect.fromCircle(center: endPoint1, radius: 50); // Circle with 50 radius
    pipePath.arcTo(arcBounds, math.pi, math.pi / 2, false); // 90-degree arc

    // Draw the second vertical segment after the bend
    final Offset endPoint2 = Offset(endPoint1.dx, endPoint1.dy + 100);
    pipePath.lineTo(endPoint2.dx, endPoint2.dy);

    // Draw the path
    canvas.drawPath(pipePath, pipePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
*/
