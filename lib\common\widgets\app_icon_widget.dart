import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../constants/app_sizes.dart';
import 'widgets.dart';


class AppIconWidget extends ConsumerWidget {
  const AppIconWidget({Key? key, this.assetImgPath, this.imageSize}) : super(key: key);

  final String? assetImgPath;
  final Size? imageSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(Sizes.p16),
        child: Consumer(
          builder: (BuildContext context, WidgetRef ref, Widget? child) {
            return CustomCachedNetworkImageWidget(
              imageUrl: "https://firebasestorage.googleapis.com/v0/b/sewage-lifting.appspot.com/o/si%20(1).png?alt=media&token=6611a437-bece-44c7-9a6e-656ff965eeb3&_gl=1*1b6iohd*_ga*MjE4ODA4Mzc0LjE2OTIxNTU5NTk.*_ga_CW55HF8NVT*MTY5Njk1NTYwMy40MS4xLjE2OTY5NTYwNzguNTAuMC4w",
              height: imageSize?.height ?? Sizes.p120,
              width: imageSize?.width ?? Sizes.p120,
            );
          },
        ),
      ),
    );
  }
}
