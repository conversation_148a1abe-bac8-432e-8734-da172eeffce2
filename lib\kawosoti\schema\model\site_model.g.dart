// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'site_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SiteFireStoreModelImpl _$$SiteFireStoreModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SiteFireStoreModelImpl(
      t1: (json['t1'] as num?)?.toDouble(),
      t2: (json['t2'] as num?)?.toDouble(),
      t3: (json['t3'] as num?)?.toDouble(),
      t4: (json['t4'] as num?)?.toDouble(),
      t5: (json['t5'] as num?)?.toDouble(),
      cl: (json['cl'] as num?)?.toDouble(),
      ph: (json['ph'] as num?)?.toDouble(),
      oh: (json['oh'] as num?)?.toDouble(),
      bw: (json['bw'] as num?)?.toDouble(),
      id: json['id'] as String?,
      time1: (json['time1'] as num?)?.toInt(),
      time2: (json['time2'] as num?)?.toInt(),
      timestamp: const TimestampNullableConverter()
          .fromJson(json['timestamp'] as Timestamp?),
    );

Map<String, dynamic> _$$SiteFireStoreModelImplToJson(
        _$SiteFireStoreModelImpl instance) =>
    <String, dynamic>{
      't1': instance.t1,
      't2': instance.t2,
      't3': instance.t3,
      't4': instance.t4,
      't5': instance.t5,
      'cl': instance.cl,
      'ph': instance.ph,
      'oh': instance.oh,
      'bw': instance.bw,
      'id': instance.id,
      'time1': instance.time1,
      'time2': instance.time2,
      'timestamp':
          const TimestampNullableConverter().toJson(instance.timestamp),
    };

_$SiteRtDbModelImpl _$$SiteRtDbModelImplFromJson(Map<String, dynamic> json) =>
    _$SiteRtDbModelImpl(
      Tub1: (json['Tub1'] as num?)?.toDouble(),
      Tub2: (json['Tub2'] as num?)?.toDouble(),
      Tub3: (json['Tub3'] as num?)?.toDouble(),
      Tub4: (json['Tub4'] as num?)?.toDouble(),
      Chlorine: (json['Chlorine'] as num?)?.toDouble(),
      PH: (json['PH'] as num?)?.toDouble(),
      OHT: (json['OHT'] as num?)?.toDouble(),
      Borewell: (json['Borewell'] as num?)?.toDouble(),
      RMU1_timestamp: (json['RMU1_timestamp'] as num?)?.toInt(),
      RMU2_timestamp: (json['RMU2_timestamp'] as num?)?.toInt(),
      sensor: $enumDecodeNullable(_$SensorEnumMap, json['sensor']),
      timestamp: const TimestampNullableConverter()
          .fromJson(json['timestamp'] as Timestamp?),
    );

Map<String, dynamic> _$$SiteRtDbModelImplToJson(_$SiteRtDbModelImpl instance) =>
    <String, dynamic>{
      'Tub1': instance.Tub1,
      'Tub2': instance.Tub2,
      'Tub3': instance.Tub3,
      'Tub4': instance.Tub4,
      'Chlorine': instance.Chlorine,
      'PH': instance.PH,
      'OHT': instance.OHT,
      'Borewell': instance.Borewell,
      'RMU1_timestamp': instance.RMU1_timestamp,
      'RMU2_timestamp': instance.RMU2_timestamp,
      'sensor': _$SensorEnumMap[instance.sensor],
      'timestamp':
          const TimestampNullableConverter().toJson(instance.timestamp),
    };

const _$SensorEnumMap = {
  Sensor.turbidity: 'turbidity',
  Sensor.chlorine: 'chlorine',
  Sensor.ph: 'ph',
  Sensor.borewell: 'borewell',
  Sensor.oht: 'oht',
};

_$SiteNameModelImpl _$$SiteNameModelImplFromJson(Map<String, dynamic> json) =>
    _$SiteNameModelImpl(
      site1: json['site1'] as String,
      site2: json['site2'] as String,
      site3: json['site3'] as String,
      site4: json['site4'] as String,
      site5: json['site5'] as String,
      site6: json['site6'] as String,
      site7: json['site7'] as String,
      site8: json['site8'] as String,
    );

Map<String, dynamic> _$$SiteNameModelImplToJson(_$SiteNameModelImpl instance) =>
    <String, dynamic>{
      'site1': instance.site1,
      'site2': instance.site2,
      'site3': instance.site3,
      'site4': instance.site4,
      'site5': instance.site5,
      'site6': instance.site6,
      'site7': instance.site7,
      'site8': instance.site8,
    };
