// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tank_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TankModel _$TankModelFromJson(Map<String, dynamic> json) {
  return _TankModel.fromJson(json);
}

/// @nodoc
mixin _$TankModel {
  int? get float_status => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  int get actual_height => throw _privateConstructorUsedError;

  /// Serializes this TankModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TankModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TankModelCopyWith<TankModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TankModelCopyWith<$Res> {
  factory $TankModelCopyWith(TankModel value, $Res Function(TankModel) then) =
      _$TankModelCopyWithImpl<$Res, TankModel>;
  @useResult
  $Res call({int? float_status, int level, int actual_height});
}

/// @nodoc
class _$TankModelCopyWithImpl<$Res, $Val extends TankModel>
    implements $TankModelCopyWith<$Res> {
  _$TankModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TankModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float_status = freezed,
    Object? level = null,
    Object? actual_height = null,
  }) {
    return _then(_value.copyWith(
      float_status: freezed == float_status
          ? _value.float_status
          : float_status // ignore: cast_nullable_to_non_nullable
              as int?,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      actual_height: null == actual_height
          ? _value.actual_height
          : actual_height // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TankModelImplCopyWith<$Res>
    implements $TankModelCopyWith<$Res> {
  factory _$$TankModelImplCopyWith(
          _$TankModelImpl value, $Res Function(_$TankModelImpl) then) =
      __$$TankModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? float_status, int level, int actual_height});
}

/// @nodoc
class __$$TankModelImplCopyWithImpl<$Res>
    extends _$TankModelCopyWithImpl<$Res, _$TankModelImpl>
    implements _$$TankModelImplCopyWith<$Res> {
  __$$TankModelImplCopyWithImpl(
      _$TankModelImpl _value, $Res Function(_$TankModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TankModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? float_status = freezed,
    Object? level = null,
    Object? actual_height = null,
  }) {
    return _then(_$TankModelImpl(
      float_status: freezed == float_status
          ? _value.float_status
          : float_status // ignore: cast_nullable_to_non_nullable
              as int?,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      actual_height: null == actual_height
          ? _value.actual_height
          : actual_height // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TankModelImpl implements _TankModel {
  const _$TankModelImpl(
      {this.float_status = 0, this.level = 1, this.actual_height = 450});

  factory _$TankModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TankModelImplFromJson(json);

  @override
  @JsonKey()
  final int? float_status;
  @override
  @JsonKey()
  final int level;
  @override
  @JsonKey()
  final int actual_height;

  @override
  String toString() {
    return 'TankModel(float_status: $float_status, level: $level, actual_height: $actual_height)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TankModelImpl &&
            (identical(other.float_status, float_status) ||
                other.float_status == float_status) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.actual_height, actual_height) ||
                other.actual_height == actual_height));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, float_status, level, actual_height);

  /// Create a copy of TankModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TankModelImplCopyWith<_$TankModelImpl> get copyWith =>
      __$$TankModelImplCopyWithImpl<_$TankModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TankModelImplToJson(
      this,
    );
  }
}

abstract class _TankModel implements TankModel {
  const factory _TankModel(
      {final int? float_status,
      final int level,
      final int actual_height}) = _$TankModelImpl;

  factory _TankModel.fromJson(Map<String, dynamic> json) =
      _$TankModelImpl.fromJson;

  @override
  int? get float_status;
  @override
  int get level;
  @override
  int get actual_height;

  /// Create a copy of TankModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TankModelImplCopyWith<_$TankModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BwModel _$BwModelFromJson(Map<String, dynamic> json) {
  return _BwModel.fromJson(json);
}

/// @nodoc
mixin _$BwModel {
  double get level => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;

  /// Serializes this BwModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BwModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BwModelCopyWith<BwModel> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BwModelCopyWith<$Res> {
  factory $BwModelCopyWith(BwModel value, $Res Function(BwModel) then) =
      _$BwModelCopyWithImpl<$Res, BwModel>;
  @useResult
  $Res call({double level, int time});
}

/// @nodoc
class _$BwModelCopyWithImpl<$Res, $Val extends BwModel>
    implements $BwModelCopyWith<$Res> {
  _$BwModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BwModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? level = null,
    Object? time = null,
  }) {
    return _then(_value.copyWith(
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as double,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BwModelImplCopyWith<$Res> implements $BwModelCopyWith<$Res> {
  factory _$$BwModelImplCopyWith(
          _$BwModelImpl value, $Res Function(_$BwModelImpl) then) =
      __$$BwModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double level, int time});
}

/// @nodoc
class __$$BwModelImplCopyWithImpl<$Res>
    extends _$BwModelCopyWithImpl<$Res, _$BwModelImpl>
    implements _$$BwModelImplCopyWith<$Res> {
  __$$BwModelImplCopyWithImpl(
      _$BwModelImpl _value, $Res Function(_$BwModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BwModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? level = null,
    Object? time = null,
  }) {
    return _then(_$BwModelImpl(
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as double,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BwModelImpl implements _BwModel {
  const _$BwModelImpl({this.level = 1, this.time = 0});

  factory _$BwModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BwModelImplFromJson(json);

  @override
  @JsonKey()
  final double level;
  @override
  @JsonKey()
  final int time;

  @override
  String toString() {
    return 'BwModel(level: $level, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BwModelImpl &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, level, time);

  /// Create a copy of BwModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BwModelImplCopyWith<_$BwModelImpl> get copyWith =>
      __$$BwModelImplCopyWithImpl<_$BwModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BwModelImplToJson(
      this,
    );
  }
}

abstract class _BwModel implements BwModel {
  const factory _BwModel({final double level, final int time}) = _$BwModelImpl;

  factory _BwModel.fromJson(Map<String, dynamic> json) = _$BwModelImpl.fromJson;

  @override
  double get level;
  @override
  int get time;

  /// Create a copy of BwModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BwModelImplCopyWith<_$BwModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OutputInputVolumeModel _$OutputInputVolumeModelFromJson(
    Map<String, dynamic> json) {
  return _OutputInputVolumeModel.fromJson(json);
}

/// @nodoc
mixin _$OutputInputVolumeModel {
  int get mins => throw _privateConstructorUsedError;
  int get random => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;
  int get status => throw _privateConstructorUsedError;
  int get volume => throw _privateConstructorUsedError;
  double get rate => throw _privateConstructorUsedError;

  /// Serializes this OutputInputVolumeModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OutputInputVolumeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OutputInputVolumeModelCopyWith<OutputInputVolumeModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutputInputVolumeModelCopyWith<$Res> {
  factory $OutputInputVolumeModelCopyWith(OutputInputVolumeModel value,
          $Res Function(OutputInputVolumeModel) then) =
      _$OutputInputVolumeModelCopyWithImpl<$Res, OutputInputVolumeModel>;
  @useResult
  $Res call(
      {int mins, int random, int time, int status, int volume, double rate});
}

/// @nodoc
class _$OutputInputVolumeModelCopyWithImpl<$Res,
        $Val extends OutputInputVolumeModel>
    implements $OutputInputVolumeModelCopyWith<$Res> {
  _$OutputInputVolumeModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OutputInputVolumeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mins = null,
    Object? random = null,
    Object? time = null,
    Object? status = null,
    Object? volume = null,
    Object? rate = null,
  }) {
    return _then(_value.copyWith(
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as int,
      random: null == random
          ? _value.random
          : random // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      volume: null == volume
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as int,
      rate: null == rate
          ? _value.rate
          : rate // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutputInputVolumeModelImplCopyWith<$Res>
    implements $OutputInputVolumeModelCopyWith<$Res> {
  factory _$$OutputInputVolumeModelImplCopyWith(
          _$OutputInputVolumeModelImpl value,
          $Res Function(_$OutputInputVolumeModelImpl) then) =
      __$$OutputInputVolumeModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int mins, int random, int time, int status, int volume, double rate});
}

/// @nodoc
class __$$OutputInputVolumeModelImplCopyWithImpl<$Res>
    extends _$OutputInputVolumeModelCopyWithImpl<$Res,
        _$OutputInputVolumeModelImpl>
    implements _$$OutputInputVolumeModelImplCopyWith<$Res> {
  __$$OutputInputVolumeModelImplCopyWithImpl(
      _$OutputInputVolumeModelImpl _value,
      $Res Function(_$OutputInputVolumeModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutputInputVolumeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mins = null,
    Object? random = null,
    Object? time = null,
    Object? status = null,
    Object? volume = null,
    Object? rate = null,
  }) {
    return _then(_$OutputInputVolumeModelImpl(
      mins: null == mins
          ? _value.mins
          : mins // ignore: cast_nullable_to_non_nullable
              as int,
      random: null == random
          ? _value.random
          : random // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      volume: null == volume
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as int,
      rate: null == rate
          ? _value.rate
          : rate // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutputInputVolumeModelImpl implements _OutputInputVolumeModel {
  const _$OutputInputVolumeModelImpl(
      {this.mins = 0,
      this.random = 0,
      this.time = 0,
      this.status = 0,
      this.volume = 0,
      this.rate = 0.0});

  factory _$OutputInputVolumeModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutputInputVolumeModelImplFromJson(json);

  @override
  @JsonKey()
  final int mins;
  @override
  @JsonKey()
  final int random;
  @override
  @JsonKey()
  final int time;
  @override
  @JsonKey()
  final int status;
  @override
  @JsonKey()
  final int volume;
  @override
  @JsonKey()
  final double rate;

  @override
  String toString() {
    return 'OutputInputVolumeModel(mins: $mins, random: $random, time: $time, status: $status, volume: $volume, rate: $rate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutputInputVolumeModelImpl &&
            (identical(other.mins, mins) || other.mins == mins) &&
            (identical(other.random, random) || other.random == random) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.volume, volume) || other.volume == volume) &&
            (identical(other.rate, rate) || other.rate == rate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, mins, random, time, status, volume, rate);

  /// Create a copy of OutputInputVolumeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OutputInputVolumeModelImplCopyWith<_$OutputInputVolumeModelImpl>
      get copyWith => __$$OutputInputVolumeModelImplCopyWithImpl<
          _$OutputInputVolumeModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutputInputVolumeModelImplToJson(
      this,
    );
  }
}

abstract class _OutputInputVolumeModel implements OutputInputVolumeModel {
  const factory _OutputInputVolumeModel(
      {final int mins,
      final int random,
      final int time,
      final int status,
      final int volume,
      final double rate}) = _$OutputInputVolumeModelImpl;

  factory _OutputInputVolumeModel.fromJson(Map<String, dynamic> json) =
      _$OutputInputVolumeModelImpl.fromJson;

  @override
  int get mins;
  @override
  int get random;
  @override
  int get time;
  @override
  int get status;
  @override
  int get volume;
  @override
  double get rate;

  /// Create a copy of OutputInputVolumeModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OutputInputVolumeModelImplCopyWith<_$OutputInputVolumeModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
