

import 'dart:math';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:si/division1/repository/borewell_repository.dart';
import 'package:si/division1/repository/division_repository.dart';
import 'package:si/division1/repository/new_division_repository.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/services/firestore_path.dart';

import '../../../datatable/paginated_data_table_2.dart';
import '../../../utils/format.dart';
import '../model/level_history_model.dart';

// Async datasource for AsynPaginatedDataTabke2 example. Based on AsyncDataTableSource which
/// is an extension to FLutter's DataTableSource and aimed at solving
/// saync data fetching scenarious by paginated table (such as using Web API)
class BorewellHistorySiteDataSource extends AsyncDataTableSource {

  BorewellHistorySiteDataSource({this.userType=1}) {
    print('SiteDataSource created');
  }



  BorewellHistorySiteDataSource.empty({this.userType=1}) {
    _empty = true;
    print('SiteDataSource.empty created');
  }

  BorewellHistorySiteDataSource.error({this.userType=1}) {
    print('SiteDataSource.error created');
  }

  bool _empty = false;
  final int userType;


  int deviceId = 10;

  int type = 1;

  int? _siteDate;

  int? startSiteDate;
  int? endSiteDate;


  String  siteId = FirestorePath.schemaOne;

  set startDate(int? value) {
    startSiteDate = value;
    notifyListeners();
  }

  set SiteId(String value){
    siteId = value;
    notifyListeners();
  }

  set Id(int id){
    deviceId = id;
    notifyListeners();
  }

  int startingIndex = 0;

  List<BorewellHistoryModel> listBorewellModel = [];

  final IBorewellRepository _repo = BorewellRepository();


  Future<int> getTotalRecords() {
    return Future<int>.delayed(
        const Duration(milliseconds: 0), () => _empty ? 0 : 15);
  }

  @override
  Future<AsyncRowsResponse> getRows(int start, int end) async {
    var index = 0;
    final today = DateTime.now();
    var x = start==0  ? await _repo.getBwHistory(siteId: siteId, id: deviceId,date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0).millisecondsSinceEpoch) :
    start<startingIndex ?  listBorewellModel.skip(start).take(end).toList()   :await _repo.getBwHistory(siteId:siteId,id: deviceId,
        startAfterDate: _siteDate, date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0).millisecondsSinceEpoch);
    if(start==0){
      listBorewellModel.clear();
    }

    final count = await _repo.getLength(siteId:siteId,id:deviceId,dateTime:(startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0).millisecondsSinceEpoch));
    if(count==0){
      _empty = true;
      return AsyncRowsResponse(listBorewellModel.length, []);
    }else{
      var r = AsyncRowsResponse(
          count,
          x.map((siteModel) {
            index++;
            return DataRow(
              key: ValueKey<String>(siteModel!.time!.toString()),
              selected: false,
              onSelectChanged: (value) {

              },
              cells: getSiteCells( start, index, siteModel),
            );
          }).toList());

      startingIndex =  start;
      if(start>=startingIndex){
        for(final model in x){
          listBorewellModel.add(model!);
        }
      }
      developer.log(listBorewellModel.toString());
      _siteDate = x.last!.time;

      return r;
    }

  }
}


List<DataCell> getSiteCells(int start, int index, BorewellHistoryModel bwModel) {
  return [
    DataCell(Text(Format.onlyTimeFromTimeStamp(bwModel.time!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(bwModel.level.toStringAsFixed(2),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(bwModel.rssi.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(bwModel.bwId.toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}