

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_common_motor_unit.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/motor_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../../constants/app_sizes.dart';
import '../../provider/dashboard_provider.dart';

class ValveListPage extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final setting = ref.read(localStorageProvider).getSettings();
    final List<Widget> widgetList = List.generate(setting.valve_id!.length, (index) {
      return NewCommonValveUnit(
        valveId: setting.valve_id![index].toString(),
        valveSettingModel: getValveModel(setting.valve_id![index].toString(), setting),
      );

    });
    return Column(
      children: _buildRows(widgetList),
    );

  }

  List<Widget> _buildRows(List<Widget> widgetList) {
    List<Widget> rows = [];
    for (int i = 0; i < widgetList.length; i += 2) {
      int end = i + 2 > widgetList.length ? widgetList.length : i + 2;
      rows.add(
        Column(
          children: [
            gapH8,
            Row(
              mainAxisAlignment: end - i == 1
                  ? MainAxisAlignment.center
                  : MainAxisAlignment.spaceEvenly,
              children: widgetList.sublist(i, end),
            ),
            gapH8
          ],
        ),
      );
    }
    return rows;
  }
}




MotorSettingModel getMotorModel(String motorId, DivisionSettingModel setting){
  if(motorId=="10"){
    return setting.motor10!;
  }else if(motorId=="11"){
    return setting.motor11!;
  }else if(motorId=="12"){
    return setting.motor12!;
  }else if(motorId=="13"){
    return setting.motor13!;
  }else if(motorId=="14"){
    return setting.motor14!;
  }else if(motorId=="20"){
    return setting.motor20!;
  }else if(motorId=="21"){
    return setting.motor21!;
  }else if(motorId=="30"){
    return setting.motor30!;
  }else if(motorId=="31"){
    return setting.motor31!;
  }else if(motorId=="40"){
    return setting.motor40!;
  }else if(motorId=="41"){
    return setting.motor41!;
  }else if(motorId=="50"){
    return setting.motor50!;
  }else if(motorId=="51"){
    return setting.motor51!;
  }else{
    return setting.motor10!;
  }
}


ValveSettingModel getValveModel(String valveId, DivisionSettingModel setting){
  if(valveId=="10"){
    return setting.valve10!;
  }else if(valveId=="11"){
    return setting.valve11!;
  }else if(valveId=="12"){
    return setting.valve12!;
  }else if(valveId=="20"){
    return setting.valve20!;
  }else if(valveId=="21"){
    return setting.valve21!;
  }else{
    return setting.valve10!;
  }
}