


import 'dart:async';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/division1/homepage/basgadhiSecond/basgadhi_second_page.dart';
import 'package:si/division1/homepage/bhairab/bhairab_page.dart';
import 'package:si/division1/homepage/bharmakot/bharmakot_page.dart';
import 'package:si/division1/homepage/bhatethumki/bhatethumki_page.dart';
import 'package:si/division1/homepage/butwal/butwal_khanepani_page.dart';
import 'package:si/division1/homepage/chanakhel/chanakhel_page.dart';
import 'package:si/division1/homepage/danapur/danapur_page.dart';
import 'package:si/division1/homepage/deukot/deukot_page.dart';
import 'package:si/division1/homepage/faidhoka_page.dart';
import 'package:si/division1/homepage/filter_motor_widget.dart';
import 'package:si/division1/homepage/gogan/gogan_khanepani_page.dart';
import 'package:si/division1/homepage/haripur/haripur_page.dart';
import 'package:si/division1/homepage/hatiya/hatiya_lifting_page.dart';
import 'package:si/division1/homepage/hetauda/hetauda_page.dart';
import 'package:si/division1/homepage/jyotinagar/jyotinagar_page.dart';
import 'package:si/division1/homepage/katunje/katunje_page.dart';
import 'package:si/division1/homepage/khadkagau_page.dart';
import 'package:si/division1/homepage/khoriya/khoriya_page.dart';
import 'package:si/division1/homepage/kolati/kolati_page.dart';
import 'package:si/division1/homepage/panbari/panbari_page.dart';
import 'package:si/division1/homepage/pandubazaar/pandubazar_page.dart';
import 'package:si/division1/homepage/ramechhap/site11_page.dart';
import 'package:si/division1/homepage/ranibari/ranibarI_page.dart';
import 'package:si/division1/homepage/sadakchhetra/sadakchetra_page.dart';
import 'package:si/division1/homepage/sandbox_page.dart';
import 'package:si/division1/homepage/saraswotikhel/saraswoti_page.dart';
import 'package:si/division1/homepage/sindhupalchowk/mobile_sindhulpalchowk_page.dart';
import 'package:si/division1/homepage/sindhupalchowk/sindhupalchowk_page.dart';
import 'package:si/division1/homepage/solarpump/solarpump_page.dart';
import 'package:si/division1/homepage/talkhu/talkhu_page.dart';
import 'package:si/division1/homepage/topgachi/topgachi_page.dart';

import 'package:si/services/shared_preferences_service.dart';

import '../../constants/app_sizes.dart';
import '../model/motor_model.dart';
import '../provider/division_provider.dart';
import 'ramechhap/site12_page.dart';
import 'ramechhap/site13_page.dart';
import 'rumba/piparwa_page.dart';
import 'simkhola/simkhola_page.dart';
import 'display.dart';
import 'filter_plant.dart';
import 'new_tank_unit.dart';

class DivisionHomePage extends HookConsumerWidget {
  const DivisionHomePage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);

    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.red, // Set your desired status bar color
      statusBarIconBrightness: Brightness.light, // Light or dark status bar icons
    ));
    if(prefs.getSiteId()=="site1"){
      return HaripurPage();
    } else if(prefs.getSiteId()=="site3"){
      return ChanaKhelPage();
    }else if(prefs.getSiteId()=="site4"){
      return MobileSindhulpalchowkPage();
    }
    else if(prefs.getSiteId()=="site5"){
      return SimkholaPage();
    } else if(prefs.getSiteId()=="site200"){
      return TalkhuPage();
    } else if(prefs.getSiteId()=="site201"){
      return KatunjePage();
    } else if(prefs.getSiteId() == "site205"){
      return SandBoxPage();
    } else if(prefs.getSiteId() == "site400"){
      return BasgadhiSecondKhanepaniPage();
    } else if(prefs.getSiteId() == "site401"){
      return JyotiNagarPage();
    } else if(prefs.getSiteId() == "site402"){
      return PanbariPage();
    } else if(prefs.getSiteId() == "site403"){
      return DanapurPage();
    } else if(prefs.getSiteId() == "site404"){
      return ButwalKhanepaniPage();
    } else if(prefs.getSiteId() == "site15"){
      return SaraswotiPage();
    } else if(prefs.getSiteId() == "site13"){
      return KhoriyaPage();
    }
    else if(prefs.getSiteId() == "site7"){
      return PandubazarPage();
    }else if(prefs.getSiteId() == "site9"){
      return HatiyaLiftingPage();
    }else if(prefs.getSiteId() == "site10"){
      return BhairabPage();
    } else if(prefs.getSiteId() == "site11"){
      return RamechapSite11Page();
    }else if(prefs.getSiteId() == "site12"){
      return RamechapSite12Page();
    }else if(prefs.getSiteId() == "site13"){
      return RamechapSite13Page();
    }else if(prefs.getSiteId() == "site14") {
      return RanibariPage();
    } else if(prefs.getSiteId() == "site101"){
      return TopGachiPage();
    }else if(prefs.getSiteId() == "site102") {
      return HetaudaPage();
    }
    else if(prefs.getSiteId() == "site103") {
      return KolatiPage();
    }
    else if(prefs.getSiteId() == "site104"){
      return BharmakotPage();
    } else if(prefs.getSiteId() == "site105"){
      return PiparwaPage();
    }else if(prefs.getSiteId() == "site106"){
      return SadakchetraPage();
    }else if(prefs.getSiteId() == "site107"){
      return GoganKhanepaniPage();
    }else if(prefs.getSiteId() == "site108"){
      return DeukotPage();
    } else if(prefs.getSiteId() == "site109"){
      return BhatethumkiPage();
    } else if(prefs.getSiteId() == "site1000"){
      return SolarpumpPage();
    }
    else{
      return SandBoxPage();
    }

  }
}
