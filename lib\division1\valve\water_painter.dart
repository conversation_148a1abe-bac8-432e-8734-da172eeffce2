import 'package:flutter/material.dart';

class WaterFlowPainter extends CustomPainter {
  final double animationValue;

  WaterFlowPainter(this.animationValue, this.heightDivision, this.width);

  int heightDivision;
  double width;

  @override
  void paint(Canvas canvas, Size size) {

    // Draw the pipe
    Paint pipePaint = Paint()..color = Colors.blue..strokeWidth = 50;
    Offset start = Offset(0, 50);
    Offset end = Offset(75, 50);
    canvas.drawLine(start, end, pipePaint);

    Paint pipePaint2 = Paint()..color = Colors.grey..strokeWidth = 50;
    Offset start2 = Offset(75, 50);
    Offset end2 = Offset(width, 50);
    canvas.drawLine(start2, end2, pipePaint2);

    // Calculate water level based on animation value
    double waterWidth = 50;
    double waterLevel = width * animationValue;

    // Draw water
    Paint waterPaint = Paint()..color = Colors.blue;
    Rect waterRect =
    Rect.fromLTWH(start.dx, start.dy - waterWidth / 2, waterLevel, waterWidth);
    canvas.drawRect(waterRect, waterPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}