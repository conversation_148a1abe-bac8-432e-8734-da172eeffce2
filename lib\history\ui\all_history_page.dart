import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/history/provider/history_provider.dart';

import '../../common/widgets/widgets.dart';
import '../../utils/format.dart';
import '../model/history_model.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';

class AllHistoryPage extends HookConsumerWidget {
  AllHistoryPage({required this.site});

  final String site;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseScaffold(
      appbarText: site == "site1" ? "Site One History" : "Site Two History",
      child: FirestoreListView<AllHistorymodel>(
        pageSize: 20,
        query: site == "site1" ? siteOnehistoryQuery : siteTwohistoryQuery,
        errorBuilder: (context,er,st){
          return Center(child: Text(er.toString(),style: TextStyle(color: Colors.black),));
        },
        loadingBuilder: (context){
          return Center(child: LoadingIndicator(),);
        },
        itemBuilder: (context, snapshot) {
          final history = snapshot.data();
          return Column(
            children: [
              ListTile(
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Row(
                        children: [
                          Expanded(
                              child: Text("Motor Status: ${history.motor}")),
                          Expanded(
                              child: Text("Current: ${history.current} Amp")),
                        ],
                      ),
                    ),
                    Padding(
                      padding: vertPadding4,
                      child: Row(
                        children: [
                          Expanded(child: Text("Float Status: ${history.float}")),
                          Expanded(child: Text("Timer Status: ${history.timer}")),
                        ],
                      ),
                    ),
                    Padding(
                      padding: vertPadding4,
                      child: Text("Level: ${history.level}"),
                    ),
                    Align(
                      alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: vertPadding4,
                          child: Text(Format.date(history.time!)),
                        )),
                    gapH8,
                  ],
                ),
              ),
              Container(
                height: 1,
                color: Colors.green,
              )
            ],
          );
        },
      ),
    );
  }
}
