import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/homepage/talkhu/talkhu_valve_widget.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/homepage/widgets/source_widget.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../../../common/widgets/widgets.dart';
import '../../../../constants/app_sizes.dart';
import '../../../../provider/auth_provider.dart';
import '../../../model/division_setting_model.dart';
import '../../../model/motor_model.dart';
import '../../widgets/motor_widget.dart';
import '../../widgets/tank_widget.dart';

class WebTalkhuPage extends HookConsumerWidget {
  const WebTalkhuPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var size = MediaQuery.of(context).size;
    final appDimension = ref.read(talkhuProvider);
    final prefs = ref.read(sharedPreferencesServiceProvider);

    return BaseScaffold(
        showAppBar: false,
        showLeftIcon: false,
        child: Container(
          height: size.height,
          width: size.width,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF87CEFA), // Light Blue (Sky color)
                Color(0xFFE0FFFF),
                Color(0xFF87CEFA), // Light Blue (Sky color)
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),


          ),
          child: Stack(
            children: [
              const Align(
                alignment: Alignment.bottomRight,
                  child: Padding(
                    padding: const EdgeInsets.only(right:8.0),
                    child: Text("Contact: **********", style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.black)),
                  )),
              const Align(
                  alignment: Alignment.topCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(right:150.0, top: 8),
                    child: Text(
                      "Swodeshi SCADA System",
                      style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.black),
                    ),
                  )),
              const Align(
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    "Powered by: Swodeshi Innovation Pvt. Ltd.",
                    style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black),
                  )),
              Positioned(
                right: 8,
                top: 8,
                child: Row(
                  children: [
                    const Column(
                      children: [
                        Text(
                          "टल्कु सिम खानेपानी तथा सरसफाई उपभोक्ता समिति",
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          "दक्षिणकाली-९, काठमाडौं",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                    Image.asset(
                      "assets/icons/ic_logo_splash.png",
                      height: 60,
                      width: 60,
                    ),
                    gapW8,
                    IconButton(
                      icon: const Icon(Icons.logout_outlined),
                      onPressed: () async {
                        final bool didRequestSignOut =
                            await showAlertDialog(
                              context: context,
                              title: 'Logout',
                              content:
                              'Are you sure you want to logout?',
                              cancelActionText: 'Cancel',
                              defaultActionText: 'Yes',
                            ) ??
                                false;
                        if (didRequestSignOut == true) {
                          try {
                            EasyLoading.show(status: 'Logging Out');
                            await ref
                                .read(authRepositoryProvider)
                                .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                            await ref
                                .read(authRepositoryProvider)
                                .signOut();
                            // MyApp.of(context).authService.authenticated = true;
                            // onLoginCallback?.call(true);
                            // AutoRouter.of(context).push(const DashboardRouter());
                            EasyLoading.dismiss();
                            context.replace('/login');
                          } catch (err) {
                            print(err);
                            // debugPrint("Error :$err");
                          }
                        }
                      },
                    ),
                  ],
                ),
              ),
              Positioned(
                left: appDimension.wellX(x: size.width, y: size.height),
                top: appDimension.wellY(x: size.width, y: size.height),
                child: OpenWellWidget(
                  height:
                      appDimension.wellHeight(x: size.width, y: size.height),
                  width: appDimension.wellWidth(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('L1'),
                top: appDimension.l1Y(x: size.width, y: size.height),
                left: appDimension.l1X(x: size.width, y: size.height),
                child: VerticalPipeline(
                  name: 'L1',
                  isBottom: false,
                  isTop: false,
                  singleFlow: false,
                  isFlowing: true,
                  length: appDimension.l1Length(x: size.width, y: size.height),
                  isLeft: true,
                ),
              ),
              Positioned(
                key: const ValueKey('L2'),
                top: appDimension.l2Y(x: size.width, y: size.height),
                left: appDimension.l2X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L2',
                  length: appDimension.l2Length(x: size.width, y: size.height),
                  isLeft: true,
                ),
              ),
              Positioned(
                key: const ValueKey('V10'),
                top: appDimension.v1Y(x: size.width, y: size.height),
                left: appDimension.v1X(x: size.width, y: size.height),
                child: TalkhuValveWidget(
                  size: size,
                  appDimension: appDimension,
                  name: setting.valve10!.name,
                  valveId: "10",
                  role: prefs.getUserRole(),
                ),
              ),
              Positioned(
                key: const ValueKey('L3'),
                top: appDimension.l3Y(x: size.width, y: size.height),
                left: appDimension.l3X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L3',
                  isRight: false,
                  length: appDimension.l3Length(x: size.width, y: size.height),
                  isLeft: true,
                ),
              ),
              Positioned(
                key: const ValueKey('L5'),
                top: appDimension.l5Y(x: size.width, y: size.height),
                left: appDimension.l5X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L5',
                  isRight: false,
                  length: appDimension.l5Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('V2'),
                top: appDimension.v2Y(x: size.width, y: size.height),
                left: appDimension.v2X(x: size.width, y: size.height),
                child: TalkhuValveWidget(
                  size: size,
                  appDimension: appDimension,
                  name: setting.valve11!.name,
                  valveId: "11",
                  role: prefs.getUserRole(),
                ),
              ),


              Positioned(
                key: const ValueKey('L6'),
                top: appDimension.l6Y(x: size.width, y: size.height),
                left: appDimension.l6X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L6',
                  isRight: false,
                  length: appDimension.l6Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('SW2'),
                top: appDimension.sumpwellY2(x: size.width, y: size.height),
                left: appDimension.sumpwellX2(x: size.width, y: size.height),
                child: SumpWellWidget(
                  height: appDimension.sumpwellHeight2(
                      x: size.width, y: size.height),
                  width: appDimension.sumpwellWidth2(
                      x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('M11'),
                top: appDimension.m11Y(x: size.width, y: size.height),
                left: appDimension.m11X(x: size.width, y: size.height),
                child: HookConsumer(builder: (context, ref, child) {
                  final motorState = useState<MotorModel>(const MotorModel());

                  ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
                      (previous, next) {
                    if (next.asData?.value.snapshot.value != null) {
                      final datasnapshot =
                          next.asData?.value.snapshot.value as Map;
                      motorState.value = MotorModel(
                          motorAmps: datasnapshot['current'],
                          output_status: datasnapshot['output_status'],
                          voltage: datasnapshot['voltage'],
                          time: datasnapshot['time'],
                          VoltageFaultStatus: datasnapshot['voltage_fault'],
                          device_status: datasnapshot['device_status'],
                          mobile_status: datasnapshot['mobile_status']);
                    }
                    if (motorState.value.device_status ==
                        motorState.value.mobile_status) {
                      EasyLoading.dismiss();
                    }
                  });
                  return Row(
                    children: [
                      MotorMove(
                        motorModel: motorState.value,
                        motorSettingModel: setting.motor11!,
                        faultStatus: motorState.value.VoltageFaultStatus == 3,
                      ),
                      gapW8,
                      ControlPanel(
                          motorModel: motorState.value,
                          motorId: "11",
                          motorSettingModel: setting.motor11!)
                    ],
                  );
                }),
              ),
              Positioned(
                key: const ValueKey('SW1'),
                top: appDimension.sumpwellY1(x: size.width, y: size.height),
                left: appDimension.sumpwellX1(x: size.width, y: size.height),
                child: Stack(
                  children: [
                    SumpWellWidget(
                      height: appDimension.sumpwellHeight1(
                          x: size.width, y: size.height),
                      width: appDimension.sumpwellWidth1(
                          x: size.width, y: size.height),
                    ),
                  ],
                ),
              ),
              Positioned(
                key: const ValueKey('M10'),
                top: appDimension.m10Y(x: size.width, y: size.height),
                left: appDimension.m10X(x: size.width, y: size.height),
                child: HookConsumer(builder: (context, ref, child) {
                  final motorState = useState<MotorModel>(const MotorModel());

                  ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
                      (previous, next) {
                    if (next.asData?.value.snapshot.value != null) {
                      final datasnapshot =
                          next.asData?.value.snapshot.value as Map;
                      motorState.value = MotorModel(
                          motorAmps: datasnapshot['current'],
                          output_status: datasnapshot['output_status'],
                          voltage: datasnapshot['voltage'],
                          time: datasnapshot['time'],
                          VoltageFaultStatus: datasnapshot['voltage_fault'],
                          device_status: datasnapshot['device_status'],
                          mobile_status: datasnapshot['mobile_status']);
                    }
                    if (motorState.value.device_status ==
                        motorState.value.mobile_status) {
                      EasyLoading.dismiss();
                    }
                  });
                  return Row(
                    children: [
                      MotorMove(
                        motorModel: motorState.value,
                        motorSettingModel: setting.motor10!,
                        faultStatus: motorState.value.VoltageFaultStatus == 3,
                      ),
                      gapW8,
                      ControlPanel(
                          motorModel: motorState.value,
                          motorId: "10",
                          motorSettingModel: setting.motor10!)
                    ],
                  );
                }),
              ),
              Positioned(
                key: const ValueKey('L7'),
                top: appDimension.l7Y(x: size.width, y: size.height),
                left: appDimension.l7X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L7',
                  isRight: false,
                  length: appDimension.l7Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L12'),
                top: appDimension.l12Y(x: size.width, y: size.height),
                left: appDimension.l12X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L12',
                  isRight: false,
                  length: appDimension.l12Length(x: size.width, y: size.height),
                  isLeft: false,
                  singleFlow: false,
                ),
              ),
              Positioned(
                key: const ValueKey('V3'),
                top: appDimension.v3Y(x: size.width, y: size.height),
                left: appDimension.v3X(x: size.width, y: size.height),
                child: TalkhuValveWidget(
                  size: size,
                  appDimension: appDimension,
                  name: setting.valve12!.name,
                  valveId: "12",
                  role: prefs.getUserRole(),
                ),
              ),
              Positioned(
                key: const ValueKey('L8'),
                top: appDimension.l8Y(x: size.width, y: size.height),
                left: appDimension.l8X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L8',
                  isRight: false,
                  length: appDimension.l8Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('T10'),
                top: appDimension.t10Y(x: size.width, y: size.height),
                left: appDimension.t10X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "10",
                  roleType: prefs.getUserRole(),
                  tankSettingModel: setting.tank10!,
                  height: appDimension.t10Height(x: size.width, y: size.height),
                  width: appDimension.t10Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('L9'),
                top: appDimension.l9Y(x: size.width, y: size.height),
                left: appDimension.l9X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L9',
                  isRight: false,
                  length: appDimension.l9Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('V4'),
                top: appDimension.v4Y(x: size.width, y: size.height),
                left: appDimension.v4X(x: size.width, y: size.height),
                child: TalkhuValveWidget(
                  size: size,
                  appDimension: appDimension,
                  name: setting.valve13!.name,
                  valveId: "13",
                  role: prefs.getUserRole(),
                ),
              ),
              Positioned(
                key: const ValueKey('L10'),
                top: appDimension.l10Y(x: size.width, y: size.height),
                left: appDimension.l10X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L10',
                  isRight: false,
                  length: appDimension.l10Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L11'),
                top: appDimension.l11Y(x: size.width, y: size.height),
                left: appDimension.l11X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L11',
                  isRight: false,
                  length: appDimension.l11Length(x: size.width, y: size.height),
                  isLeft: true,
                  singleFlow: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L13'),
                top: appDimension.l13Y(x: size.width, y: size.height),
                left: appDimension.l13X(x: size.width, y: size.height),
                child: VerticalPipeline(
                  name: 'L13',
                  isRight: false,
                  length: appDimension.l13Length(x: size.width, y: size.height),
                  isLeft: false,
                  singleFlow: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L14'),
                top: appDimension.l14Y(x: size.width, y: size.height),
                left: appDimension.l14X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L14',
                  isRight: false,
                  length: appDimension.l14Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('T21'),
                top: appDimension.t21Y(x: size.width, y: size.height),
                left: appDimension.t21X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "21",
                  roleType: prefs.getUserRole(),
                  tankSettingModel: setting.tank21!,
                  height: appDimension.t21Height(x: size.width, y: size.height),
                  width: appDimension.t21Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('L15'),
                top: appDimension.l15Y(x: size.width, y: size.height),
                left: appDimension.l15X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L15',
                  isRight: false,
                  length: appDimension.l15Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('T20'),
                top: appDimension.t20Y(x: size.width, y: size.height),
                left: appDimension.t20X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "20",
                  tankSettingModel: setting.tank20!,
                  roleType: prefs.getUserRole(),
                  height: appDimension.t20Height(x: size.width, y: size.height),
                  width: appDimension.t20Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('V6'),
                top: appDimension.v6Y(x: size.width, y: size.height),
                left: appDimension.v6X(x: size.width, y: size.height),
                child: TalkhuValveWidget(
                  size: size,
                  appDimension: appDimension,
                  name: setting.valve20!.name,
                  valveId: "20",
                  role: prefs.getUserRole(),
                ),
              ),
              Positioned(
                key: const ValueKey('L24'),
                top: appDimension.l24Y(x: size.width, y: size.height),
                left: appDimension.l24X(x: size.width, y: size.height),
                child: VerticalPipeline(
                  name: 'L24',
                  isRight: false,
                  topFlow: true,
                  length: appDimension.l24Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L25'),
                top: appDimension.l25Y(x: size.width, y: size.height),
                left: appDimension.l25X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L25',
                  isRight: false,
                  length: appDimension.l25Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('M20'),
                top: appDimension.m20Y(x: size.width, y: size.height),
                left: appDimension.m20X(x: size.width, y: size.height),
                child: HookConsumer(builder: (context, ref, child) {
                  final motorState = useState<MotorModel>(const MotorModel());

                  ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
                      (previous, next) {
                    if (next.asData?.value.snapshot.value != null) {
                      final datasnapshot =
                          next.asData?.value.snapshot.value as Map;
                      motorState.value = MotorModel(
                          motorAmps: datasnapshot['current'],
                          output_status: datasnapshot['output_status'],
                          voltage: datasnapshot['voltage'],
                          time: datasnapshot['time'],
                          VoltageFaultStatus: datasnapshot['voltage_fault'],
                          device_status: datasnapshot['device_status'],
                          mobile_status: datasnapshot['mobile_status']);
                    }
                    if (motorState.value.device_status ==
                        motorState.value.mobile_status) {
                      EasyLoading.dismiss();
                    }
                  });
                  return Column(
                    children: [
                      ControlPanel(
                          motorModel: MotorModel(),
                          motorId: "20",
                          motorSettingModel: setting.motor20!),
                      gapH8,
                      MotorMove(
                        motorModel: motorState.value,
                        motorSettingModel: setting.motor20!,
                        faultStatus: motorState.value.VoltageFaultStatus == 3,
                      ),
                    ],
                  );
                }),
              ),
              Positioned(
                key: const ValueKey('T32'),
                top: appDimension.t22Y(x: size.width, y: size.height),
                left: appDimension.t22X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "22",
                  tankSettingModel: setting.tank22!,
                  height: appDimension.t22Height(x: size.width, y: size.height),
                  width: appDimension.t22Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('L17'),
                top: appDimension.l17Y(x: size.width, y: size.height),
                left: appDimension.l17X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L17',
                  isRight: false,
                  singleFlow: false,
                  leftFlow: true,
                  length: appDimension.l17Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L16'),
                top: appDimension.l16Y(x: size.width, y: size.height),
                left: appDimension.l16X(x: size.width, y: size.height),
                child: VerticalPipeline(
                  name: 'L16',
                  isRight: false,
                  length: appDimension.l16Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L18'),
                top: appDimension.l18Y(x: size.width, y: size.height),
                left: appDimension.l18X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L18',
                  isRight: false,
                  leftFlow: true,
                  singleFlow: false,
                  length: appDimension.l18Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('T11'),
                top: appDimension.t11Y(x: size.width, y: size.height),
                left: appDimension.t11X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "11",
                  tankSettingModel: setting.tank11!,
                  height: appDimension.t11Height(x: size.width, y: size.height),
                  width: appDimension.t11Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('L19'),
                top: appDimension.l19Y(x: size.width, y: size.height),
                left: appDimension.l19X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L19',
                  isRight: false,
                  length: appDimension.l19Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('L20'),
                top: appDimension.l20Y(x: size.width, y: size.height),
                left: appDimension.l20X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L20',
                  isRight: false,
                  length: appDimension.l20Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('T31'),
                top: appDimension.t31Y(x: size.width, y: size.height),
                left: appDimension.t31X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "31",
                  tankSettingModel: setting.tank31!,
                  height: appDimension.t31Height(x: size.width, y: size.height),
                  width: appDimension.t31Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                  key: const ValueKey("M21"),
                  top: appDimension.m21Y(x: size.width, y: size.height),
                  left: appDimension.m21X(x: size.width, y: size.height),
                  child: HookConsumer(builder: (context, ref, child) {
                    final motorState = useState<MotorModel>(const MotorModel());

                    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("21"),
                        (previous, next) {
                      if (next.asData?.value.snapshot.value != null) {
                        final datasnapshot =
                            next.asData?.value.snapshot.value as Map;
                        motorState.value = MotorModel(
                            motorAmps: datasnapshot['current'],
                            output_status: datasnapshot['output_status'],
                            voltage: datasnapshot['voltage'],
                            time: datasnapshot['time'],
                            VoltageFaultStatus: datasnapshot['voltage_fault'],
                            device_status: datasnapshot['device_status'],
                            mobile_status: datasnapshot['mobile_status']);
                      }
                      if (motorState.value.device_status ==
                          motorState.value.mobile_status) {
                        EasyLoading.dismiss();
                      }
                    });
                    return Column(
                      children: [
                        ControlPanel(
                            motorModel: motorState.value,
                            motorId: "21",
                            motorSettingModel: setting.motor21!),
                        MotorMove(
                          motorModel: motorState.value,
                          motorSettingModel: setting.motor21!,
                          faultStatus: motorState.value.VoltageFaultStatus == 3,
                        ),
                      ],
                    );
                  })),
              Positioned(
                key: const ValueKey('T12'),
                top: appDimension.t12Y(x: size.width, y: size.height),
                left: appDimension.t12X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "12",
                  tankSettingModel: setting.tank12!,
                  height: appDimension.t12Height(x: size.width, y: size.height),
                  width: appDimension.t12Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                key: const ValueKey('L21'),
                top: appDimension.l21Y(x: size.width, y: size.height),
                left: appDimension.l21X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L21',
                  isRight: false,
                  leftFlow: true,
                  length: appDimension.l21Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                  key: const ValueKey('V43'),
                  top: appDimension.v43Y(x: size.width, y: size.height),
                  left: appDimension.v43X(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve43!.name,
                    valveId: "43",
                    isRight: true,
                    showDistribution: true,
                    role: prefs.getUserRole(),
                  )),
              Positioned(
                  key: const ValueKey('V42'),
                  top: appDimension.v42Y(x: size.width, y: size.height),
                  left: appDimension.v42X(x: size.width, y: size.height) -
                      appDimension.d12Width(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve42!.name,
                    valveId: "42",
                    showDistribution: true,
                    role: prefs.getUserRole(),
                  )),
              Positioned(
                  key: const ValueKey('V41'),
                  top: appDimension.v41Y(x: size.width, y: size.height),
                  left: appDimension.v41X(x: size.width, y: size.height) -
                      appDimension.d12Width(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve41!.name,
                    valveId: "41",
                    showDistribution: true,
                    role: prefs.getUserRole(),
                  )),
              Positioned(
                key: const ValueKey('L22'),
                top: appDimension.l22Y(x: size.width, y: size.height),
                left: appDimension.l22X(x: size.width, y: size.height),
                child: HorizontalPipeline(
                  name: 'L22',
                  isRight: false,
                  leftFlow: true,
                  length: appDimension.l22Length(x: size.width, y: size.height),
                  isLeft: false,
                ),
              ),
              Positioned(
                key: const ValueKey('T30'),
                top: appDimension.t30Y(x: size.width, y: size.height),
                left: appDimension.t30X(x: size.width, y: size.height),
                child: RectangularTankBlock(
                  tankId: "30",
                  tankSettingModel: setting.tank30!,
                  height: appDimension.t30Height(x: size.width, y: size.height),
                  width: appDimension.t30Width(x: size.width, y: size.height),
                ),
              ),
              Positioned(
                  key: const ValueKey('V40'),
                  top: appDimension.v40Y(x: size.width, y: size.height),
                  left: appDimension.v40X(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve40!.name,
                    valveId: "40",
                    showDistribution: true,
                    isRight: true,
                    role: prefs.getUserRole(),
                  )),
              Positioned(
                  key: const ValueKey('V22'),
                  top: appDimension.v22Y(x: size.width, y: size.height),
                  left: appDimension.v22X(x: size.width, y: size.height) -
                      appDimension.d12Width(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve22!.name,
                    valveId: "22",
                    showDistribution: true,
                    role: prefs.getUserRole(),
                  )),
              Positioned(
                  key: const ValueKey('V50'),
                  top: appDimension.v50Y(x: size.width, y: size.height),
                  left: appDimension.v50X(x: size.width, y: size.height) -
                      appDimension.d12Width(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve50!.name,
                    valveId: "50",
                    showDistribution: true,
                    role: prefs.getUserRole(),
                  )),
              Positioned(
                  key: const ValueKey('V21'),
                  top: appDimension.v21Y(x: size.width, y: size.height),
                  left: appDimension.v21X(x: size.width, y: size.height) -
                      appDimension.d12Width(x: size.width, y: size.height),
                  child: TalkhuValveWidget(
                    size: size,
                    appDimension: appDimension,
                    name: setting.valve21!.name,
                    valveId: "21",
                    showDistribution: true,
                    role: prefs.getUserRole(),
                  )),
            ],
          ),
        ));
  }
}
