

import 'package:flutter/material.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/services/firestore_path.dart';

import '../../../datatable/paginated_data_table_2.dart';
import '../../../utils/format.dart';
import '../../repository/abstract_site_repository.dart';
import '../../repository/site_one_repository.dart';

// Async datasource for AsynPaginatedDataTabke2 example. Based on AsyncDataTableSource which
/// is an extension to FLutter's DataTableSource and aimed at solving
/// saync data fetching scenarious by paginated table (such as using Web API)
class SiteDataSource extends AsyncDataTableSource {


  SiteDataSource({this.userType=1}) {
    print('SiteDataSource created');
  }


  SiteDataSource.empty({this.userType=1}) {
    _empty = true;
    print('SiteDataSource.empty created');
  }

  SiteDataSource.error({this.userType=1}) {
    print('SiteDataSource.error created');
  }

  bool _empty = false;
  final int userType;


  DateTime? _siteDate;

  DateTime? startSiteDate;
  DateTime? endSiteDate;

  String  siteId = FirestorePath.schemaOne;

  set startDate(DateTime? value) {
    startSiteDate = value;
    notifyListeners();
  }

  set SiteId(String value){
    siteId = value;
    notifyListeners();
  }

  int startingIndex = 0;


  List<SiteFireStoreModel> listSiteModel = [];


  final ISiteFirestoreRepository _repo = SiteOneFirestoreRepository();


  Future<int> getTotalRecords() {
    return Future<int>.delayed(
        const Duration(milliseconds: 0), () => _empty ? 0 : 15);
  }

  @override
  Future<AsyncRowsResponse> getRows(int start, int end) async {
    var index = 0;
    final today = DateTime.now();
    var x = start==0  ? await _repo.getSiteHistoryData(siteId: siteId,date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0)) :
    start<startingIndex ?  listSiteModel.skip(start).take(end).toList()   :await _repo.getSiteHistoryData(siteId:siteId,startAfterDate: _siteDate, date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0));
    if(start==0){
      listSiteModel.clear();
    }
    final count = await _repo.getLength(siteId:siteId,dateTime:(startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0)));
    if(count==0){
      _empty = true;
      return AsyncRowsResponse(listSiteModel.length, []);
    }else{
      var r = AsyncRowsResponse(
          count,
          x.map((siteModel) {
            index++;
            return DataRow(
              key: ValueKey<String>(siteModel!.timestamp!.millisecondsSinceEpoch.toString()),
              selected: false,
              onSelectChanged: (value) {

              },
              cells: getSiteCells(siteId, start, index, siteModel),
            );
          }).toList());

      startingIndex =  start;
      if(start>=startingIndex){
        for(final model in x){
          listSiteModel.add(model!);
        }
      }
      _siteDate = x.last!.timestamp;

      return r;
    }

  }
}

List<DataCell> getSiteCells(String siteId,int start, int index, SiteFireStoreModel siteModel){

  switch(siteId){
    case "site1":
      return getSiteOneCells(start, index, siteModel);
    case "site2":
      return getSiteTwoCells(start, index, siteModel);
    case "site3":
      return getSiteThreeCells(start, index, siteModel);
    case "site4":
      return getSiteFourCells(start, index, siteModel);
    case "site5":
      return getSiteFiveCells(start, index, siteModel);
    case "site6":
      return getSiteSixCells(start, index, siteModel);
    case "site7":
      return getSiteSevenCells(start, index, siteModel);
    case "site8":
      return getSiteEightCells(start, index, siteModel);
    default:
      return getSiteOneCells(start, index, siteModel);
  }

}

List<DataCell> getSiteEightCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.bw.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteSevenCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.oh.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.bw.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteSixCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.oh.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.bw.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteFiveCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.oh.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.bw.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteFourCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.bw.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteThreeCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t2.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t3.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t5.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteTwoCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t2.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t3.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}

List<DataCell> getSiteOneCells(int start, int index, SiteFireStoreModel siteModel) {
  return [
    DataCell(ConstrainedBox(constraints: BoxConstraints(maxWidth: 50),child: Text((start+index).toString(),style: TextStyle(fontSize: 14,color : Colors.black),),)),
    DataCell(Text(siteModel.t1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t2.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t3.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.t4.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.cl.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.ph.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(Format.date(siteModel.timestamp!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}