import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';

import '../../services/shared_preferences_service.dart';
import 'hive_key.dart';

abstract class ILocalRepository {

  Future<void> setSettings(DivisionSettingModel divisionSettingModel);

  Future<void> setUserModel(UserModel userModel);

  DivisionSettingModel getSettings();

  UserModel getUserModel();

  Future<void> clearAll();

}

class LocalRepository implements ILocalRepository {
  LocalRepository(this.preference) : super();

  final SharedPreferencesService preference;

  @override
  DivisionSettingModel getSettings() {
    final configurationBox = Hive.box(HiveKey.siBox);
    final settings = configurationBox.get(HiveKey.siBoxSettings);
    if (settings != null) {
      Map<String, dynamic> settingMap = jsonDecode(settings) as Map<String, dynamic>;
      final settingsObject = DivisionSettingModel.fromJson(settingMap);
      return settingsObject;
    } else {
      return const DivisionSettingModel();
    }
  }

  @override
  Future<void> setSettings(DivisionSettingModel remoteConfigurationModel) async {
    String settings = jsonEncode(remoteConfigurationModel.toJson());
    final configurationBox = Hive.box(HiveKey.siBox);
    await configurationBox.put(HiveKey.siBoxSettings, settings);
  }

  @override
  UserModel getUserModel() {
    final userBox = Hive.box(HiveKey.siBox);
    final user = userBox.get(HiveKey.siBoxUser);
    if (user != null) {
      Map<String, dynamic> userMap = jsonDecode(user) as Map<String, dynamic>;
      final userObject = UserModel.fromJson(userMap);
      return userObject;
    } else {
      return const UserModel();
    }
  }

  @override
  Future<void> setUserModel(UserModel userModel) async {
    String user = jsonEncode(userModel.toJson());
    final userBox = Hive.box(HiveKey.siBox);
    await userBox.put(HiveKey.siBoxUser, user);
  }

  @override
  Future<void> clearAll() async {
    final settingBox = Hive.box(HiveKey.siBox);
    settingBox.clear();
    await preference.clearPreferences();
  }

}