

import 'package:flutter/material.dart';
import 'package:graphite/graphite.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../constants/app_sizes.dart';
import 'model/schema_model.dart';

class SchemaSiteThreePage extends StatefulHookConsumerWidget {
  const SchemaSiteThreePage({Key? key}) : super(key: key);

  @override
  SchemaThreeState createState() => SchemaThreeState();
}

class SchemaThreeState extends  ConsumerState<SchemaSiteThreePage>
    with SingleTickerProviderStateMixin {
  CurrentNodeInfo? _currentNodeInfo;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    _animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 300));
    _animation = Tween(begin: 0.0, end: 1.0).animate(_animationController);
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget _tooltip(BuildContext context) {
    const tooltipHeight = 75.0;
    const maxWidth = 310.0;
    _animationController.reset();
    _animationController.forward();
    return Positioned(
        top: _currentNodeInfo!.rect.top - tooltipHeight,
        left: _currentNodeInfo!.rect.left +
            _currentNodeInfo!.rect.width * .5 -
            maxWidth * .5,
        child: SizedBox(
          width: maxWidth,
          height: tooltipHeight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FadeTransition(
                opacity: _animation,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(225),
                    border: Border.all(
                        color: Theme.of(context).primaryColor, width: 5),
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      children: [
                        Text(
                          _currentNodeInfo!.data.title,
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  List<Widget> _buildOverlay(
      BuildContext context, List<NodeInput> nodes, List<Edge> edges) {
    return _currentNodeInfo == null ? [] : [_tooltip(context)];
  }

  _onNodeTap(TapUpDetails details, NodeInput node, Rect nodeRect) {
    setState(() {
      _currentNodeInfo =
          CurrentNodeInfo(node: node, rect: nodeRect, data: datas[node.id]!);
    });
  }

  _onCanvasTap(TapDownDetails details) {
    setState(() {
      _currentNodeInfo = null;
    });
  }

  final controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.green, Colors.red])),
      child: Center(
        child: SingleChildScrollView(
          child: Container(
            child: Center(
              child: Column(
                children: [
                  gapH16,
                  Text("Scheme 3", style: TextStyle(fontSize: 24,color: Colors.white),),
                  gapH16,
                  Scrollbar(
                    controller: controller,
                    interactive: true,
                    thickness: 10,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      controller: controller,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 5.0,bottom: 60),
                        child: DirectGraph(
                          list: imagePresets,
                          defaultCellSize: const Size(100.0, 110.0),
                          cellPadding:
                          const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                          contactEdgesDistance: 10.0,
                          orientation: MatrixOrientation.Horizontal,
                          clipBehavior: Clip.none,
                          centered: true,
                          minScale: .05,
                          maxScale: 1,
                          overlayBuilder: (BuildContext context,
                              List<NodeInput> nodes, List<Edge> edges) =>
                              _buildOverlay(context, nodes, edges),
                          onCanvasTap: _onCanvasTap,
                          onNodeTapUp: _onNodeTap,
                          nodeBuilder: (BuildContext context, NodeInput node) =>
                          datas[node.id]!.child,
                        ),
                      ),
                    ),
                  ),
                  /* Row(
                    children: [
                      sedimentTank("Sediment", "1400 NP", height: 130, width: 130),
                      Text(": ",style: TextStyle(fontSize: 18,color: Colors.black),),
                      Text("Sediment ", style: TextStyle(fontSize: 18,color: Colors.black),),
                      SizedBox(width: 20,),
                      turbidity("Turbidity", "1400 NP",height: 120,width: 80),
                      Text(": ",style: TextStyle(fontSize: 18,color: Colors.black),),
                      Text("Turbidity ",style: TextStyle(fontSize: 18,color: Colors.black),),
                      SizedBox(width: 20,),
                      chlorine("Chlorine", "0.3", height: 120,width: 80),
                      Text(": ",style: TextStyle(fontSize: 18,color: Colors.black),),
                      Text("Chlorine ",style: TextStyle(fontSize: 18,color: Colors.black),),
                      SizedBox(width: 20,),
                      ph("PH Scale", "7.2 / Basic", height: 120, width: 80),
                      Text(": ",style: TextStyle(fontSize: 18,color: Colors.black),),
                      Text("PH Scale ",style: TextStyle(fontSize: 18,color: Colors.black),),
                    ],
                  )*/
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
