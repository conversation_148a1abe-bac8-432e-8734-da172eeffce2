import 'dart:developer';
import 'dart:ui';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import '../../../services/shared_preferences_service.dart';
import '../../model/tank_model.dart';
import '../../model/tank_setting_model.dart';
import '../../provider/division_provider.dart';



class TankBlock extends StatefulHookConsumerWidget {

  const TankBlock({this.roleType = 2,required this.tankId, required this.tankSettingModel,required this.height, required this.width});

  final int roleType;

  final String tankId;

  final double height;

  final double width;

  final TankSettingModel tankSettingModel;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => TankBlockState();
}

class TankBlockState extends ConsumerState<TankBlock> {
  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFFBAE5F3);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final preference = ref.watch(sharedPreferencesServiceProvider);
    final tankLevel = useState<int>(preference.getTankLevel(widget.tankId));

    ref.listen<AsyncValue<DatabaseEvent>>(tankProvider(widget.tankId), (previous, next)  async {
      EasyLoading.dismiss();
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final aftertankModel = TankModel.fromJson(aftercleanData);
        final beforeData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final beforecleanData = Map<String, dynamic>.from(beforeData);
        final beforetankModel = TankModel.fromJson(beforecleanData);
        if(aftertankModel.level<= widget.tankSettingModel.actual_height && aftertankModel.level!=0){
          await preference.setTankLevel(tankId:widget.tankId,level: aftertankModel.level);
          tankLevel.value = aftertankModel.level;
        }else{
          if(beforetankModel.level<=widget.tankSettingModel.actual_height && aftertankModel.level!=0){
            await preference.setTankLevel(tankId:widget.tankId,level: beforetankModel.level);
            tankLevel.value = beforetankModel.level;
          }
        }
      }
    });


    int difference = widget.tankSettingModel.is_bw ? tankLevel.value.abs()  :(widget.tankSettingModel.actual_height - tankLevel.value).abs();
    double perc = (difference+widget.tankSettingModel.offset) / widget.tankSettingModel.actual_height ;
    var percentage = (perc * 100).ceil();
    double correctedPerc = perc +0.1;

    return GestureDetector(
      onLongPress: () async {
        if(preference.getUserRole()>3){
          final result = await context.push("/home/<USER>", extra: widget.tankId);
          if(result!=null && result == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },
      child: Container(
        height: widget.height,
        width: widget.width,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: widget.width,
                height: widget.height - 20,
                decoration: BoxDecoration(
                  border: Border.all(width: 3, color: Colors.black),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(48),
                    topRight: Radius.circular(48),
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Padding(
                  padding:
                  const EdgeInsets.only(bottom: 8.0, left: 6, right: 6),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        height: 30,
                        child:  Center(
                          child:  Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 12.0),
                            child:  Text(
                              preference.getUserRole()>2 ? widget.tankSettingModel.name+"("+widget.tankId+")" : widget.tankSettingModel.name,
                              style: TextStyle(fontSize: 12, height: 1),
                              maxLines: 2,
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                      Stack(
                        children: [
                          Center(
                            child: Container(
                              height: widget.height-70,
                              width: widget.width,
                              child: Stack(
                                children: [
                                  GestureDetector(
                                    child: WaveWidget(
                                      config: CustomConfig(
                                        colors: _colors,
                                        durations: _durations,
                                        heightPercentages: [
                                          1 - correctedPerc,
                                          1 - correctedPerc
                                        ],
                                      ),
                                      backgroundColor: _backgroundColor,
                                      size: Size(
                                          double.infinity, double.infinity),
                                      waveAmplitude: 0,
                                      heightPercentage: 0.1,
                                    ),
                                  ),
                                  Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        percentage.toString() + ' %',
                                        style: TextStyle(
                                          fontSize: 20,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment(0, -0.95),
              child: Image.asset(
                "assets/icons/tank_cover.png",
                fit: BoxFit.fill,
                height: 30,
                width: 80,
              ),
            ),

          ],
        ),
      ),
    );
  }
}



class RectangularTankBlock extends StatefulHookConsumerWidget {

  const RectangularTankBlock({this.roleType = 2,required this.tankId, required this.tankSettingModel,required this.height, required this.width});

  final int roleType;

  final String tankId;

  final double height;

  final double width;

  final TankSettingModel tankSettingModel;
  @override
  ConsumerState<ConsumerStatefulWidget> createState() => RectangularTankBlockState();
}

class RectangularTankBlockState extends ConsumerState<RectangularTankBlock> {
  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFFBAE5F3);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final preference = ref.watch(sharedPreferencesServiceProvider);
    final tankLevel = useState<int>(preference.getTankLevel(widget.tankId));

    ref.listen<AsyncValue<DatabaseEvent>>(tankProvider(widget.tankId), (previous, next)  async {
      EasyLoading.dismiss();
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final aftertankModel = TankModel.fromJson(aftercleanData);
        final beforeData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final beforecleanData = Map<String, dynamic>.from(beforeData);
        final beforetankModel = TankModel.fromJson(beforecleanData);
        if(aftertankModel.level<= widget.tankSettingModel.actual_height && aftertankModel.level!=0){
          await preference.setTankLevel(tankId:widget.tankId,level: aftertankModel.level);
          tankLevel.value = aftertankModel.level;
        }else{
          if(beforetankModel.level<=widget.tankSettingModel.actual_height && aftertankModel.level!=0){
            await preference.setTankLevel(tankId:widget.tankId,level: beforetankModel.level);
            tankLevel.value = beforetankModel.level;
          }
        }
      }
    });


    int difference = widget.tankSettingModel.is_bw ? tankLevel.value.abs()  :(widget.tankSettingModel.actual_height - tankLevel.value).abs();
    double perc = (difference + widget.tankSettingModel.offset) / widget.tankSettingModel.actual_height ;
    var percentage = (perc * 100).ceil();
    double correctedPerc = perc +0.1;

    return GestureDetector(
      onTap: () {

      },
      child: Container(
        height: widget.height,
        width: widget.width,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: widget.width,
                height: widget.height - 15,
                decoration: BoxDecoration(
                  border: Border.all(width: 3, color: Colors.black),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
                child: Padding(
                  padding:
                  const EdgeInsets.only(bottom: 4.0, left: 4, right: 4),
                  child: Stack(
                    children: [

                      Stack(
                        children: [
                          Center(
                            child: Container(
                              height: widget.height - 5,
                              width: widget.width,
                              child: Stack(
                                children: [
                                  GestureDetector(
                                    child: WaveWidget(
                                      config: CustomConfig(
                                        colors: _colors,
                                        durations: _durations,
                                        heightPercentages: [
                                          1 - correctedPerc,
                                          1 - correctedPerc
                                        ],
                                      ),
                                      backgroundColor: _backgroundColor,
                                      size: Size(
                                          double.infinity, double.infinity),
                                      waveAmplitude: 0,
                                      heightPercentage: 0.1,
                                    ),
                                  ),
                                  Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        percentage.toString() + ' %',
                                        style: TextStyle(
                                          fontSize: 20,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            child:  Padding(
                              padding:
                              const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12),
                              child:  Text(
                                widget.tankSettingModel.name + "("+widget.tankId+")",
                                style: TextStyle(fontSize: 12, height: 1),
                                maxLines: 2,
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment(0.2, -1),
              child: Image.asset(
                "assets/icons/tank_cover.png",
                fit: BoxFit.fill,
                height: 30,
                width: 80,
              ),
            ),

          ],
        ),
      ),
    );
  }
}


class InactiveTankBlock extends StatefulHookConsumerWidget {

  const InactiveTankBlock({required this.height, required this.width});



  final double height;

  final double width;


  @override
  ConsumerState<ConsumerStatefulWidget> createState() => InActiveTankBlockState();
}

class InActiveTankBlockState extends ConsumerState<InactiveTankBlock> {
  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFFBAE5F3);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final preference = ref.watch(sharedPreferencesServiceProvider);


    int difference = 150;
    double perc = difference / 350 ;
    var percentage = (perc * 100).ceil();
    double correctedPerc = perc +0.1;

    return GestureDetector(
      onLongPress: () async {

      },
      child: Container(
        height: widget.height,
        width: widget.width,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: widget.width,
                height: widget.height - 20,
                decoration: BoxDecoration(
                  border: Border.all(width: 3, color: Colors.black),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(48),
                    topRight: Radius.circular(48),
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Padding(
                  padding:
                  const EdgeInsets.only(bottom: 8.0, left: 6, right: 6),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        height: 30,
                        child:  Center(
                          child:  Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 12.0),
                            child:  Text(
                              "Tank (Inactive)",
                              style: TextStyle(fontSize: 12, height: 1),
                              maxLines: 2,
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                      Stack(
                        children: [
                          Center(
                            child: Container(
                              height: widget.height-70,
                              width: widget.width,
                              child: Stack(
                                children: [
                                  GestureDetector(
                                    child: WaveWidget(
                                      config: CustomConfig(
                                        colors: _colors,
                                        durations: _durations,
                                        heightPercentages: [
                                          1 - correctedPerc,
                                          1 - correctedPerc
                                        ],
                                      ),
                                      backgroundColor: _backgroundColor,
                                      size: Size(
                                          double.infinity, double.infinity),
                                      waveAmplitude: 0,
                                      heightPercentage: 0.1,
                                    ),
                                  ),
                                  Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        percentage.toString() + ' %',
                                        style: TextStyle(
                                          fontSize: 20,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment(0, -0.95),
              child: Image.asset(
                "assets/icons/tank_cover.png",
                fit: BoxFit.fill,
                height: 30,
                width: 80,
              ),
            ),

          ],
        ),
      ),
    );
  }
}
