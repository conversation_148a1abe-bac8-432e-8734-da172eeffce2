import 'package:flutter/material.dart';
import 'package:si/constants/style_manager.dart';

import '../../constants/app_sizes.dart';
import '../../utils/app_colors.dart';
import 'custom_loading_indicator.dart';

/// Primary button based on [ElevatedButton].
/// Useful for CTAs in the app.
/// @param text - text to display on the button.
/// @param isLoading - if true, a loading indicator will be displayed instead of
/// the text.
/// @param onPressed - callback to be called when the button is pressed.
class PrimaryButton extends StatelessWidget {
  const PrimaryButton(
      {Key? key, required this.text, this.isLoading = false,
        this.buttonPrimaryColor,
        this.buttonSecondaryColor,
        this.isGradient = true,
        this.textSize = FontSize.s20,
        this.circleRadius = Sizes.p32,
        this.height = Sizes.p60,this.textColor,this.onPressed})
      : super(key: key);
  final String text;
  final Color? buttonPrimaryColor;
  final Color? buttonSecondaryColor;
  final Color? textColor;
  final bool isLoading;
  final double height;
  final bool isGradient;
  final double textSize;
  final double circleRadius;
  final VoidCallback? onPressed;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(

        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 1.0],
          colors: [
            AppColors.themeColor,
            isGradient ? AppColors.themeColor  : AppColors.themeColor,
          ],
        ),
        color: AppColors.themeColor,
        borderRadius: BorderRadius.circular(circleRadius),
      ),
      child: ElevatedButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(circleRadius),
            ),
          ),
          minimumSize: MaterialStateProperty.all(Size(double.infinity, height)),
          backgroundColor:
          MaterialStateProperty.all(Colors.transparent),
          // elevation: MaterialStateProperty.all(3),
          shadowColor:
          MaterialStateProperty.all(Colors.transparent),
        ),
        onPressed:
        isLoading ? null : () => onPressed?.call(),

        child: isLoading ?  LoadingIndicator(color: Colors.white,)
            : Text(text, style: TextStyle(color: AppColors.onThemeColor ?? Colors.white,fontSize: textSize,fontWeight: FontWeight.bold),),
      ),
    );
  }
}
