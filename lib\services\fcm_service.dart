

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';

class FCMService {
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;

  Future<void> requestPermission() async {
    NotificationSettings settings = await _fcm.requestPermission(
      alert: true,
      announcement: true,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      if (kDebugMode) {
        debugPrint('Notification permission granted');
      }
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      if (kDebugMode) {
        debugPrint('provisional permission granted');
      }
    } else {
      if (kDebugMode) {
        debugPrint('no permission granted');
      }

      var requestedResponse = await Permission.notification.request();
      if (requestedResponse.isGranted) {
        debugPrint('requestNotificaitonAccess isGranted = true.');
        return;
      }

      if (requestedResponse.isDenied) {
        var secondTryResponbse = await Permission.notification.request();
      }
    }
  }

  Future<void> loadFcm() async {
    var channel = const AndroidNotification();

    if (!kIsWeb) {
      channel = const AndroidNotification(
        channelId: 'high_notification_channel',
        priority: AndroidNotificationPriority.highPriority,
      );
    }
    await _fcm.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // FirebaseMessaging.onMessageOpenedApp.listen((event) {
    //
    //   if(event.data.containsKey('screen_type') && event.data['screen_type'] == ScreenType.community.name){
    //
    //   }
    //
    // });
  }

  Future<RemoteMessage> firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    await Firebase.initializeApp();

    EasyLoading.showToast('This is ${message.data}');
    if (message.data.containsKey('screen_type')) {
      return message;
      // final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
      // late final _appRouter = AppRouter(
      //   authRouteGuard: AuthRouteGuard(),
      //   unAuthGuard: UnAuthGuard(),
      //   navigatorKey: navigatorKey,
      // );
      //
      // _appRouter.push(CommunityDetailScreen(
      //   feedId: message.data['post_id'],
      //   likeCount: 0,
      // ));

    }

    return message;
  }

  Future<String?> getToken() async {
    String? fcmToken;
    fcmToken = await _fcm.getToken();
    _fcm.onTokenRefresh.listen((refreshedToken) {
      fcmToken = refreshedToken;
      debugPrint('FCM TOKEN: $fcmToken');
    });

    return fcmToken;
  }
}
