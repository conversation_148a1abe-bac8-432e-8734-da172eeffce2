
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import '../../services/shared_preferences_service.dart';
import '../model/tank_model.dart';
import '../provider/division_provider.dart';
import 'distribution_water_volume.dart';

class TankWidget extends HookConsumerWidget {
  static const _backgroundColor = Color(0xFFBAE5F3);

  const TankWidget(this.roleType, this.tankId,this.tankSettingModel );

  final int roleType;

  final String tankId;

  final TankSettingModel tankSettingModel;

  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initialLevelProvider = ref.watch(futureTankProvider(tankId));
    final preference = ref.watch(sharedPreferencesServiceProvider);
    final tankLevel = useState<int>(preference.getTankLevel(tankId));


    ref.listen<AsyncValue<DatabaseEvent>>(tankProvider(tankId), (previous, next)  async {
      EasyLoading.dismiss();
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final aftertankModel = TankModel.fromJson(aftercleanData);
        final beforeData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final beforecleanData = Map<String, dynamic>.from(beforeData);
        final beforetankModel = TankModel.fromJson(beforecleanData);
        if(aftertankModel.level<= aftertankModel.actual_height && aftertankModel.level!=0){
          await preference.setTankLevel(tankId:tankId,level: aftertankModel.level);
          tankLevel.value = aftertankModel.level;
        }else{
          if(beforetankModel.level<=aftertankModel.actual_height && aftertankModel.level!=0){
            await preference.setTankLevel(tankId:tankId,level: beforetankModel.level);
            tankLevel.value = beforetankModel.level;
          }
        }
      }

    });
    return initialLevelProvider.when(
              data: (_data){
                int difference = (tankLevel.value).abs();
                double perc = difference / tankSettingModel.actual_height ;
                var percentage = (perc * 100).ceil();
                double correctedPerc = perc +0.1;
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      tankSettingModel.name,
                      style: TextStyle(fontSize: 14),
                    ),

                    Stack(
                      children: [
                        Center(
                          child: Container(
                            height: preference.getTankNumber() >= 3 ? MediaQuery.of(context).size.height / 3.3 :
                    preference.getTankNumber() == 2 ? MediaQuery.of(context).size.height / 2.3 : MediaQuery.of(context).size.height / 2,
                            width: preference.getTankNumber() >= 3  ? MediaQuery.of(context).size.width / 3.3 :
                        preference.getTankNumber() == 2 ? MediaQuery.of(context).size.width / 2.3 : MediaQuery.of(context).size.width / 2.1,
                            child: Stack(
                              children: [
                                GestureDetector(
                                  child: WaveWidget(
                                    config: CustomConfig(
                                      colors: _colors,
                                      durations: _durations,
                                      heightPercentages: [1 - correctedPerc, 1 - correctedPerc],
                                    ),
                                    backgroundColor: _backgroundColor,
                                    size: Size(double.infinity, double.infinity),
                                    waveAmplitude: 0,
                                    heightPercentage: 0.1,
                                  ),
                                ),
                                Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(
                                      percentage.toString() + ' %',
                                      style: TextStyle(fontSize: 20),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        /*Positioned(
                          top: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: (){
                              if(tankSettingModel.is_active){
                                if(tankId=='tank1'){
                                  ref.read(divisionRepositoryProvider).updateTankActive(tankId, false);
                                  ref.read(divisionRepositoryProvider).updateTankActive('tank2', true);
                                }else{
                                  ref.read(divisionRepositoryProvider).updateTankActive(tankId, false);
                                  ref.read(divisionRepositoryProvider).updateTankActive('tank1', true);
                                }
                              }else{
                                if(tankId=='tank1'){
                                  ref.read(divisionRepositoryProvider).updateTankActive(tankId, true);
                                  ref.read(divisionRepositoryProvider).updateTankActive('tank2', false);
                                }else{
                                  ref.read(divisionRepositoryProvider).updateTankActive(tankId, true);
                                  ref.read(divisionRepositoryProvider).updateTankActive('tank1', false);
                                }
                              }

                            },
                            child: Icon(
                              tankSettingModel.is_active ? Icons.check_box : Icons.check_box_outline_blank,
                              color: tankSettingModel.is_active ? Colors.green : Colors.grey,
                            ),
                          ),
                        )*/

                      ],
                    ),


                  ],
                );
              },
              error: (er,st){
                EasyLoading.dismiss();
                return Container();

              },
              loading: ()=>Container());


  }
}
