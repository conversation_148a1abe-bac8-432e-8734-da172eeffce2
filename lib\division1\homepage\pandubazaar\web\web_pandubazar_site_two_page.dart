

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common/widgets/widgets.dart';
import '../../../../constants/app_sizes.dart';
import '../../../../provider/auth_provider.dart';
import '../../../model/division_setting_model.dart';
import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';
import '../../new_motor_unit.dart';
import '../../new_tank_unit.dart';
import '../../widgets/sensor_widget.dart';


class WebPandubazarSiteTwoPage extends HookConsumerWidget{


  const WebPandubazarSiteTwoPage(this.setting);

  final DivisionSettingModel setting;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor30State = useState<MotorModel>(MotorModel());
    final motor31State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("30"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor30State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor30State.value.device_status == motor30State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("31"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor31State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor31State.value.device_status == motor31State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return CustomScrollView(
      slivers: [
        const SliverToBoxAdapter(
          child: gapH4,
        ),
        SliverToBoxAdapter(
          child:Stack(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      NewWebMotorUnit(
                        width: 140,
                        motorId: "30",
                        motorSettingModel: setting.motor30,
                        motorModel: motor30State.value,
                      ),
                      Container(
                        width: 20,
                          height: 10,
                          color: Colors.blueAccent,
                      )
                    ],
                  ),
                  Column(
                    children: [
                      NewWebMotorUnit(
                        width: 140,
                        motorId: "31",
                        motorSettingModel: setting.motor31,
                        motorModel: motor31State.value,
                      ),
                      Container(
                        width: 20,
                        height: 10,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ],
              ),
              Positioned(
                top: 0,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.logout_outlined),
                  onPressed: () async {
                    final bool didRequestSignOut =
                        await showAlertDialog(
                          context: context,
                          title: 'Logout',
                          content:
                          'Are you sure you want to logout?',
                          cancelActionText: 'Cancel',
                          defaultActionText: 'Yes',
                        ) ??
                            false;
                    if (didRequestSignOut == true) {
                      try {
                        EasyLoading.show(status: 'Logging Out');
                        await ref
                            .read(authRepositoryProvider)
                            .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                        await ref
                            .read(authRepositoryProvider)
                            .signOut();
                        // MyApp.of(context).authService.authenticated = true;
                        // onLoginCallback?.call(true);
                        // AutoRouter.of(context).push(const DashboardRouter());
                        EasyLoading.dismiss();
                        context.replace('/login');
                      } catch (err) {
                        print(err);
                        // debugPrint("Error :$err");
                      }
                    }
                  },
                ),
              ),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            height: 20,
            width: double.infinity,
            color: Colors.blueAccent,
          ),
        ),
        SliverToBoxAdapter(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TurbiditySensorWidget(sensorId: "21", isPortrait: true,
              setting: setting.sensor21,),
              gapW8,
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 15,
                    color: Colors.blueAccent,
                  ),
                  Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            NewTankUnit(3, "30", setting.tank30!, 100, 90),
                            Container(
                              height: 15,
                              width: 20,
                              color : Colors.blueAccent,
                            ),
                            NewTankUnit(3, "21", setting.tank21!, 100, 90),

                          ],
                        ),
                      )),
                  Container(
                    width: 20,
                    height: 15,
                    color: Colors.blueAccent,
                  )
                ],
              ),
              gapW4,
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 15,
                    color: Colors.blueAccent,
                  ),
                  Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Colors.black),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            NewTankUnit(3, "31", setting.tank31!, 100, 90),
                            Container(
                              height: 15,
                              width: 20,
                              color : Colors.blueAccent,
                            ),
                            NewTankUnit(3, "32", setting.tank32!, 100, 90),
                          ],
                        ),
                      )),
                  Container(
                    width: 20,
                    height: 15,
                    color: Colors.blueAccent,
                  )
                ],
              ),
              gapW4,
              TurbiditySensorWidget(sensorId: "32", isPortrait: true,
              setting: setting.sensor32,),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            height: 30,
            color: Colors.blueAccent,
              child: const Center(
                child: Text(
                  "वितरण",
                  style: TextStyle(color: Colors.white,fontSize: 18),
                ),
              ),
          ),
        )
      ],
    );
  }

}