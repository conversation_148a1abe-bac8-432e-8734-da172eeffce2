

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/homepage/widgets/motor_widget.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/homepage/widgets/tank_widget.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';

class HaripurSystemOnePage extends HookConsumerWidget{

  const HaripurSystemOnePage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());
    final valve10State = useState<MotorModel>(MotorModel());
    final valve11State = useState<MotorModel>(MotorModel());
    final dimension = ref.read(dimensionProvider);
    final size = MediaQuery.of(context).size;
    final prefs = ref.read(sharedPreferencesServiceProvider);

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve10State.value = MotorModel(
                output_status: datasnapshot['output_status'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve10State.value.device_status == valve10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve11State.value = MotorModel(
                output_status: datasnapshot['output_status'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve11State.value.device_status == valve11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return Center(
      child: Container(
        width: 360,
        child: Stack(
          children: [
            Positioned(
              left: 16,
              top: 280,
              child: TankBlock(
                tankSettingModel: setting.tank10!,
                tankId: "10",
                height: 260,
                width: 220,
              ),
            ),

            Positioned(
              left: 360/2-10,
              top: 116,
              child: VerticalPipeline(
                length: 190,
                isTop: false,
                topFlow: true,
                singleFlow: false,
                isBottom: false,
              ),
            ),
            Positioned(
                left: 24,
                top: 16,
                child: NewMotorUnit(
                  motorSettingModel: setting.motor10!,
                  motorModel: motor10State.value,
                  motorId: "10",
                )),

            Positioned(
                top: 16,
                right: 24,
                child: NewMotorUnit(
                  motorSettingModel: setting.motor11!,
                  motorModel: motor11State.value,
                  motorId: "11",
                )),
            Positioned(
              left: 24+360/3-2,
              top: 100,
              child: HorizontalPipeline(
                length: 70+5,
                isLeft: false,
                isRight: false,
              ),
            ),
            Positioned(
              left: 235,
              top: 430,
              child: HaripurValveWidget(
                size: size,
                role: prefs.getUserRole(),
                appDimension: dimension,
                valveId: "10",
                motorModel: valve10State.value,
                
                name: setting.valve10?.name ?? "Valve 10",
              ),
            ),
            Positioned(
              left: 290,
              top: 458,
              child: Image.asset(
                valve10State.value.output_status == 3 ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
                height: 40,
                width: 70,
                fit: BoxFit.contain,
              ),
            ),
            Positioned(
              left: 235,
              top: 320,
              child: HaripurValveWidget(
                size: size,
                role: prefs.getUserRole(),
                appDimension: dimension,
                valveId: "11",
                motorModel: valve11State.value,
                name: setting.valve11?.name ?? "Valve 11",
              ),
            ),
            Positioned(
              left: 290,
              top: 348,
              child: Image.asset(
                valve11State.value.output_status == 3 ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
                height: 40,
                width: 70,
                fit: BoxFit.contain,
              ),
            ),



          ],
        ),
      ),
    );

  }

}