import 'package:flutter/material.dart';

class CustomPinchZoom extends StatefulWidget {
  final Widget child;
  final double maxScale;
  final Duration resetDuration;
  final bool zoomEnabled;
  final Function? onZoomStart, onZoomEnd;

  const CustomPinchZoom(
      {super.key,
      required this.child,
      this.resetDuration = const Duration(milliseconds: 100),
      this.maxScale = 3.0,
      this.zoomEnabled = true,
      this.onZoomStart,
      this.onZoomEnd});

  @override
  _CustomPinchZoomState createState() => _CustomPinchZoomState();
}

class _CustomPinchZoomState extends State<CustomPinchZoom> with SingleTickerProviderStateMixin {
  final TransformationController _transformationController = TransformationController();

  late Animation<Matrix4> _animationReset;
  late AnimationController _controllerReset;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SizedBox(
        height: double.maxFinite,
        width: double.maxFinite,
        child: <PERSON>Viewer(
          scaleEnabled: widget.zoomEnabled,
          maxScale: widget.maxScale,
          panEnabled: false,
          onInteractionStart: widget.zoomEnabled
              ? (_) {
                  if (_controllerReset.status == AnimationStatus.forward) {
                    _animateResetStop();
                  } else {
                    if (widget.onZoomStart != null) {
                      widget.onZoomStart!();
                    }
                  }
                }
              : null,
          // onInteractionEnd: (_) => _animateResetInitialize(),
          transformationController: _transformationController,
          child: widget.child,
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _controllerReset = AnimationController(
      duration: widget.resetDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _controllerReset.dispose();
    super.dispose();
  }

  /// Go back to static state after resetting has ended
  void _onAnimateReset() {
    _transformationController.value = _animationReset.value;
    if (!_controllerReset.isAnimating) {
      _animationReset.removeListener(_onAnimateReset);
      _animationReset = Matrix4Tween().animate(_controllerReset);
      _controllerReset.reset();
      if (widget.onZoomEnd != null) {
        widget.onZoomEnd!();
      }
    }
  }

  /// Start resetting the animation
  void _animateResetInitialize() {
    _controllerReset.reset();
    _animationReset = Matrix4Tween(
      begin: _transformationController.value,
      end: Matrix4.identity(),
    ).animate(_controllerReset);
    _animationReset.addListener(_onAnimateReset);
    _controllerReset.forward();
  }

  /// Stop the reset animation
  void _animateResetStop() {
    _controllerReset.stop();
    _animationReset.removeListener(_onAnimateReset);
    _animationReset = Matrix4Tween().animate(_controllerReset);
    _controllerReset.reset();
  }
}
