

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:si/common/widgets/alertdialog/async_value_ui.dart';
import 'package:si/model/dashboard_model.dart';
import 'package:si/provider/dashboard_provider.dart';

import '../../constants/app_sizes.dart';
import '../../utils/format.dart';

class MotorMoving extends HookConsumerWidget{

  final DashboardModel dashboardModel;

  MotorMoving(this.dashboardModel, this.site);
  final String site;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onDoubleTap: (){
        if(ref.read(userState).role==3){
          if(site=="site1"){
            context.push('/siteOne/history');
          }else{
            context.push('/siteTwo/history');
          }
        }

      },
      child: Column(
        children: [
          Container(
            height: 70,
            width: 70,
            decoration: new BoxDecoration(
                color: Colors.transparent,
                shape: BoxShape.circle,
                border: Border.all(color: dashboardModel.motor_status==1 ? Colors.green : Colors.grey ,width: 5)
            ),
            child: Stack(
              children: [
                Visibility(
                  visible: dashboardModel.motor_status!=1,
                  child: Center(
                    child: Container(
                      height: 50,
                        width: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: dashboardModel.motor_status!=1,
                  child: Center(
                    child: Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: dashboardModel.motor_status==1,
                  child: Center(
                    child: SpinKitDualRing(
                      color: Colors.green,
                      size: 50.0,
                    ),
                  ),
                ),
                Visibility(
                  visible: dashboardModel.motor_status==1,
                  child: Center(
                    child: LoadingAnimationWidget.beat(
                      color: Colors.green,
                      size: 25,
                    ),
                  ),
                ),
                Visibility(
                  visible: dashboardModel.motor_status==1,
                  child: Center(
                    child: LoadingAnimationWidget.hexagonDots(
                      color: Colors.green,
                      size: 40,
                    ),
                  ),
                ),
              ],
            ),
          ),
          gapH16,
          Consumer(
            builder: (context,ref,child) {
              return FlutterSwitch(
                  width: 100.0,
                  height: 40.0,
                  activeColor: Colors.green,
                  inactiveColor: Colors.grey,
                  valueFontSize: 16.0,
                  toggleSize: 30.0,
                  value: dashboardModel.motor_status==1,
                  borderRadius: 30.0,
                  padding: 8.0,
                  showOnOff: true,
                  onToggle: (val) async {
                    if(dashboardModel.mode==1){
                      if(ref.read(userState).role==3){
                        if(dashboardModel.motor_status==1){
                          await ref
                              .read(rtdbProvider)
                              .stopMotor(site=="site1" ? "motor1" : "motor2");
                        }else{
                          await ref
                              .read(rtdbProvider)
                              .startMotor(site=="site1" ? "motor1" : "motor2");
                        }
                      }else{
                        EasyLoading.showError("You don't have permission to perform this action.");
                      }
                    }else{
                      EasyLoading.showError('Manual operation is forbidden.');
                    }

                  });
            }
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              Format.date(dashboardModel.motor_updated_at!),
              style: TextStyle(color: Colors.black),
            ),
          ),
          gapH16,
        ],
      ),
    );
  }



}