

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class BoreWellSensor extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
        padding: const EdgeInsets.all(0),
        child: Center(
            child: Container(
                height: 250,
                padding: const EdgeInsets.all(0.0),
                child: SfLinearGauge(
                  orientation: LinearGaugeOrientation.vertical,
                  maximum: 100,
                  tickPosition: LinearElementPosition.outside,
                  labelPosition: LinearLabelPosition.outside,
                  minorTicksPerInterval: 0,
                  interval: 10,
                  onGenerateLabels: () {
                    return <LinearAxisLabel>[
                      const LinearAxisLabel(text: '100 m', value: 0),
                      const LinearAxisLabel(text: '90 m', value: 10),
                      const LinearAxisLabel(text: '80 m', value: 20),
                      const LinearAxisLabel(text: '70 m', value: 30),
                      const LinearAxisLabel(text: '60 m', value: 40),
                      const LinearAxisLabel(text: '50 m', value: 50),
                      const LinearAxisLabel(text: '40 m', value: 60),
                      const LinearAxisLabel(text: '30 m', value: 70),
                      const LinearAxisLabel(text: '20 m', value: 80),
                      const LinearAxisLabel(text: '10 m', value: 90),
                      const LinearAxisLabel(text: '0 m', value: 100),
                    ];
                  },
                  axisTrackStyle: const LinearAxisTrackStyle(),
                  markerPointers: <LinearMarkerPointer>[
                    LinearShapePointer(
                        value: 20,
                        enableAnimation: false,
                        onChanged: (dynamic value) {

                        },
                        shapeType: LinearShapePointerType.rectangle,
                        color: const Color(0xff0074E3),
                        height: 1.5,
                        width: 230),
                    LinearWidgetPointer(
                        value: 20,
                        enableAnimation: false,
                        onChanged: (dynamic value) {

                        },
                        child: SizedBox(
                            width: 24,
                            height: 16,
                            child: Image.asset(
                              'icons/rectangle_pointer.png',
                            ))),
                    LinearWidgetPointer(
                        value: 20,
                        enableAnimation: false,
                        onChanged: (dynamic value) {

                        },
                        offset: 165,
                        position: LinearElementPosition.outside,
                        child: Container(
                            width: 60,
                            height: 25,
                            decoration: BoxDecoration(
                                color: Colors.grey,
                                boxShadow: <BoxShadow>[
                                  BoxShadow(
                                    color: Colors.grey,
                                    offset: const Offset(0.0, 1.0), //(x,y)
                                    blurRadius: 6.0,
                                  ),
                                ],
                                borderRadius: BorderRadius.circular(4)),
                            child: Center(
                              child: Text(
                                  20.toStringAsFixed(0) + ' cm',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.normal,
                                      fontSize: 14,
                                      color: Color(0xff0074E3))),
                            ))),
                  ],
                  ranges: <LinearGaugeRange>[
                    LinearGaugeRange(
                      endValue: 20,
                      startWidth: 230,
                      midWidth:  230,
                      endWidth: 230,
                      color: Colors.lightBlueAccent,
                    ),
                    LinearGaugeRange(
                      startValue: 20,
                      endValue: 230,
                      startWidth: 230,
                      midWidth:  230,
                      endWidth: 230,
                      color: Colors.brown.withOpacity(0.7),
                    ),
                  ],
                ))));
  }

}