import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../constants/app_sizes.dart';
import '../primary_button.dart';

/// Generic function to show a platform-aware Material or Cupertino dialog
Future<bool?> showAlertDialog({
  required BuildContext context,
  required String title,
  String? content,
  String? cancelActionText,
  String defaultActionText = 'OK',
  bool barrierDismissible = true,
  TextStyle? titleStyle,
}) async {
  if (kIsWeb || !Platform.isIOS) {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: content != null ? Text(content) : null,
        actions: <Widget>[
          if (cancelActionText != null)
            TextButton(
              child: Text(cancelActionText),
              onPressed: () => Navigator.of(context).pop(false),
            ),
          TextButton(
            child: Text(defaultActionText),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );
  }
  return showCupertinoDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (context) => CupertinoAlertDialog(
      title: Text(title),
      content: content != null ? Text(content) : null,
      actions: <Widget>[
        if (cancelActionText != null)
          CupertinoDialogAction(
            child: Text(cancelActionText),
            onPressed: () => Navigator.of(context).pop(false),
          ),
        CupertinoDialogAction(
          child: Text(defaultActionText),
          onPressed: () => Navigator.of(context).pop(true),
        ),
      ],
    ),
  );
}

/// Generic function to show a platform-aware Material or Cupertino error dialog
Future<void> showExceptionAlertDialog({
  required BuildContext context,
  required String title,
  required String message,
}) =>
    showAlertDialog(
      context: context,
      title: title,
      content: message,
      defaultActionText: "OK",
    );

Future<void> showNotImplementedAlertDialog({required BuildContext context}) => showAlertDialog(
      context: context,
      title: "Not Implemented",
    );

Future<bool?> showCustomDialog(
        {required BuildContext context,
        required WidgetRef ref,
        required Widget icon,
        required String title,
          bool showClose = true,
          bool barrierDismissable = true,
        required String description,
        required VoidCallback onPressed,
        required String buttonText}) =>
    showGeneralDialog(
      barrierColor: Colors.black.withOpacity(0.5),
      transitionBuilder: (context, a1, a2, widget) {
        return WillPopScope(
          onWillPop: () async{
            return barrierDismissable;
          },
          child: Transform.scale(
            scale: a1.value,
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                contentPadding: padding16,

                shape: OutlineInputBorder(borderRadius: BorderRadius.circular(16.0)),
                //  icon: icon,
                //  title: Text(title, style: Theme.of(context).textTheme.titleLarge,),
                //  content: Text(description,style: Theme.of(context).textTheme.bodyMedium,textAlign: TextAlign.center,),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Visibility(
                      visible: showClose,
                      child: Align(
                        alignment: Alignment.topRight,
                        child: InkWell(
                            onTap: () {
                              Navigator.of(context).pop(false);
                            },
                            child: const Icon(Icons.close)),
                      ),
                    ),
                    Padding(
                      padding: padding8,
                      child: icon,
                    ),
                    Padding(
                      padding: padding8,
                      child: Text(
                        title,
                      ),
                    ),
                    Padding(
                      padding: padding8,
                      child: Text(
                        description,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    gapH24,
                    Padding(
                      padding: padding8,
                      child: PrimaryButton(
                        height: 48,
                        text: buttonText,
                        onPressed: () {
                          onPressed.call();
                        },
                      ),
                    ),
                    gapH16
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      barrierDismissible: barrierDismissable,
      barrierLabel: '',
      context: context,
      pageBuilder: (context, animation1, animation2) {
        return const Text("");
      },
    );


