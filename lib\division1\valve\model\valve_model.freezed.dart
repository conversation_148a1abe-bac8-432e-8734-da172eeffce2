// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'valve_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ValveModel _$ValveModelFromJson(Map<String, dynamic> json) {
  return _ValveModel.fromJson(json);
}

/// @nodoc
mixin _$ValveModel {
  int get device_status => throw _privateConstructorUsedError;
  int get mobile_status => throw _privateConstructorUsedError;

  /// Serializes this ValveModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ValveModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ValveModelCopyWith<ValveModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ValveModelCopyWith<$Res> {
  factory $ValveModelCopyWith(
          ValveModel value, $Res Function(ValveModel) then) =
      _$ValveModelCopyWithImpl<$Res, ValveModel>;
  @useResult
  $Res call({int device_status, int mobile_status});
}

/// @nodoc
class _$ValveModelCopyWithImpl<$Res, $Val extends ValveModel>
    implements $ValveModelCopyWith<$Res> {
  _$ValveModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ValveModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device_status = null,
    Object? mobile_status = null,
  }) {
    return _then(_value.copyWith(
      device_status: null == device_status
          ? _value.device_status
          : device_status // ignore: cast_nullable_to_non_nullable
              as int,
      mobile_status: null == mobile_status
          ? _value.mobile_status
          : mobile_status // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ValveModelImplCopyWith<$Res>
    implements $ValveModelCopyWith<$Res> {
  factory _$$ValveModelImplCopyWith(
          _$ValveModelImpl value, $Res Function(_$ValveModelImpl) then) =
      __$$ValveModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int device_status, int mobile_status});
}

/// @nodoc
class __$$ValveModelImplCopyWithImpl<$Res>
    extends _$ValveModelCopyWithImpl<$Res, _$ValveModelImpl>
    implements _$$ValveModelImplCopyWith<$Res> {
  __$$ValveModelImplCopyWithImpl(
      _$ValveModelImpl _value, $Res Function(_$ValveModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValveModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device_status = null,
    Object? mobile_status = null,
  }) {
    return _then(_$ValveModelImpl(
      device_status: null == device_status
          ? _value.device_status
          : device_status // ignore: cast_nullable_to_non_nullable
              as int,
      mobile_status: null == mobile_status
          ? _value.mobile_status
          : mobile_status // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ValveModelImpl implements _ValveModel {
  const _$ValveModelImpl({this.device_status = 0, this.mobile_status = 0});

  factory _$ValveModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ValveModelImplFromJson(json);

  @override
  @JsonKey()
  final int device_status;
  @override
  @JsonKey()
  final int mobile_status;

  @override
  String toString() {
    return 'ValveModel(device_status: $device_status, mobile_status: $mobile_status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValveModelImpl &&
            (identical(other.device_status, device_status) ||
                other.device_status == device_status) &&
            (identical(other.mobile_status, mobile_status) ||
                other.mobile_status == mobile_status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, device_status, mobile_status);

  /// Create a copy of ValveModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValveModelImplCopyWith<_$ValveModelImpl> get copyWith =>
      __$$ValveModelImplCopyWithImpl<_$ValveModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ValveModelImplToJson(
      this,
    );
  }
}

abstract class _ValveModel implements ValveModel {
  const factory _ValveModel(
      {final int device_status, final int mobile_status}) = _$ValveModelImpl;

  factory _ValveModel.fromJson(Map<String, dynamic> json) =
      _$ValveModelImpl.fromJson;

  @override
  int get device_status;
  @override
  int get mobile_status;

  /// Create a copy of ValveModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValveModelImplCopyWith<_$ValveModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
