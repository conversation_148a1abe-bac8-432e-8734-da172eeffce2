
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../utils/app_colors.dart';
import '../../utils/string_validators.dart';
import '../controller/login_state.dart';
import '../provider/login_provider.dart';

class FormSectionForLogin extends ConsumerStatefulWidget {
  const FormSectionForLogin({super.key});

  @override
  ConsumerState<FormSectionForLogin> createState() =>
      _FormSectionForLoginState();
}

class _FormSectionForLoginState extends ConsumerState<FormSectionForLogin> {
  final _loginFormKey = GlobalKey<FormState>();
  final _node = FocusScopeNode();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  String get email => _emailController.text;

  String get password => _passwordController.text;

  // local variable used to apply AutovalidateMode.onUserInteraction and show
  // error hints only when the form has been submitted
  // For more details on how this is implemented, see:
  // https://codewithandrea.com/articles/flutter-text-field-form-validation/
  var _submitted = false;

  Future<void> _submit(LoginState state, WidgetRef ref) async {
    setState(() => _submitted = true);
    // only submit the form if validation passes
    if (_loginFormKey.currentState!.validate()) {
      final controller = ref.read(loginControllerProvider.notifier);
      controller.sendLoadingState();
      String token = "";
      if(!kIsWeb){
        token = await ref.read(fcmServiceProvider).getToken() ?? '';
      }
      final success = await controller.submit(email, password,token);
      if (success) {
        final userRole = ref.read(sharedPreferencesServiceProvider).getUserRole();
        if(context.mounted){
          if(userRole<2){
            EasyLoading.showError("You aren't allowed to login. Please contact admin.");
          } else if(userRole>=3){
            context.go('/home');
          }else{
            context.go('/password');
          }
        }
      }
    }
  }

  void _emailEditingComplete(LoginState state) {
    if (state.canSubmitEmail(email)) {
      _node.nextFocus();
    }
  }

  void _passwordEditingComplete(LoginState state) {
    if (!state.canSubmitEmail(email)) {
      _node.previousFocus();
      return;
    }
    //_submit(state);
  }

  @override
  void initState() {
    super.initState();
  }


  @override
  void dispose() {
    // * TextEditingControllers should be always disposed
    _node.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue>(
      loginControllerProvider.select((state) => state.value),
      (_, state) {

      },
    );
    ref.listen(userCallbackProvider, (previous, next) {
      _emailController.text = (next).email!;
      _passwordController.text = (next).password!;
    });

    final state = ref.watch(loginControllerProvider);
    return Padding(
      padding: const EdgeInsets.all(Sizes.p8),
      child: FocusScope(
        node: _node,
        child: Form(
          key: _loginFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // email field
              CustomTextFormField(
                name: "email",
                enabled: !state.isLoading,
                controller: _emailController,
                hintText: "Email",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.emailAddress,
                prefixIcon: const Icon(
                  Icons.email_outlined,
                  color: Colors.grey,
                ),
                validator: (email) =>
                    !_submitted ? null : state.emailErrorText(email ?? ''),
                onEditingComplete: () => _emailEditingComplete(state),
                textInputFormatter: <TextInputFormatter>[
                  ValidatorInputFormatter(
                      editingValidator: EmailEditingRegexValidator()),
                ],
              ),
              gapH16,
              // Password field
              Consumer(
                builder: (BuildContext context, WidgetRef ref, Widget? child) {
                  final obscured = ref.watch(loginEyeToggleProvider);
                  return CustomTextFormField(
                    controller: _passwordController,
                    name: "password",
                    enabled: !state.isLoading,
                    hintText: "Password",
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    obscureText: obscured,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    onEditingComplete: () => _passwordEditingComplete(state),
                    validator: (password) => !_submitted
                        ? null
                        : state.passwordErrorText(password ?? ''),
                    inputDecoration: defaultInputDecoration(context).copyWith(
                      hintText: "Password",
                      hintStyle: context.hintText,
                      enabled: !state.isLoading,
                      prefixIcon: const Icon(
                        FontAwesomeIcons.key,
                        color: Colors.grey,
                      ),
                      suffixIcon: Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: IconButton(
                          onPressed: () => ref
                              .read(loginEyeToggleProvider.notifier)
                              .state = !obscured,
                          // onPressed: () {},
                          icon: Icon(
                            obscured
                                ? Icons.visibility_off_outlined
                                : Icons.visibility,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
              gapH24,
              PrimaryButton(
                isLoading: state.isLoading,
                height: Sizes.p60,
                text: "Login",
                onPressed: () async {
                  _submit(state, ref);
                  /*await ref.read(sharedPreferencesServiceProvider).setLogin();
                  context.router.replaceAll([DashBoardRouter()]);*/
                },
              ),
              gapH24,

            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForgetPassword(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.bottomRight,
            child: TextButton(
              child: Text(
                "Forget Password",
                style: context.displayText.copyWith(
                  decoration: TextDecoration.underline,
                ),
              ),
              onPressed: () async {

              },
            ),
          ),
        ],
      ),
    );
  }
}
