

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/chanakhel/system_one_page.dart';
import 'package:si/division1/homepage/chanakhel/system_two_page.dart';
import 'package:si/division1/homepage/pandubazaar/mobile_pandubazar_page.dart';
import 'package:si/division1/homepage/pandubazaar/pandubazar_site_one_page.dart';
import 'package:si/division1/homepage/pandubazaar/web/web_pandubazar_page.dart';
import 'package:si/division1/homepage/simkhola/simkhola_site_one_page.dart';
import 'package:si/division1/homepage/simkhola/simkhola_site_two_page.dart';
import 'package:si/utils/app_colors.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../services/shared_preferences_service.dart';
import '../../provider/division_provider.dart';
import 'pandubazar_site_two_page.dart';

class PandubazarPage extends HookConsumerWidget{

  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    //final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    final width = MediaQuery.of(context).size.width;

    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    return siteSetting.when(
        success: (setting, message) {
          if(width > 600){
            return BaseScaffold(
                showLeftIcon: false,
                showAppBar: false,
                appbarText: setting?.name ?? "",
                showAction: prefs.getUserRole() >3 ? true : false,
                onActionClick:() async {
                  final setting = await context.push("/location");
                },
                child: WebPandubazarPage(setting!));
          }else{
            return MobilePandubazarPage(setting!);
          }

        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }


}