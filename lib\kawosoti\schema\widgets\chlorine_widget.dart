

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../constants/assets_manager.dart';

class ChlorineWidget extends StatelessWidget{

  const ChlorineWidget({
    required this.name,
    required this.sensorValue,
    this.height = 180,
    this.width = 120,
    Key? key,
  }) : super(key: key);

  final String name;
  final String sensorValue;
  final double height;
  final double width;

  @override
  Widget build(BuildContext context) {
    return   Container(
        height: height,
        width: width,
        margin: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.black)),
        child: Column(
          children: [
            Image.asset(
              AppAssets.instance.chlorine,
              height: 100,
              width: 100,
            ),
            Divider(
              height: 3,
              color: Colors.black,
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))),
                child: Center(
                    child: Text("0.34",
                        style: TextStyle(fontSize: 14, color: Colors.white))),
              ),
            )
          ],
        ));

  }

}