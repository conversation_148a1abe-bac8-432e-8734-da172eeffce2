import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:si/constants/app_sizes.dart';
import 'package:si/kawosoti/charts/borewell_chart.dart';
import 'package:si/kawosoti/charts/chlorine_chart.dart';
import 'package:si/kawosoti/charts/oh_chart.dart';
import 'package:si/kawosoti/charts/ph_chart.dart';
import 'package:si/kawosoti/schema/widgets/date_forward_backward.dart';

import '../schema/site_one/provider/chart_one_controller.dart';

class ChartSiteEightPage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chartController = ref.watch(chartOneProvider("site8"));
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            DateForwardBackward(onDateChange: (date) {
              ref.read(chartOneProvider("site5").notifier).getData(date);
            }),
            chartController.when(
              success: (data, message) {
                return Consumer(
                    builder: (context, ref, child) {

                      return Column(
                        children: [
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('PH',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: PhChart(chartModel: data!,))
                          ),
                          gapH16,
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Chlorine',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: ChlorineChart(chartModel: data,))
                          ),
                          gapH16,
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Borewell',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: BorewellChart(chartModel: data,))
                          ),
                          gapH16,

                        ],
                      );
                    }
                );
              },

              error: (er) {
                return Container();
              },
              unInitialized: () {
                return Container();
              },
              loading: () {
                return CircularProgressIndicator();
              },
            )
          ],
        ),
      ),
    );
  }
}


