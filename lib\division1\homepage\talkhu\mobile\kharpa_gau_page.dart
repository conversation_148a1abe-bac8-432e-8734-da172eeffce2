


import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../../constants/app_sizes.dart';
import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';
import '../../new_common_motor_unit.dart';
import '../../new_motor_unit.dart';
import '../../new_tank_unit.dart';
import '../../widgets/distribution_widget.dart';

class KharpaGauPage extends HookConsumerWidget{

  const KharpaGauPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {



    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                height: 40,
                width: double.infinity,
                color: Colors.blueAccent,
                child: Center(child: const Text("डाडा गाउ", style: TextStyle(color: Colors.white),))
              ),
              Padding(
                padding: const EdgeInsets.only(right: 64.0),
                child: Container(
                  height: 25,
                  width:20,
                  color: Colors.blueAccent,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  NewTankUnit(2, "11", setting.tank11!, 160, 130),
                  DistributionWidget(name: "स्कुल माथि", height: 30, width: 80)
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(right: 64.0),
                child: Container(
                  height: 25,
                  width:20,
                  color: Colors.blueAccent,
                ),
              ),
              Container(
                height: 20,
                width:double.infinity,
                color: Colors.blueAccent,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: 25,
                        width:20,
                        color: Colors.blueAccent,
                      ),
                      NewMotorUnit(
                        motorId: "21",
                        motorModel: MotorModel(),
                        motorSettingModel: setting.motor21,
                      ),
                    ],
                  ),
                  Container(
                    height: 20,
                    width: 60,
                    color: Colors.blueAccent,
                  ),
                  NewTankUnit(2, "31", setting!.tank31!, 170, 160),

                  /*Column(
                    children: [
                      Container(
                        height: 30,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                      NewCommonValveUnit(
                        valveModel: MotorModel(),
                        valveId: "31",
                        ratio: 4,
                        valveSettingModel: setting.valve31!,
                      ),
                      Container(
                        height: 30,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                      NewMotorUnit(
                        motorId: "21",
                        motorModel: MotorModel(),
                        motorSettingModel: setting.motor21,
                      ),
                      Container(
                        height: 20,
                        width: 20,
                        color: Colors.blueAccent,
                      ),
                    ],
                  )*/
                ],
              ),
              gapH24

            ],
          ),
        )
      ],
    );
  }

}