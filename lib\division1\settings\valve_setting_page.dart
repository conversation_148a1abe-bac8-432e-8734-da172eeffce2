




import 'dart:developer' as logging;
import 'dart:math';

import 'package:day_picker/day_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/homepage/motor_list_page.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/settings/label_text.dart';
import 'package:si/division1/settings/schedule_setting_page.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../constants/assets_manager.dart';
import '../../provider/auth_provider.dart';
import '../homepage/tank_list_page.dart';
import '../model/motor_setting_model.dart';
import 'add_site_page.dart';


enum Options { Normal, Automatic, Scheduler }

class ValveSettingPage extends HookConsumerWidget {
  ValveSettingPage( this.valveId);

  final String? valveId;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  Future<void> _handleValveSetting(
      BuildContext context,
      WidgetRef ref, {
        required TextEditingController valveNameController,
        required bool isActive,
        required int mode,
        String? hrs,
        String? mins,
        String? days,
      }) async {


    final repo = ref.read(divisionRepositoryProvider);
    final rtdbrepo = ref.read(divisionRtdbServiceProvider);
    final siteId = ref.read(sharedPreferencesServiceProvider).getSiteId();
    if (_formKey.currentState != null) {
      if (_formKey.currentState!.validate()) {
        // final email = emailController.text;
        // final password = passwordController.text;
        // Logger().d('Email : $email \nPassword: $password');
        try {
          EasyLoading.show(status: 'Updating..');
          await repo.setValveSetting(siteId: siteId,
            valveId:'valve'+valveId!,
            valveName:valveNameController.text.trim().toString(),
            mode: mode,
            hrs: hrs,
            mins: mins,
            days: days,
          );
          await rtdbrepo.updateValveParameter(siteId:siteId,valveId:valveId!,
            name: valveNameController.text.trim().toString(),
            mode: mode,
            hrs: hrs,
            mins: mins,
            days: days,);
          final realSetting = await repo.getSiteSetting(siteId);
          await ref.read(localStorageProvider).setSettings(realSetting!);
          Navigator.pop(context,'refresh');

        } catch (e) {
          EasyLoading.showError('Error');
          //Logger().e('create user from email', e);
        }
        FocusScope.of(context).unfocus();
      } else {
        debugPrint("login validation failed");
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final valveNameController = useTextEditingController();

    final motorSettingModel = getValveModel(valveId ?? "10", ref.read(localStorageProvider).getSettings());

    final mode = useState(Options.Normal);
    final hrArray = useState<List<int>>([]);
    final minArray = useState<List<int>>([]);
    final dayArray = useState<List<int>>([]);

    logging.log(motorSettingModel.toString());

    useEffect(() {
      // print('SessionDEt : ${sessionModel.id}');
      valveNameController.text = motorSettingModel.name.toString();
      mode.value = motorSettingModel.mode==3 ? Options.Scheduler
          : motorSettingModel.mode == 2 ?  Options.Automatic : Options.Normal;
      if(mode.value == Options.Scheduler){
        hrArray.value = motorSettingModel.hrs.split(',').map((str) => int.parse(str)).toList();;
        minArray.value = motorSettingModel.mins.split(',').map((str) => int.parse(str)).toList();
      }
      // logging.log(maxCurrentController.value.toString());
      return null;
    }, const []);

    return BaseScaffold(
      showAppBar: true,
      appbarText: "Valve Setting ("+(valveId ?? "10")+")",
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildTextFormFieldSection(context, ref,
                      valveNameController,
                     ),
                    const Align(
                        alignment: Alignment.topLeft,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text("Valve Mode", style: TextStyle(fontSize: 18, )),
                        )),
                    ...Options.values.map((option) {
                      return RadioListTile<Options>(
                        title: Text(option.toString().split('.').last), // Display enum value name
                        value: option,
                        groupValue: mode.value,
                        onChanged: (value) {
                          mode.value = value!;
                          logging.log(mode.value.toString());
                        },
                      );
                    }).toList(),
                    if(mode.value==Options.Scheduler && hrArray.value.isNotEmpty)
                      const Align(
                          alignment: Alignment.topLeft,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            child: Text("Saved time schedule", style: TextStyle(fontSize: 14, )),
                          )),
                    if(mode.value==Options.Scheduler && hrArray.value.isNotEmpty)
                      for(var i =0 ; i< hrArray.value.length ; i+=2)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Container(
                            height: 40,
                            child: Row(
                              children: [
                                GestureDetector(
                                    onTap: () async {

                                    },
                                    child: Text("Start Time")),
                                Container(width: 16,),
                                Text("${getHr(hrArray.value[i], minArray.value[i]==1 ? 15 : minArray.value[i] == 2 ? 30 : minArray.value[i]==3 ? 45 : 0)}"),
                                Expanded(
                                  child: Container(),
                                ),
                                GestureDetector(
                                    onTap: () async {

                                    },
                                    child: Text("Stop Time")),
                                Container(width: 16,),
                                Text("${getHr(hrArray.value[i+1], minArray.value[i+1]==1 ? 15 : minArray.value[i+1] == 2 ? 30 : minArray.value[i+1]==3 ? 45 : 0)}"),

                              ],
                            ),
                          ),
                        ),
                    mode.value == Options.Scheduler ?
                    ValveScheduleSettingPage(valveId!,
                            (hrArray, minArray, dayArray) {
                          _handleValveSetting(context,ref,
                              valveNameController: valveNameController,
                              hrs: hrArray,
                              mins: minArray,
                              days: dayArray,
                              mode: mode.value == Options.Automatic ? 2 : mode.value == Options.Scheduler ? 3 : 1,
                              isActive: true);

                        }) : Container(),


                    /* HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final levelSwitch = useState<bool>(.value);
                      return ListTile(
                        title: const Text('Level sensor'),
                        trailing: CupertinoSwitch(
                          value: levelSwitch.value,
                          onChanged: (bool value) {
                            levelSwitch.value = value;
                            sensorEnable.value = levelSwitch.value;
                          },
                        ),
                        onTap: () {
                          levelSwitch.value = !levelSwitch.value;
                          sensorEnable.value = levelSwitch.value;
                        },
                      );
                    }),
                    HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final activeSwitch = useState<bool>(activeEnable.value);
                      return ListTile(
                        title: const Text('Active'),
                        trailing: CupertinoSwitch(
                          value: activeSwitch.value,
                          onChanged: (bool value) {
                            activeSwitch.value = value;
                            activeEnable.value = activeSwitch.value;
                          },
                        ),
                        onTap: () {
                          activeSwitch.value = !activeSwitch.value;
                          activeEnable.value = activeSwitch.value;
                        },
                      );
                    }),*/
                    if(mode.value != Options.Scheduler)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24.0, vertical: 24),
                        child: PrimaryButton(
                          text: 'Save',
                          height: 54,
                          onPressed: () async {
                            _handleValveSetting(context,ref,
                                valveNameController: valveNameController,
                                hrs: '',
                                mins:'',
                                mode: mode.value == Options.Automatic ? 2 : mode.value == Options.Scheduler ? 3 : 1,
                                isActive: true);
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

        ],
      ),

    );
  }

  Widget _buildTextFormFieldSection(
      BuildContext context,
      WidgetRef ref,
      TextEditingController valveNameController,
      ) {
    // final FocusNode? pwdFocusNode = FocusNode();

    // EdgeFunctionController edgeFunctionController = EdgeFunctionController();

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          gapH16,
          LabelText(label: "Enter Valve Name"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "valveName",
                controller: valveNameController,
                hintText: "Valve Name",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),


        ],
      ),
    );
  }
}

