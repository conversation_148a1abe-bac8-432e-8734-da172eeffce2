

import 'dart:developer';

import 'package:day_picker/day_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:interval_time_picker/interval_time_picker.dart';
import 'package:interval_time_picker/models/visible_step.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/primary_button.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../constants/app_sizes.dart';

class ScheduleSettingPage extends HookConsumerWidget{

  const ScheduleSettingPage(this.motorId,this.onClick);

  final String motorId;

  final Function(String, String) onClick;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final frequency = useState<int>(1);
    final List<int> items = [1, 2, 3, 4, 5, 6, 7, 8];
    final startTime = useState<List<TimeOfDay?>>([null]);
    final  stopTime = useState<List<TimeOfDay?>>([null]);
    final repo = ref.read(divisionRepositoryProvider);
    final pref = ref.read(sharedPreferencesServiceProvider);
    final setting = ref.read(localStorageProvider).getSettings();

    final refresh = useState<bool>(false);
    void adjustArraySize() {
      // Adjust `startTime` array size
      startTime.value = List<TimeOfDay?>.filled(frequency.value, null, growable: false);
      // Adjust `stopTime` array size
      stopTime.value = List<TimeOfDay?>.filled(frequency.value, null, growable: false);
    }


    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text("Total number of cycles : ", style: TextStyle()),
              Expanded(child: Container(),),
              DropdownButton(
                value: frequency.value,
                  items: items.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(item.toString(), style: TextStyle(color: Colors.black),),
                );
              }).toList(), onChanged: (item){
                frequency.value = item!;
                adjustArraySize();
              })
            ],
          ),
        ),
        for(var i=0;i<frequency.value ;i++ )
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Container(
              height: 40,
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () async {
                        final time = await showIntervalTimePicker(
                          context: context,
                          initialTime: TimeOfDay(hour: 0, minute: 0),
                          interval: 15,
                          visibleStep: VisibleStep.fifteenths,
                        );
                        if(time!=null){
                          if(i>0){
                            if(stopTime.value[i-1]==null){
                              startTime.value[i] = null;
                              refresh.value = !refresh.value;
                              EasyLoading.showError("Please enter previous cycle.");
                              return;
                            }

                            if((time.hour*60+time.minute)<(stopTime.value[i-1]!.hour*60+stopTime.value[i-1]!.minute)){
                              startTime.value[i] = null;
                              refresh.value = !refresh.value;
                              EasyLoading.showError("Start time should be greater than previous stop time.");
                              return;
                            }
                          }
                          startTime.value[i] = time;
                          refresh.value = !refresh.value;

                        }
                    },
                      child: Text("Start Time")),
                  Container(width: 16,),
                  Text(startTime.value[i]!=null ? "${getHr(startTime.value[i]!.hour, startTime.value[i]!.minute)}"  : "00:00"),
                  Expanded(
                    child: Container(),
                  ),
                  GestureDetector(
                    onTap: () async {
                        final time = await showIntervalTimePicker(
                          context: context,
                          initialTime: TimeOfDay(hour: 0, minute: 0),
                          interval: 15,
                          visibleStep: VisibleStep.fifteenths,
                        );
                        if(time!=null){
                          if(startTime.value[i]==null){
                            EasyLoading.showError("Please enter start time at first.");
                            stopTime.value[i] = null;
                            return;

                          }
                          if((time.hour*60+time.minute)<(startTime.value[i]!.hour*60+startTime.value[i]!.minute)){
                            stopTime.value[i] = null;
                            refresh.value = !refresh.value;
                            EasyLoading.showError("Start time should be greater than previous stop time.");
                            return;
                          }
                          stopTime.value[i] = time;
                          refresh.value = !refresh.value;
                        }
                    },
                      child: Text("Stop Time")),
                  Container(width: 16,),
                  Text(stopTime.value[i]!=null ? "${getHr(stopTime.value[i]!.hour, stopTime.value[i]!.minute)}"  : "00:00"),

                ],
              ),
            ),
          ),
        gapH24,
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: PrimaryButton(text: "Save",
          onPressed: () async {
            if(!startTime.value.contains(null) && !stopTime.value.contains(null)){
              //EasyLoading.show();
              final List<int> hrArray = [];
              final List<int> minArray = [];
              for(var i = 0 ; i<startTime.value.length ; i++){
                hrArray.add(startTime.value[i]!.hour);
                hrArray.add(stopTime.value[i]!.hour);
                minArray.add(startTime.value[i]!.minute==15 ? 1 :
                startTime.value[i]!.minute == 30 ? 2 : startTime.value[i]!.minute == 45 ? 3 : 0);
                minArray.add(stopTime.value[i]!.minute==15 ? 1 :
                stopTime.value[i]!.minute == 30 ? 2 : stopTime.value[i]!.minute == 45 ? 3 : 0);
              }
              onClick(hrArray.join(','), minArray.join(','));
            /*  await repo.updateScheduleSetting(
                  pref.getSiteId(), "motor"+motorId, hrArray.join(','),
                  minArray.join(','),setting);
              await ref.read(divisionRtdbServiceProvider)
                  .updateMotorScheduler(siteId: pref.getSiteId(), motorId: motorId,
                  hrs: hrArray.join(','), mins: minArray.join(','), );*/
             // EasyLoading.dismiss();
            }else{
              EasyLoading.showError("Please enter all data");
            }
          },),
        )
      ],
    );
  }
}


class ValveScheduleSettingPage extends HookConsumerWidget{

  const ValveScheduleSettingPage(this.valveId,this.onClick);

  final String valveId;

  final Function(String, String, String) onClick;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final frequency = useState<int>(1);
    final List<int> items = [1, 2, 3, 4, 5, 6, 7, 8];
    final startTime = useState<List<TimeOfDay?>>([null]);
    final  stopTime = useState<List<TimeOfDay?>>([null]);
    final  days = useState<List<int?>>([null]);


    final refresh = useState<bool>(false);
    void adjustArraySize() {
      // Adjust `startTime` array size
      startTime.value = List<TimeOfDay?>.filled(frequency.value, null, growable: false);
      // Adjust `stopTime` array size
      stopTime.value = List<TimeOfDay?>.filled(frequency.value, null, growable: false);
    }


    final List<DayInWeek> _days = [
      DayInWeek("Sun", dayKey: "1"),
      DayInWeek("Mon", dayKey: "2"),
      DayInWeek("Tue", dayKey: "3"),
      DayInWeek("Wed", dayKey: "4"),
      DayInWeek("Thur", dayKey: "5"),
      DayInWeek("Fri", dayKey: "6"),
      DayInWeek("Sat", dayKey: "7",),
    ];



    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text("Total number of cycles : ", style: TextStyle()),
              Expanded(child: Container(),),
              DropdownButton(
                  value: frequency.value,
                  items: items.map((item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.toString(), style: TextStyle(color: Colors.black),),
                    );
                  }).toList(), onChanged: (item){
                frequency.value = item!;
                adjustArraySize();
              })
            ],
          ),
        ),
        for(var i=0;i<frequency.value ;i++ )
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Container(
              height: 40,
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () async {
                        final time = await showIntervalTimePicker(
                          context: context,
                          initialTime: TimeOfDay(hour: 0, minute: 0),
                          interval: 15,
                          visibleStep: VisibleStep.fifteenths,
                        );
                        print(time.toString());
                        if(time!=null){
                          if(i>0){
                            if(stopTime.value[i-1]==null){
                              startTime.value[i] = null;
                              refresh.value = !refresh.value;
                              EasyLoading.showError("Please enter previous cycle.");
                              return;
                            }

                            if((time.hour*60+time.minute)<(stopTime.value[i-1]!.hour*60+stopTime.value[i-1]!.minute)){
                              startTime.value[i] = null;
                              refresh.value = !refresh.value;
                              EasyLoading.showError("Start time should be greater than previous stop time.");
                              return;
                            }
                          }
                          startTime.value[i] = time;
                          refresh.value = !refresh.value;

                        }
                      },
                      child: Text("Start Time")),
                  Container(width: 16,),
                  Text(startTime.value[i]!=null ? "${getHr(startTime.value[i]!.hour, startTime.value[i]!.minute)}"  : "00:00"),
                  Expanded(
                    child: Container(),
                  ),
                  GestureDetector(
                      onTap: () async {
                        final time = await showIntervalTimePicker(
                          context: context,
                          initialTime: TimeOfDay(hour: 0, minute: 0),
                          interval: 15,
                          visibleStep: VisibleStep.fifteenths,
                        );
                        if(time!=null){
                          if(startTime.value[i]==null){
                            EasyLoading.showError("Please enter start time at first.");
                            stopTime.value[i] = null;
                            return;

                          }
                          if((time.hour*60+time.minute)<(startTime.value[i]!.hour*60+startTime.value[i]!.minute)){
                            stopTime.value[i] = null;
                            refresh.value = !refresh.value;
                            EasyLoading.showError("Start time should be greater than previous stop time.");
                            return;
                          }
                          stopTime.value[i] = time;
                          refresh.value = !refresh.value;
                        }
                      },
                      child: Text("Stop Time")),
                  Container(width: 16,),
                  Text(stopTime.value[i]!=null ? "${getHr(stopTime.value[i]!.hour, stopTime.value[i]!.minute)}"  : "00:00"),

                ],
              ),
            ),
          ),
        gapH24,
        /*Padding(
          padding: const EdgeInsets.all(8.0),
          child: SelectWeekDays(
            width: MediaQuery.of(context).size.width,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            days: _days,
            border: false,
            boxDecoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30.0),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                // 10% of the width, so there are ten blinds.
                colors: [
                  const Color(0xFFE55CE4),
                  const Color(0xFFBB75FB)
                ], // whitish to gray
                tileMode:
                TileMode.repeated, // repeats the gradient over the canvas
              ),
            ),
            onSelect: (values) {
              days.value = values.map(int.parse).toList();
            },
          ),
        ),*/
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: PrimaryButton(text: "Save",
            onPressed: () async {
              if(!startTime.value.contains(null) && !stopTime.value.contains(null)){
                //EasyLoading.show();
                final List<int> hrArray = [];
                final List<int> minArray = [];
                for(var i = 0 ; i<startTime.value.length ; i++){
                  hrArray.add(startTime.value[i]!.hour);
                  hrArray.add(stopTime.value[i]!.hour);
                  minArray.add(startTime.value[i]!.minute==15 ? 1 :
                  startTime.value[i]!.minute == 30 ? 2 : startTime.value[i]!.minute == 45 ? 3 : 0);
                  minArray.add(stopTime.value[i]!.minute==15 ? 1 :
                  stopTime.value[i]!.minute == 30 ? 2 : stopTime.value[i]!.minute == 45 ? 3 : 0);
                }
                onClick(hrArray.join(','), minArray.join(','),days.value.join(','));
                /*  await repo.updateScheduleSetting(
                  pref.getSiteId(), "motor"+motorId, hrArray.join(','),
                  minArray.join(','),setting);
              await ref.read(divisionRtdbServiceProvider)
                  .updateMotorScheduler(siteId: pref.getSiteId(), motorId: motorId,
                  hrs: hrArray.join(','), mins: minArray.join(','), );*/
                // EasyLoading.dismiss();
              }else{
                EasyLoading.showError("Please enter all data");
              }
            },),
        )
      ],
    );
  }
}


String getHr(int hr, int min){
  if(hr<10){
    if(min<10){
      return "0${hr}:0${min} am";
    }else{
      return "0${hr}:${min} am";
    }
  }else if(hr>=10 && hr < 12){
    if(min<10){
      return "${hr}:0${min} am";
    }else{
      return "${hr}:${min} am";
    }
  }else{
    if(hr>12){
      if(hr-12<10){
        if(min<10){
          return "0${hr-12}:0${min} pm";
        }else{
          return "0${hr-12}:${min} pm";
        }
      }else{
        if(min<10){
          return "${hr}:0${min} pm";
        }else{
          return "${hr}:${min} pm";
        }
      }
    }else{
      if(min<10){
        return "${hr}:0${min} pm";
      }else{
        return "${hr}:${min} pm";
      }
    }

  }
}