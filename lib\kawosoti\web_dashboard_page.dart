

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/widgets/side_menu.dart';

import '../dashboard/widgets/animated_branch_container.dart';

class WebDashBoardPage extends HookConsumerWidget{

  /// Creates a [BookstoreScaffold].
  const WebDashBoardPage({
    required this.navigationShell,
    required this.children,
    Key? key,
  }) : super(key: key ?? const ValueKey<String>('NavBar'));

  /// The navigation shell and container for the branch Navigators.
  final StatefulNavigationShell navigationShell;

  /// The children (branch Navigators) to display in a custom container
  /// ([AnimatedBranchContainer]).
  final List<Widget> children;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final width = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: width < 850 ? AppBar(
        centerTitle: true,
        iconTheme: IconThemeData(color: Colors.white),
        title: const Text(
          '',
        ),
        backgroundColor: Colors.black,
      ) : null,
      drawer: SideMenu(navigationShell: navigationShell,),
      body: Row(
        children: [
          width < 850 ? SizedBox.shrink() :  SideMenu(navigationShell: navigationShell,),
          Expanded(
            child: AnimatedBranchContainer(
              currentIndex: navigationShell.currentIndex,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

}