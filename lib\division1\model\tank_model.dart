



import '../../utils/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'tank_model.freezed.dart';
part 'tank_model.g.dart';

@freezed
class TankModel with _$TankModel {
  const factory TankModel({
    @Default(0) int? float_status,
    @Default(1) int  level,
    @Default(450) int actual_height
  }) = _TankModel;

  factory TankModel.fromJson(Map<String, dynamic> json) => _$TankModelFromJson(json);
}

@freezed
class BwModel with _$BwModel {
  const factory BwModel({
    @Default(1) double  level,
    @Default(0) int time,
  }) = _BwModel;

  factory BwModel.fromJson(Map<String, dynamic> json) => _$BwModelFromJson(json);
}



@freezed
class OutputInputVolumeModel with _$OutputInputVolumeModel {
  const factory OutputInputVolumeModel({
    @Default(0) int mins,
    @Default(0) int random,
    @Default(0) int time,
    @Default(0) int status,
    @Default(0) int volume,
    @Default(0.0) double rate,
  }) = _OutputInputVolumeModel;

  factory OutputInputVolumeModel.fromJson(Map<String, dynamic> json) => _$OutputInputVolumeModelFromJson(json);
}