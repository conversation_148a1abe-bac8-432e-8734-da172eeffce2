import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';


final sharedPreferencesServiceProvider =
    Provider<SharedPreferencesService>((ref) => throw UnimplementedError());

class SharedPreferencesService {
  SharedPreferencesService(this.sharedPreferences);
  final SharedPreferences sharedPreferences;

  static const login = 'login';
  static const fullName = "fullName";
  static const phoneNumber = "phoneNumber";
  static const email = "email";
  static const userRole = "role";
  static const hasSiteDefine = "hasSite";
  static const siteId = "siteId";
  static const hasValve = "hasValve";
  static const motorNumber = "motorNumber";
  static const tankNumber = "tankNumber";
  static const siteName = "siteName";
  static const password = "password";
  static const userId = "userId";
  static const settingVersion = 'settingVersion';
  static const appVersion = 'appVersion';
  static const version = 'version';
  static const themeMode  = "themeMode";


  Future<void> setLogin() async {
    await sharedPreferences.setBool(login, true);
  }

  Future<void> setThemeMode(int themeModel) async {
    await sharedPreferences.setInt(themeMode, themeModel);
  }

  Future<void> setFirstTime() async {
    await sharedPreferences.setBool(login, true);
  }

  Future<void> setSite({bool hasSite = true}) async {
    await sharedPreferences.setBool(hasSiteDefine, hasSite);
  }

  Future<void> setMotorNumber(int number) async {
    await sharedPreferences.setInt(motorNumber, number);
  }

  Future<void> setTankNumber(int number) async {
    await sharedPreferences.setInt(tankNumber, number);
  }

  Future<void> setTankName(String key,String name) async {
    await sharedPreferences.setString(key, name);
  }


  Future<void> setSiteId(String site) async {
    await sharedPreferences.setString(siteId, site);
  }

  Future<void> setHasValve(bool isvalvePresent) async {
    await sharedPreferences.setBool(hasValve, isvalvePresent);
  }

  Future<void> setSiteNae(String site) async {
    await sharedPreferences.setString(siteName, site);
  }

  String getTankName(String key)  {
     return sharedPreferences.getString(key) ?? "";
  }


  Future<void> setEmail(String emailAddress) async {
    await sharedPreferences.setString(email, emailAddress);
  }

  Future<void> setFullName(String name) async {
    await sharedPreferences.setString(fullName, name);
  }

  Future<void> setRoleType(int role) async {
    await sharedPreferences.setInt(userRole, role);
  }

  Future<void> setPhoneNumber(String phone) async {
    await sharedPreferences.setString(phoneNumber, phone);
  }


  Future<void> setUserId(int userIds) async {
    await sharedPreferences.setInt(userId, userIds);
  }



  Future<void> setLogout() async {
    await sharedPreferences.setBool(login, false);

  }


  Future<void> clearPreferences() async{
    await sharedPreferences.clear();
  }


  Future<void> setTankLevel({required String tankId,required int level}) async {
    try {
      await sharedPreferences.setInt(tankId, level);
    } catch (err) {
    }
  }

  Future<void> setActualHeight({required String tankId,required int level}) async {
    try {
      await sharedPreferences.setInt(tankId+'height', level);
    } catch (err) {
    }
  }

  int getTankLevel(String tankId)  {
    try {
      final int? level = sharedPreferences.getInt(tankId);
      return level ?? 100;
    } catch (err) {
      return 100;
    }
  }

  int getActualHeight(String tankId)  {
    try {
      final int? level = sharedPreferences.getInt(tankId+'height');
      return level ?? 450;
    } catch (err) {
      return 450;
    }
  }




  int getUserId() => sharedPreferences.getInt(userId) ?? 0;

  bool isValvePresent() => sharedPreferences.getBool(hasValve) ?? false;

  int getTankNumber() => sharedPreferences.getInt(tankNumber) ?? 1;

  int getMotorNumber() => sharedPreferences.getInt(motorNumber) ?? 1;

  int getUserRole() => sharedPreferences.getInt(userRole) ?? 0;

  bool hasSite() => sharedPreferences.getBool(hasSiteDefine) ?? false;

  String getSiteId() => sharedPreferences.getString(siteId) ?? "";

  String getSiteName() => sharedPreferences.getString(siteName) ?? "";

  String getSettingVersion() => sharedPreferences.getString(settingVersion) ?? '';

  String getFullName() => sharedPreferences.getString(fullName) ?? '';


  String getEmail() => sharedPreferences.getString(email) ?? '';

  String getPhoneNumber() => sharedPreferences.getString(phoneNumber) ?? '';


  int getThemeMode() => sharedPreferences.getInt(themeMode) ?? 1;


  Future<void> logout() async {
    await sharedPreferences.setBool(login, false);
  }

  bool isLogin() => sharedPreferences.getBool(login) ?? false;

}
