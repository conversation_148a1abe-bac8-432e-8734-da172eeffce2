import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../utils/timestamp_converter.dart';

part 'site_model.freezed.dart';
part 'site_model.g.dart';

@freezed
class SiteFireStoreModel with _$SiteFireStoreModel{
  const factory SiteFireStoreModel({
    double? t1,
    double? t2,
    double? t3,
    double? t4,
    double? t5,
    double? cl,
    double? ph,
    double? oh,
    double? bw,
    String? id,
    int? time1,
    int? time2,
    @TimestampNullableConverter() DateTime? timestamp,

  }) = _SiteFireStoreModel;

  factory SiteFireStoreModel.fromJson(Map<String, dynamic> json) => _$SiteFireStoreModelFromJson(json);
}



@freezed
class SiteRtDbModel with _$SiteRtDbModel{
  const factory SiteRtDbModel({
    double? Tub1,
    double? Tub2,
    double? Tub3,
    double? Tub4,
    double? Chlorine,
    double? PH,
    double? OHT,
    double? Borewell,
    int? RMU1_timestamp,
    int? RMU2_timestamp,
    Sensor? sensor,

    @TimestampNullableConverter() DateTime? timestamp,

  }) = _SiteRtDbModel;

  factory SiteRtDbModel.fromJson(Map<String, dynamic> json) => _$SiteRtDbModelFromJson(json);
}

enum Sensor {
  turbidity,
  chlorine,
  ph,
  borewell,
  oht,
}


@freezed
class SiteNameModel with _$SiteNameModel{
  const factory SiteNameModel({
    required String site1,
    required String site2,
    required String site3,
    required String site4,
    required String site5,
    required String site6,
    required String site7,
    required String site8,

  }) = _SiteNameModel;

  factory SiteNameModel.fromJson(Map<String, dynamic> json) => _$SiteNameModelFromJson(json);
}

