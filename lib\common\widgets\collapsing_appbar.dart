import 'package:flutter/material.dart';

class CollapsingAppbar extends StatelessWidget {
  const CollapsingAppbar({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 150,
      floating: false,
      pinned: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      flexibleSpace: const FlexibleSpaceBar(
        centerTitle: false,
        title: Text('Account'),
        titlePadding: EdgeInsets.only(left: 48, bottom: 16),
      ),
    );
  }
}
