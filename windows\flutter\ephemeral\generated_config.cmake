# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\StudioProjects\\sewage-lifting" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\StudioProjects\\sewage-lifting"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\StudioProjects\\sewage-lifting\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\StudioProjects\\sewage-lifting"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\StudioProjects\\sewage-lifting\\lib\\division1\\main_division.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9BUFBfRkxBVk9SPXNhbmRib3g="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\StudioProjects\\sewage-lifting\\.dart_tool\\package_config.json"
  "FLAVOR=sandbox"
)
