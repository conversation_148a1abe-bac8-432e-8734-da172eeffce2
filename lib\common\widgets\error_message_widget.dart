
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/constants/style_manager.dart';



class ErrorMessageWidget extends StatelessWidget {
  const ErrorMessageWidget(this.errorMessage, {this.onRefresh, Key? key}) : super(key: key);
  final String errorMessage;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              onRefresh?.call();
            },
            icon: Icon(
              Icons.refresh_outlined,
              color: context.onBackgroundColor,
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Text(
            errorMessage,
            style: TextStyle(
              color: context.onBackgroundColor,
            ),
          ),
        ],
      ),
    );
  }
}


class CustomEmptyWidget extends StatelessWidget {
  const CustomEmptyWidget({this.message, this.onRefresh, this.heightFactor = 0.32, Key? key}) : super(key: key);
  final String? message;
  final double heightFactor;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: MediaQuery.of(context).size.height * heightFactor),
          Icon(
            Icons.info,
            color: context.onBackgroundColor,
          ),
          const SizedBox(height: 20),
          Text(
            message ?? "No item found",
            style: TextStyle(
              color: context.onBackgroundColor,
            ),
          ),
          /* IconButton(
            onPressed: () {
              onRefresh?.call();
            },
            icon: const Icon(Icons.refresh_outlined, color: Colors.black,),
          ),*/
        ],
      ),
    );
  }
}

