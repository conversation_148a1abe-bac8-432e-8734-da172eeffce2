
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/dashboard/settingcontroller/setting_state.dart';
import 'package:si/repository/dashboard_repository.dart';
import 'package:si/repository/rtdb_repository.dart';

import '../../model/setting_model.dart';

class SettingController extends StateNotifier<SettingState> {
  SettingController({
    required this.repository,
  }) : super(SettingState());
  final RealtimeDatabase repository;

  Future<bool> submit(String site, SettingModel settingModel) async {
    state = state.copyWith(value: const AsyncValue.loading());
    final value = await AsyncValue.guard(() => setSetting(site,settingModel));
    state = state.copyWith(value: value);
    return value.hasError==false;
  }

  Future<bool> sendLoadingState(){
    state = state.copyWith(value: const AsyncValue.loading());
    return Future.value(true);

  }
  Future<bool?> setSetting(String site,SettingModel settingModel) async  {
    final returningvalue =  await repository
        .setSetting(site, settingModel);
    return returningvalue;
  }
}
