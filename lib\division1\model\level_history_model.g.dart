// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'level_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DivisionLevelHistorymodelImpl _$$DivisionLevelHistorymodelImplFromJson(
        Map<String, dynamic> json) =>
    _$DivisionLevelHistorymodelImpl(
      float: (json['float'] as num?)?.toInt() ?? 2,
      level: (json['level'] as num?)?.toInt() ?? 0,
      mD1: (json['mD1'] as num?)?.toInt() ?? 2,
      mO1: (json['mO1'] as num?)?.toInt() ?? 2,
      mD2: (json['mD2'] as num?)?.toInt() ?? 2,
      mO2: (json['mO2'] as num?)?.toInt() ?? 2,
      v1: (json['v1'] as num?)?.toDouble() ?? 0.0,
      v2: (json['v2'] as num?)?.toDouble() ?? 0.0,
      CF1: (json['CF1'] as num?)?.toInt() ?? 0,
      CF2: (json['CF2'] as num?)?.toInt() ?? 0,
      id: json['id'] as String? ?? "",
      tankId: (json['tankId'] as num?)?.toInt() ?? 1,
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$DivisionLevelHistorymodelImplToJson(
        _$DivisionLevelHistorymodelImpl instance) =>
    <String, dynamic>{
      'float': instance.float,
      'level': instance.level,
      'mD1': instance.mD1,
      'mO1': instance.mO1,
      'mD2': instance.mD2,
      'mO2': instance.mO2,
      'v1': instance.v1,
      'v2': instance.v2,
      'CF1': instance.CF1,
      'CF2': instance.CF2,
      'id': instance.id,
      'tankId': instance.tankId,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };

_$TankHistoryModelImpl _$$TankHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TankHistoryModelImpl(
      float: (json['float'] as num?)?.toInt() ?? 2,
      level: (json['level'] as num?)?.toInt() ?? 0,
      tankId: (json['tankId'] as num?)?.toInt() ?? 1,
      rssi: (json['rssi'] as num?)?.toInt() ?? 1,
      time: (json['time'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$TankHistoryModelImplToJson(
        _$TankHistoryModelImpl instance) =>
    <String, dynamic>{
      'float': instance.float,
      'level': instance.level,
      'tankId': instance.tankId,
      'rssi': instance.rssi,
      'time': instance.time,
    };

_$BorewellHistoryModelImpl _$$BorewellHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BorewellHistoryModelImpl(
      siteId: (json['siteId'] as num?)?.toInt() ?? 1,
      level: (json['level'] as num?)?.toDouble() ?? 0.0,
      bwId: (json['bwId'] as num?)?.toInt() ?? 1,
      rssi: (json['rssi'] as num?)?.toInt() ?? 1,
      time: (json['time'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BorewellHistoryModelImplToJson(
        _$BorewellHistoryModelImpl instance) =>
    <String, dynamic>{
      'siteId': instance.siteId,
      'level': instance.level,
      'bwId': instance.bwId,
      'rssi': instance.rssi,
      'time': instance.time,
    };

_$LogsModelImpl _$$LogsModelImplFromJson(Map<String, dynamic> json) =>
    _$LogsModelImpl(
      device_id: (json['device_id'] as num?)?.toInt() ?? 1,
      remark: json['remark'] as String? ?? "",
      name: json['name'] as String? ?? "",
      type: (json['type'] as num?)?.toInt() ?? 1,
      siteId: json['siteId'] as String? ?? "",
      id: json['id'] as String? ?? "",
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$LogsModelImplToJson(_$LogsModelImpl instance) =>
    <String, dynamic>{
      'device_id': instance.device_id,
      'remark': instance.remark,
      'name': instance.name,
      'type': instance.type,
      'siteId': instance.siteId,
      'id': instance.id,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };

_$FlowHistoryModelImpl _$$FlowHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$FlowHistoryModelImpl(
      mins: (json['mins'] as num?)?.toInt() ?? 0,
      volume: (json['volume'] as num?)?.toInt() ?? 0,
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$FlowHistoryModelImplToJson(
        _$FlowHistoryModelImpl instance) =>
    <String, dynamic>{
      'mins': instance.mins,
      'volume': instance.volume,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };

_$SensorHistoryModelImpl _$$SensorHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SensorHistoryModelImpl(
      t1: (json['t1'] as num?)?.toDouble() ?? 0.0,
      t2: (json['t2'] as num?)?.toDouble() ?? 0.0,
      t3: (json['t3'] as num?)?.toDouble() ?? 0.0,
      t4: (json['t4'] as num?)?.toDouble() ?? 0.0,
      cl: (json['cl'] as num?)?.toDouble() ?? 0.0,
      ph: (json['ph'] as num?)?.toDouble() ?? 0.0,
      flow: (json['flow'] as num?)?.toDouble() ?? 0.0,
      cum_flow: (json['cum_flow'] as num?)?.toDouble() ?? 0.0,
      time1: (json['time1'] as num?)?.toInt(),
      time2: (json['time2'] as num?)?.toInt(),
      time3: (json['time3'] as num?)?.toInt(),
      time4: (json['time4'] as num?)?.toInt(),
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$SensorHistoryModelImplToJson(
        _$SensorHistoryModelImpl instance) =>
    <String, dynamic>{
      't1': instance.t1,
      't2': instance.t2,
      't3': instance.t3,
      't4': instance.t4,
      'cl': instance.cl,
      'ph': instance.ph,
      'flow': instance.flow,
      'cum_flow': instance.cum_flow,
      'time1': instance.time1,
      'time2': instance.time2,
      'time3': instance.time3,
      'time4': instance.time4,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };
