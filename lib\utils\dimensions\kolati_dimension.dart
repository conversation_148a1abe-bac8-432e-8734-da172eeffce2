

class KolatiDimension{


  double sourceWidth({required double x, required double y}){
    return 200;
  }

  double sourceHeight({required double x, required double y}){
    return 240;
  }

  double sourceX({required double x, required double y}){
    if(x<1600){
      return 64;
    }else{
      return 100;
    }
  }

  double sourceY({required double x, required double y}){
    return 32;
  }

  double l1X({required double x, required double y}){
    return sourceX(x: x, y: y)+sourceWidth(x: x, y: y);
  }

  double l1Y({required double x, required double y}){
    return sourceY(x: x, y: y)+sourceHeight(x: x, y: y)/2 - 15;
  }

  double l1Length({required double x, required double y}){
    if(x<1600){
      return 80;
    }else{
      return 120;
    }

  }

  double motor10Width({required double x, required double y}){
    return 200;
  }

  double motor10Height({required double x, required double y}){
    return 220;
  }

  double motor10X({required double x, required double y}){
    return l2X(x: x, y: y)+l2Length(x: x, y: y);
  }

  double motor10Y({required double x, required double y}){
    return l2Y(x: x, y: y) - motor10Height(x: x, y: y)/2;
  }

  double l2X({required double x, required double y}){
    return tank10X(x: x, y: y)+tank10Width(x: x, y: y);
  }

  double l2Y({required double x, required double y}){
    return l1Y(x: x, y: y);
  }

  double l2Length({required double x, required double y}){
    if(x<1600){
      return 80;
    }else{
      return 120;
    }

  }

  double l3X({required double x, required double y}){
    return motor10X(x: x, y: y)+motor10Width(x: x, y: y);
  }

  double l3Y({required double x, required double y}){
    return l2Y(x: x, y: y);
  }

  double l3Length({required double x, required double y}){
    if(x<1600){
      return 80;
    }else{
      return 120;
    }

  }

  double l4X({required double x, required double y}){
    return tank11X(x: x, y: y)+tank11Width(x: x, y: y);
  }

  double l4Y({required double x, required double y}){
    return l3Y(x: x, y: y);
  }

  double l4Length({required double x, required double y}){
    if(x<1600){
      return 80;
    }else{
      return 120;
    }

  }

  double l5X({required double x, required double y}){
    return motor11X(x: x, y: y)+motor11Width(x: x, y: y)/2-15;
  }

  double l5Y({required double x, required double y}){
    return motor11Y(x: x, y: y)+motor11Height(x: x, y: y);
  }

  double l5Length({required double x, required double y}){
    if(y<900){
      return 100;
    }else{
      return 160;
    }
  }

  double l6X({required double x, required double y}){
    return v4X(x: x, y: y)- l6Length(x: x, y: y);
  }

  double l6Y({required double x, required double y}){
    return v4Y(x: x, y: y) + 25;
  }

  double l6Length({required double x, required double y}){
    return 170;
  }

  double l7X({required double x, required double y}){
    return l6X(x: x, y: y) - valveWidth(x: x, y: y) - l7Length(x: x, y: y);
  }

  double l7Y({required double x, required double y}){
    return l6Y(x: x, y: y);
  }

  double l7Length({required double x, required double y}){
    return 170;
  }

  double valveHeight({required double x, required double y}){
    return 70;
  }

  double valveWidth({required double x, required double y}){
    return 70;
  }

  double tank10Width({required double x, required double y}){
    return 200;
  }

  double tank10Height({required double x, required double y}){
    return 250;
  }

  double tank10X({required double x, required double y}){
    return l1X(x: x, y: y)+l1Length(x: x, y: y);
  }

  double tank10Y({required double x, required double y}){
    return sourceY(x: x, y: y)-30;
  }

  double motor11Width({required double x, required double y}){
    return 200;
  }

  double motor11Height({required double x, required double y}){
    return 220;
  }

  double motor11X({required double x, required double y}){
    return l4X(x: x, y: y)+l4Length(x: x, y: y);
  }

  double motor11Y({required double x, required double y}){
    return motor10Y(x: x, y: y);
  }

  double tank11Width({required double x, required double y}){
    return 200;
  }

  double tank11Height({required double x, required double y}){
    return 250;
  }

  double tank11X({required double x, required double y}){
    return l3X(x: x, y: y)+l3Length(x: x, y: y);
  }

  double tank11Y({required double x, required double y}){
    return tank10Y(x: x, y: y);
  }

  double tank12Width({required double x, required double y}){
    return 300;
  }

  double tank12Height({required double x, required double y}){
    return 300;
  }

  double tank12X({required double x, required double y}){
    return l5X(x: x, y: y)-tank12Width(x: x, y: y)+80;
  }

  double tank12Y({required double x, required double y}){
    return l5Y(x: x, y: y)+l5Length(x: x, y: y)-25;
  }

  double tank13Width({required double x, required double y}){
    return 200;
  }

  double tank13Height({required double x, required double y}){
    return 200;
  }

  double tank13X({required double x, required double y}){
    return l1X(x: x, y: y)+l1Length(x: x, y: y);
  }

  double tank13Y({required double x, required double y}){
    return 16;
  }

  double tank14Width({required double x, required double y}){
    return 300;
  }

  double tank14Height({required double x, required double y}){
    return 300;
  }

  double tank14X({required double x, required double y}){
    return l7X(x: x, y: y)-tank14Width(x: x, y: y);
  }

  double tank14Y({required double x, required double y}){
    return tank12Y(x: x, y: y);
  }

  double v1X({required double x, required double y}){
    return tank12X(x: x, y: y)+tank12Width(x: x, y: y)-3;
  }

  double v1Y({required double x, required double y}){
    return tank12Y(x: x, y: y)+40;
  }

  double v2X({required double x, required double y}){
    return v1X(x: x, y: y);
  }

  double v2Y({required double x, required double y}){
    return tank12Y(x: x, y: y) + tank12Height(x: x, y: y)*0.45;
  }

  double v3X({required double x, required double y}){
    return v1X(x: x, y: y);
  }

  double v3Y({required double x, required double y}){
    return tank12Y(x: x, y: y) + tank12Height(x: x, y: y)*0.75;
  }

  double v4X({required double x, required double y}){
    return tank12X(x: x, y: y) - valveWidth(x: x, y: y);
  }

  double v4Y({required double x, required double y}){
    return tank12Y(x: x, y: y) + tank12Height(x: x, y: y)*0.25;
  }

  double v5X({required double x, required double y}){
    return v4X(x: x, y: y);
  }

  double v5Y({required double x, required double y}){
    return tank12Y(x: x, y: y) + tank12Height(x: x, y: y)*0.75;
  }

  double v6X({required double x, required double y}){
    return l6X(x: x, y: y) - valveWidth(x: x, y: y);
  }

  double v6Y({required double x, required double y}){
    return v4Y(x: x, y: y);
  }

  double v7X({required double x, required double y}){
    return l7X(x: x, y: y);
  }

  double v7Y({required double x, required double y}){
    return tank14Y(x: x, y: y) + tank14Height(x: x, y: y) * 0.75;
  }

  double v8X({required double x, required double y}){
    return tank14X(x: x, y: y)-valveWidth(x: x, y: y);
  }

  double v8Y({required double x, required double y}){
    return tank14Y(x: x, y: y) + 40;
  }

  double v9X({required double x, required double y}){
    return v8X(x: x, y: y);
  }

  double v9Y({required double x, required double y}){
    return tank14Y(x: x, y: y) + tank14Height(x: x, y: y)*0.45;
  }

  double v43X({required double x, required double y}){
    return v9X(x: x, y: y);
  }

  double v43Y({required double x, required double y}){
    return tank14Y(x: x, y: y) + tank14Height(x: x, y: y)*0.75;
  }

  double d1X({required double x, required double y}){
    return v1X(x: x, y: y) + valveWidth(x: x, y: y)+3;
  }

  double d1Y({required double x, required double y}){
    return v1Y(x: x, y: y)+13;
  }

  double d2X({required double x, required double y}){
    return v2X(x: x, y: y) + valveWidth(x: x, y: y)+3;
  }

  double d2Y({required double x, required double y}){
    return v2Y(x: x, y: y)+13;
  }

  double d3X({required double x, required double y}){
    return v3X(x: x, y: y) + valveWidth(x: x, y: y)+3;
  }

  double d3Y({required double x, required double y}){
    return v3Y(x: x, y: y)+13;
  }

  double d4X({required double x, required double y}){
    return v4X(x: x, y: y) + valveWidth(x: x, y: y)+3;
  }

  double d4Y({required double x, required double y}){
    return v4Y(x: x, y: y)+13;
  }


  double d5X({required double x, required double y}){
    return v5X(x: x, y: y) - 100;
  }

  double d5Y({required double x, required double y}){
    return v5Y(x: x, y: y)+13;
  }

  double d10X({required double x, required double y}){
    return v43X(x: x, y: y) - 100;
  }

  double d10Y({required double x, required double y}){
    return v43Y(x: x, y: y)+13;
  }

  double d9X({required double x, required double y}){
    return v9X(x: x, y: y) - 100;
  }

  double d9Y({required double x, required double y}){
    return v9Y(x: x, y: y)+13;
  }

  double d8X({required double x, required double y}){
    return v8X(x: x, y: y) - 100;
  }

  double d8Y({required double x, required double y}){
    return v8Y(x: x, y: y)+13;
  }

  double d7X({required double x, required double y}){
    return v7X(x: x, y: y)+valveWidth(x: x, y: y);
  }

  double d7Y({required double x, required double y}){
    return v7Y(x: x, y: y)+13;
  }






}