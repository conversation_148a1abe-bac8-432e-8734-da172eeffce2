
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/assets_manager.dart';
import 'package:si/constants/style_manager.dart';

import '../../common/widgets/app_icon_widget.dart';
import '../../common/widgets/base_scaffold.dart';
import '../../constants/app_sizes.dart';
import '../../utils/app_colors.dart';
import '../../utils/breakpoints.dart';
import 'login_text_form_field.dart';
import 'social_login_section.dart';

class LoginPage extends HookConsumerWidget {
  const LoginPage({Key? key, this.fromOnboarding = false}) : super(key: key);

  final bool fromOnboarding;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final width = MediaQuery.of(context).size.width;
    useEffect(() {
      SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(statusBarColor: Colors.black));
      return () => {};
    }, []);

    return BaseScaffold(
      showAppBar: false,
      child: Center(
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal:sliverTabletHorizontalPadding(MediaQuery.of(context).size.width)),
            child: Container(
              padding: horPadding8,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  gapH24,
                  AppIconWidget(imageSize: Size(150, 150),),
                  gapH24,
                  const FormSectionForLogin(),
                  //const SocialLoginSection(),
                  gapH16,
                  _buildBottomRichText(context),
                  gapH16
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomRichText(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        gapH20,
        RichText(
          text: TextSpan(
            recognizer: TapGestureRecognizer()..onTap = () {},
            text: "Don't have an account? ",
            style: TextStyle(fontSize: 16,color: Colors.grey),
            children: [
              TextSpan(
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                        context.push("/signup");
                    },
                  text: "Signup",
                  style: TextStyle(
                      color: AppColors.themeColor,
                      decoration: TextDecoration.underline)),
            ],
          ),
        ),
      ],
    );
  }
}
