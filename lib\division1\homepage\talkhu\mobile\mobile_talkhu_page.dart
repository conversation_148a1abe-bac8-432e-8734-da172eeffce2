import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/homepage/new_common_motor_unit.dart';
import 'package:si/division1/homepage/talkhu/mobile/dada_gau_page.dart';
import 'package:si/division1/homepage/talkhu/mobile/kharpa_gau_page.dart';

import 'package:si/division1/homepage/widgets/source_widget.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../../common/widgets/widgets.dart';
import '../../../../constants/app_sizes.dart';
import '../../../../services/shared_preferences_service.dart';
import '../../../../utils/app_colors.dart';
import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';
import '../../new_motor_unit.dart';
import '../../new_tank_unit.dart';
import 'school_page.dart';
import 'source_site_page.dart';

class MobileTalkhuPage extends HookConsumerWidget {
  const MobileTalkhuPage( this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    ref.listen(refreshProvider, (previous, next) async {
      ref
          .read(divisionStateProvider(prefs.getSiteId()).notifier)
          .getSetting(prefs.getSiteId());
    });

    return BaseScaffold(
        showLeftIcon: false,
        appbarText: setting?.name ?? "",
        showAction: prefs.getUserRole() >3 ? true : false,
        onActionClick:() async {
          final setting = await context.push("/location");
        },
        child: DefaultTabController(
          length: 4, // Number of tabs
          child: Scaffold(
            body: Column(
              children: [
                Container(
                  color: AppColors.themeColor,
                  child: const TabBar(
                    tabs: [
                      Tab(text: 'सिम मुहान'),
                      Tab(text: 'डाडा गाउ'),
                      Tab(text: 'खार्पा'),
                      Tab(text: 'स्कुल'),
                    ],
                    dividerColor: Colors.transparent,
                    indicatorSize: TabBarIndicatorSize.tab,
                    indicatorColor: Colors.white,
                    labelColor: AppColors.onThemeColor, // Customizing the TabBar
                    unselectedLabelColor: Colors.black54,
                    labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal, fontSize: 14),
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      SourceSitePage(setting!), // Corresponds to Tab 1
                      DadaGauPage(setting), // Corresponds to Tab 2
                      KharpaGauPage(setting),
                      SchoolPage(setting)// Corresponds to Tab 2
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
