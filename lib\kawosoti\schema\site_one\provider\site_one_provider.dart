

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../repository/abstract_site_repository.dart';
import '../../../repository/site_one_repository.dart';


final siteOneRepositoryProvider = Provider<ISiteFirestoreRepository>((ref) {
  return SiteOneFirestoreRepository();
});

final siteDataProvider = StreamProvider.autoDispose.family<DatabaseEvent, String>((ref, siteId) {
  final rtdb = ref.watch(siteOneRepositoryProvider);
  return rtdb.readSite(siteId);
});

final siteNameProvider = FutureProvider((ref) => ref.watch(siteOneRepositoryProvider).getNames());
