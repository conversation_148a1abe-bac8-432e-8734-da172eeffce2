import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';

class InverterWidget extends HookConsumerWidget {
  final String inverterId;

  const InverterWidget({super.key, this.inverterId = "10"});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final inverterState = useState<MotorModel>(const MotorModel());

    // Listen to inverter data from Firebase
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider(inverterId),
        (previous, next) {
      if (next.asData?.value.snapshot.value != null) {
        final datasnapshot = next.asData?.value.snapshot.value as Map;
        inverterState.value = MotorModel(
            motorAmps: datasnapshot['current'] ?? 0.0,
            voltage: datasnapshot['voltage'] ?? 0.0,
            freq: datasnapshot['freq'] ?? 0.0,
            device_status: datasnapshot['device_status'] ?? 0,
            time: datasnapshot['time'] ?? 0);
      }
    });

    // Calculate power (P = V × I)
    final power = (inverterState.value.voltage ?? 0.0) *
        (inverterState.value.motorAmps ?? 0.0);
    final isActive = inverterState.value.device_status == 3;

    return Container(
      height: 120,
      width: 160,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.green : Colors.grey,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: isActive
                ? Colors.green.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Title
            Text(
              'INVERTER',
              style: TextStyle(
                color: isActive ? Colors.green : Colors.grey,
                fontSize: 10,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.2,
              ),
            ),

            // Data in two rows
            Column(
              children: [
                // First row: Current and Power
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildDataItem(
                      'CURRENT',
                      (inverterState.value.motorAmps ?? 0.0).toStringAsFixed(2),
                      'A',
                      isActive,
                    ),
                    _buildDataItem(
                      'POWER',
                      power.toStringAsFixed(1),
                      'W',
                      isActive,
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Second row: Voltage and Frequency
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildDataItem(
                      'VOLTAGE',
                      (inverterState.value.voltage ?? 0.0).toStringAsFixed(1),
                      'V',
                      isActive,
                    ),
                    _buildDataItem(
                      'FREQ',
                      (inverterState.value.freq ?? 0.0).toStringAsFixed(1),
                      'Hz',
                      isActive,
                    ),
                  ],
                ),
              ],
            ),

            // Status indicator
            Container(
              width: double.infinity,
              height: 6,
              decoration: BoxDecoration(
                color: isActive ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataItem(
      String label, String value, String unit, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 8,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              value,
              style: TextStyle(
                color: isActive ? Colors.green : Colors.grey,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
            const SizedBox(width: 2),
            Text(
              unit,
              style: TextStyle(
                color: isActive ? Colors.green[300] : Colors.grey[600],
                fontSize: 9,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
