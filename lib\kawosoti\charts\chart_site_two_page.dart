import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:msh_checkbox/msh_checkbox.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/kawosoti/charts/chlorine_chart.dart';
import 'package:si/kawosoti/charts/line_chart.dart';
import 'package:si/kawosoti/charts/oh_chart.dart';
import 'package:si/kawosoti/charts/ph_chart.dart';
import 'package:si/kawosoti/charts/turbidity_chart.dart';
import 'package:si/kawosoti/schema/widgets/date_forward_backward.dart';

import '../../utils/app_colors.dart';
import '../schema/site_one/provider/chart_one_controller.dart';

class ChartSiteTwoPage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chartController = ref.watch(chartOneProvider("site2"));
    final t1Checked = useState(true);
    final t2Checked = useState(true);
    final t3Checked = useState(true);

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            DateForwardBackward(onDateChange: (date) {
              ref.read(chartOneProvider("site2").notifier).getData(date);
            }),
            chartController.when(
              success: (data, message) {
                return Consumer(
                    builder: (context, ref, child) {

                      return Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              children: [
                                const Text('Turbidity',style: TextStyle(fontSize: kIsWeb ? 20 : 14,color: Colors.black),),
                                gapW20,
                                const Text('T1',style: TextStyle(fontSize: kIsWeb ? 18 : 14,color: Colors.black),),
                                gapW8,
                                MSHCheckbox(
                                  size: kIsWeb ? 20 : 14,
                                  value: t1Checked.value,
                                  colorConfig: MSHColorConfig.fromCheckedUncheckedDisabled(
                                    checkedColor: AppColors.contentColorGreen,
                                  ),
                                  style: MSHCheckboxStyle.stroke,
                                  onChanged: (selected) {
                                    t1Checked.value = selected;
                                  },
                                ),
                                gapW20,

                                const Text('T2',style: TextStyle(fontSize: kIsWeb ? 18 : 14,color: Colors.black),),
                                gapW8,

                                MSHCheckbox(
                                  size: kIsWeb ? 20 : 14,
                                  value: t2Checked.value,
                                  colorConfig: MSHColorConfig.fromCheckedUncheckedDisabled(
                                    checkedColor: AppColors.contentColorBlue,
                                  ),
                                  style: MSHCheckboxStyle.stroke,
                                  onChanged: (selected) {
                                    t2Checked.value = selected;
                                  },
                                ),
                                gapW20,

                                const Text('T3',style: TextStyle(fontSize: kIsWeb ? 18 : 14,color: Colors.black),),
                                gapW8,
                                MSHCheckbox(
                                  size: kIsWeb ? 20 : 14,
                                  value: t3Checked.value,
                                  colorConfig: MSHColorConfig.fromCheckedUncheckedDisabled(
                                    checkedColor: AppColors.contentColorPurple,
                                  ),
                                  style: MSHCheckboxStyle.stroke,
                                  onChanged: (selected) {
                                    t3Checked.value = selected;
                                  },
                                ),
                                gapW20,

                              ],
                            ),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: TurbidityChart(chartModel: data!,
                                    showT1: t1Checked.value,
                                    showT2: t2Checked.value,
                                    showT3: t3Checked.value,
                                    showT4: false,
                                  ))
                          ),
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('PH',style: TextStyle(fontSize: kIsWeb ? 20 : 16,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: PhChart(chartModel: data,))
                          ),
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Chlorine',style: TextStyle(fontSize: kIsWeb ? 20 : 16,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: ChlorineChart(chartModel: data,))
                          ),
                        ],
                      );
                    }
                );
              },

              error: (er) {
                return Container();
              },
              unInitialized: () {
                return Container();
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            )
          ],
        ),
      ),
    );
  }
}


