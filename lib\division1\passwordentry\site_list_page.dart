

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/utils/constants.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../provider/auth_provider.dart';
import '../../provider/dashboard_provider.dart';
import '../../services/shared_preferences_service.dart';
import '../provider/division_provider.dart';

class SiteListPage extends HookConsumerWidget{

  const SiteListPage({ this.fromPage = SelectSiteFrom.fromPassword});

  final int fromPage;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final siteList = ref.watch(futureSiteListProvider);
    final prefs = ref.watch(sharedPreferencesServiceProvider);

    return BaseScaffold(
      showFab: prefs.getUserRole() == 5,
      onFabClick: () async {
        final navigate =  await context.push<bool>('/selectSite/addSite');
      },
      appbarText: 'Site List',
      child: Column(
        children: [
          Expanded(
            child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: siteList.when(data: (data){
                  return ListView(
                    children: [
                      for(var site in data)
                        Column(
                          children: [
                            ListTile(
                              tileColor: Colors.black,
                              iconColor: Colors.black,
                              textColor: Colors.black,
                              title: Text(site?.name ?? ''),
                              subtitle: Text(site?.id ?? ''),
                              onTap: (){
                                if(fromPage == SelectSiteFrom.forUsers){
                                  context.push("/settings/users",extra: site?.id);
                                } else if( fromPage == SelectSiteFrom.siteHistory){
                                  context.push('/history',
                                      extra: site?.id);
                                } else{
                                  context.pop(site?.password);
                                }
                              },
                            ),
                            Container(
                              height: 0.5,
                              color: Colors.grey,
                            )
                          ],
                        ),
                    ],
                  );
                },
                    error: (er,st){
                      return Container();
                    },
                    loading: ()=>const LoadingIndicator())

              /* ListView(
                   children: [
                     Visibility(
                       visible: roleType==3 || roleType==4,
                       child: ListTile(
                         tileColor: Colors.black,
                         iconColor: Colors.black,
                         textColor: Colors.black,
                         leading: Icon(Icons.history),
                         title: Text('Tank 1 Level History'),
                         onTap: (){

                         },),
                     ),
                     Visibility(
                       visible: roleType==3 || roleType==4,
                       child: Padding(
                         padding: const EdgeInsets.symmetric(horizontal: 16.0),
                         child: SizedBox(
                           height: 0.5,
                           child: Container(
                             color: Colors.grey,
                           ),
                         ),
                       ),
                     ),
                     Visibility(
                       visible: roleType==3 || roleType==4,
                       child: ListTile(
                         tileColor: Colors.black,
                         iconColor: Colors.black,
                         textColor: Colors.black,
                         leading: Icon(Icons.history),
                         title: Text('Tank 2 Level History'),
                       onTap: (){

                       },),
                     ),
                     Visibility(
                       visible: roleType==3 || roleType==4,
                       child: Padding(
                         padding: const EdgeInsets.symmetric(horizontal: 16.0),
                         child: SizedBox(
                           height: 0.5,
                           child: Container(
                             color: Colors.grey,
                           ),
                         ),
                       ),
                     ),

                     ListTile(
                       tileColor: Colors.black,
                       iconColor: Colors.black,
                       textColor: Colors.black,
                       leading: Icon(Icons.privacy_tip_outlined),
                       title: Text('Terms & Conditions'),),
                     Padding(
                       padding: const EdgeInsets.symmetric(horizontal: 16.0),
                       child: SizedBox(
                         height: 0.5,
                         child: Container(
                           color: Colors.grey,
                         ),
                       ),
                     ),
                     Padding(
                       padding: const EdgeInsets.symmetric(horizontal: 16.0),
                       child: SizedBox(
                         height: 0.5,
                         child: Container(
                           color: Colors.grey,
                         ),
                       ),
                     ),

                   ],
                ),*/
            ),
          ),
        ],
      ),
    );
  }
}