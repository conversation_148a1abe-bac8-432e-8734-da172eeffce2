

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';

import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../widgets/step_valve_widget.dart';

class TopGachiPage extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final motor10State = useState<MotorModel>(MotorModel());
    final motor11State = useState<MotorModel>(MotorModel());
    final  size = MediaQuery
        .of(context)
        .size;

    //final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return siteSetting.when(success: (setting,message){
      return  BaseScaffold(
          showLeftIcon: false,
          appbarText: setting?.name,
          child: Container(
          decoration: const BoxDecoration(
          gradient: LinearGradient(
          colors: [
              Color(0xFF87CEFA), // Light Blue (Sky color)
              Color(0xFFE0FFFF),
              Color(0xFF87CEFA), // Light Blue (Sky color)
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,),),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Container(
                    height: 50,
                    width: double.infinity,
                    color: Colors.blueAccent,
                    child: Center(child: Text("बोरिङ्ग पानी ",
                        style: TextStyle(color: Colors.white,fontSize: 18))),
                  ),
                  Row(
                    children: [
                      Column(
                        children: [
                          Container(
                            height: 40,
                            width: 30,
                            color:  Colors.blueAccent,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 16.0),
                            child: NewMotorUnit(
                              motorId: "10",
                              motorSettingModel: setting!.motor10!,
                              ratio: 2,
                              motorModel: motor10State.value,
                            ),
                          ),
                          gapH38,
                        ],
                      ),
                      Column(
                        children: [
                          gapH48,
                          Container(
                            height: 30,
                            width: 60,
                            color: Colors.blueAccent,
                          )
                        ],
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 16.0),
                          child: Container(
                            width: double.infinity,
                            child: Column(
                              children: [
                                gapH32,
                                NewTankUnit(prefs.getUserRole(),
                                    "10", setting!.tank10!, 150,double.infinity),
                                Container(
                                  height: 70,
                                  width: 30,
                                  color: Colors.blueAccent,
                                )
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  StepWiseValveWidget(name: "Step Valve",
                      valveId: "10",
                      role: prefs.getUserRole(),
                      motorModel: MotorModel()),
                  Container(
                    height:50,
                    width: 30,
                    color: Colors.blueAccent,
                  ),
                  Container(
                    height:60,
                    width: double.infinity,
                    color: Colors.blueAccent,
                    child: Center(
                      child: Text("Distribution",
                        style: TextStyle(color: Colors.white,fontSize: 18),),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ));
    },
        unInitialized: (){
          return Container();
        },
        error: (er){
          return Container();
        },
        unauthorized: ()=> const Center(child: Text("Unauthorized")),
        loading: ()=> LoadingIndicator());

  }

}