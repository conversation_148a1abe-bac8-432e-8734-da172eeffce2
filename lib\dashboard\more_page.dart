import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/provider/auth_provider.dart';
import 'package:si/provider/dashboard_provider.dart';

class MorePage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseScaffold(
      showAppBar: true,
      showLeftIcon: false,
      appbarText: "More",
      child: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                gapH16,
                Center(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(ref.read(userState).fullName ?? '', style: TextStyle(color: Colors.black, fontSize: 16),),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(ref.read(userState).email ?? '',style: TextStyle(color: Colors.black, fontSize: 16),),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(ref.read(userState).phoneNumber ?? '',style: TextStyle(color: Colors.black, fontSize: 16),),
                      ),
                    ],
                  ),
                ),
                gapH16,
                Container(height: 0.5,color: Colors.grey,),
                ListTile(
                  tileColor: Colors.black,
                  iconColor: Colors.black,
                  textColor: Colors.black,
                  leading: Icon(Icons.logout),
                  title: Text('Log out', style: TextStyle(color: Colors.black),),
                  onTap: () async {
                      await ref.read(authRepositoryProvider).signOut();
                      context.go("/login");
                  },
                ),
              ],
            ),
          ),
          Text('Powered by',style: TextStyle(fontSize: 16),),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('Swodeshi Innovation',style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),),
          )
        ],
      )
    );
  }
}
