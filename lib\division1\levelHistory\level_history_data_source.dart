

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:si/division1/repository/division_repository.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/services/firestore_path.dart';

import '../../../datatable/paginated_data_table_2.dart';
import '../../../utils/format.dart';
import '../model/level_history_model.dart';

// Async datasource for AsynPaginatedDataTabke2 example. Based on AsyncDataTableSource which
/// is an extension to FLutter's DataTableSource and aimed at solving
/// saync data fetching scenarious by paginated table (such as using Web API)
class LevelHistorySiteDataSource extends AsyncDataTableSource {

  LevelHistorySiteDataSource({this.userType=1}) {
    print('SiteDataSource created');
  }



  LevelHistorySiteDataSource.empty({this.userType=1}) {
    _empty = true;
    print('SiteDataSource.empty created');
  }

  LevelHistorySiteDataSource.error({this.userType=1}) {
    print('SiteDataSource.error created');
  }

  bool _empty = false;
  final int userType;


  DateTime? _siteDate;
  int tankId = 1;

  DateTime? startSiteDate;
  DateTime? endSiteDate;

  String  siteId = FirestorePath.schemaOne;

  set startDate(DateTime? value) {
    startSiteDate = value;
    notifyListeners();
  }

  set SiteId(String value){
    siteId = value;
    notifyListeners();
  }

  set TankId(int id){
    tankId = id;
    notifyListeners();
  }

  int startingIndex = 0;

  List<DivisionLevelHistorymodel> listSiteModel = [];

  final IDivisionRepository _repo = DivisionRepository();


  Future<int> getTotalRecords() {
    return Future<int>.delayed(
        const Duration(milliseconds: 0), () => _empty ? 0 : 15);
  }

  @override
  Future<AsyncRowsResponse> getRows(int start, int end) async {
    var index = 0;
    final today = DateTime.now();
    var x = start==0  ? await _repo.getSiteHistoryData(siteId: siteId, tankId: tankId,date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0)) :
    start<startingIndex ?  listSiteModel.skip(start).take(end).toList()   :await _repo.getSiteHistoryData(siteId:siteId,tankId: tankId,
        startAfterDate: _siteDate, date: startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0));
    if(start==0){
      listSiteModel.clear();
    }
    final count = await _repo.getLength(siteId:siteId,tankId:tankId,dateTime:(startSiteDate ?? DateTime(today.year, today.month, today.day,0,0,0)));
    if(count==0){
      _empty = true;
      return AsyncRowsResponse(listSiteModel.length, []);
    }else{
      var r = AsyncRowsResponse(
          count,
          x.map((siteModel) {
            index++;
            return DataRow(
              key: ValueKey<String>(siteModel!.time!.millisecondsSinceEpoch.toString()),
              selected: false,
              onSelectChanged: (value) {

              },
              cells: getSiteCells( start, index, siteModel),
            );
          }).toList());

      startingIndex =  start;
      if(start>=startingIndex){
        for(final model in x){
          listSiteModel.add(model!);
        }
      }
      _siteDate = x.last!.time;

      return r;
    }

  }
}


List<DataCell> getSiteCells(int start, int index, DivisionLevelHistorymodel siteModel) {
  return [
    DataCell(Text(Format.onlyTime(siteModel.time!).toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.level.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.mD1.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.mD2.toString(),style: TextStyle(fontSize: 14,color : Colors.black),)),
    DataCell(Text(siteModel.float.toString(),style: TextStyle(fontSize: 14,color : Colors.black),))];
}