

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DistributionWidget extends HookConsumerWidget{

  const DistributionWidget({required this.name, required this.height, required this.width, this.active =  false});

  final String name;
  final double height;
  final double width;
  final bool active;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        Image.asset(
          active ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
          height: height,
          width : width,
          fit: BoxFit.fill,
        ),
        Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Text(name, style: TextStyle(fontSize: 10),),
          ),
        )
      ],
    );
  }

}




class RotatedDistributionWidget extends HookConsumerWidget{

  const RotatedDistributionWidget({required this.name, required this.height, required this.width,
  this.turn = 2, this.isActive = false});

  final String name;
  final double height;
  final double width;
  final int turn;
  final isActive;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        RotatedBox(
          quarterTurns: turn,
          child: Image.asset(
            isActive ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
            height: height,
            width : width,
            fit: BoxFit.cover,
          ),
        ),
        Center(
          child: Padding(
            padding:  EdgeInsets.only(left: turn == 2 ? 48.0 : 8.0, bottom: 8),
            child: Text(name, style: TextStyle(fontSize: 8),),
          ),
        )
      ],
    );
  }

}

class UpdownDistributionWidget extends HookConsumerWidget{

  const UpdownDistributionWidget({required this.name, required this.height, required this.width,
    this.turn = 2, this.isFlowing = false});

  final String name;
  final double height;
  final double width;
  final int turn;
  final bool isFlowing;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        RotatedBox(
          quarterTurns: turn,
          child: Image.asset(
            isFlowing ? "assets/icons/updown_distribution.png" : "assets/icons/updown_distribution_grey.png",
            height: height,
            width : width,
            fit: BoxFit.cover,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 48.0, top: 24),
          child: Text(name, style: TextStyle(fontSize: 12),),
        )
      ],
    );
  }

}