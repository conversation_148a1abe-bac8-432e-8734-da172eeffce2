

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/dashboard/settingcontroller/setting_controller.dart';
import 'package:si/dashboard/settingcontroller/setting_state.dart';
import 'package:si/model/setting_model.dart';

import '../kawosoti/schema/model/user_model.dart';
import '../model/dashboard_model.dart';
import '../repository/dashboard_repository.dart';
import '../repository/rtdb_repository.dart';
import '../services/fcm_service.dart';
import '../services/notification_service.dart';
import 'auth_provider.dart';


final fcmServiceProvider  = Provider<FCMService>((ref){
  return FCMService();
});


final localNotificationProvider  = Provider<NotificationService>((ref){
  return NotificationService();
});

final dashboardRepositoryProvider = Provider<IDashboardRepository>((ref) => DashboardRepository());

final rtdbProvider = Provider<RealtimeDatabase>((ref) => RealtimeDatabase());


final site1Provider = StreamProvider<DashboardModel?>((ref)  {
    final database = ref.watch(dashboardRepositoryProvider);
    final auth = ref.watch(authStateNotifierProviderAuth);
    if (auth != null) {
        return database.getDashboard("site1");
    } else {
      return const Stream.empty();
    }
});


final site2Provider = StreamProvider<DashboardModel?>((ref)  {
  final database = ref.watch(dashboardRepositoryProvider);
  final auth = ref.watch(authStateNotifierProviderAuth);
  if (auth != null) {
    return database.getDashboard("site2");
  } else {
    return const Stream.empty();
  }
});

final settingProvider = FutureProvider.autoDispose.family<SettingModel,String>((ref,req)  {
  final rtdb = ref.watch(rtdbProvider);
  final auth = ref.watch(authStateNotifierProviderAuth);
  if (auth != null) {
    return rtdb.getSetting(req);
  } else {
    return Future.value(SettingModel());
  }

});



final settingControllerProvider = StateNotifierProvider.autoDispose
<SettingController, SettingState>((ref) {
  final dashboardRepository = ref.watch(rtdbProvider);
  return SettingController(
    repository: dashboardRepository,
  );
});



final userProvider = StreamProvider<UserModel?>((ref)  {
  final database = ref.watch(dashboardRepositoryProvider);
  final auth = ref.watch(authStateNotifierProviderAuth);
  if (auth != null) {
    return database.getUser();
  } else {
    return const Stream.empty();
  }
});


final userState = StateProvider((ref) => UserModel(role: 1));