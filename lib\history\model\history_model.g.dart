// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HistorymodelImpl _$$HistorymodelImplFromJson(Map<String, dynamic> json) =>
    _$HistorymodelImpl(
      loc_id: (json['loc_id'] as num?)?.toInt() ?? 0,
      status: (json['status'] as num?)?.toInt() ?? 0,
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$HistorymodelImplToJson(_$HistorymodelImpl instance) =>
    <String, dynamic>{
      'loc_id': instance.loc_id,
      'status': instance.status,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };

_$LevelHistorymodelImpl _$$LevelHistorymodelImplFromJson(
        Map<String, dynamic> json) =>
    _$LevelHistorymodelImpl(
      loc_id: (json['loc_id'] as num?)?.toInt() ?? 0,
      level: (json['level'] as num?)?.toInt() ?? 0,
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$LevelHistorymodelImplToJson(
        _$LevelHistorymodelImpl instance) =>
    <String, dynamic>{
      'loc_id': instance.loc_id,
      'level': instance.level,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };

_$AllHistorymodelImpl _$$AllHistorymodelImplFromJson(
        Map<String, dynamic> json) =>
    _$AllHistorymodelImpl(
      loc_id: (json['loc_id'] as num?)?.toInt() ?? 0,
      float: (json['float'] as num?)?.toInt() ?? 0,
      motor: (json['motor'] as num?)?.toInt() ?? 0,
      timer: (json['timer'] as num?)?.toInt() ?? 0,
      level: (json['level'] as num?)?.toInt() ?? 0,
      current: (json['current'] as num?)?.toDouble() ?? 0.0,
      time: const TimestampNullableConverter()
          .fromJson(json['time'] as Timestamp?),
    );

Map<String, dynamic> _$$AllHistorymodelImplToJson(
        _$AllHistorymodelImpl instance) =>
    <String, dynamic>{
      'loc_id': instance.loc_id,
      'float': instance.float,
      'motor': instance.motor,
      'timer': instance.timer,
      'level': instance.level,
      'current': instance.current,
      'time': const TimestampNullableConverter().toJson(instance.time),
    };
