

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/homepage/widgets/sensor_widget.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/utils/app_dimension.dart';

import '../../../common/widgets/widgets.dart';
import '../../../provider/auth_provider.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import 'distribution_widget.dart';
import 'flow_meter_widget.dart';
import 'valve_widget.dart';

class SensorBasedValve extends HookConsumerWidget{

  const SensorBasedValve({required this.size, required this.appDimension});

  final Size size;
  final SindhupalchowkDimension appDimension;
  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final prefs = ref.read(sharedPreferencesServiceProvider);
    final setting = DivisionSettingModel();
    final sensorState = useState<SensorModel>(SensorModel());
    final valveState = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(sensorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            sensorState.value = SensorModel(
              tur: datasnapshot['tur'],
              time: datasnapshot['time'],
              flow: datasnapshot['flow'],
              cum_flow: datasnapshot['cum_flow'],
            );
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valveState.value = MotorModel(
              mobile_status: datasnapshot['mobile_status'],
              device_status: datasnapshot['device_status'],
              output_status: datasnapshot['output_status'],
            );
          }
        });


    return Container(
      color: Colors.black.withAlpha(50),
      height: appDimension.valveControllerHeight(x: size.width, y: size.height),
      width: appDimension.valveControllerWidth(x: size.width, y: size.height),
      child: Stack(
        children: [
          Positioned(

            top: appDimension.l1Y(x: size.width, y: size.height),
            left: appDimension.l1X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l1Length(x: size.width, y: size.height),
              name: "L1",
              isFlowing: true,
              isTop: false,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l112Y(x: size.width, y: size.height),
            left: appDimension.l112X(x: size.width, y: size.height),
            child: VerticalPipeline(
              length: appDimension.l2Length(x: size.width, y: size.height),
              name: "L2",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: true,
              isLeft: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l3Y(x: size.width, y: size.height),
            left: appDimension.l3X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l3Length(x: size.width, y: size.height),
              name: "L3",
              isFlowing: true,
              isTop: false,
              isRight: false,
              isBottom: false,
              isLeft: false,
              leftFlow: true,
              singleFlow: true,
            ),
          ),
          Positioned(
            top: appDimension.l4Y(x: size.width, y: size.height),
            left: appDimension.l4X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l4Length(x: size.width, y: size.height),
              name: "L4",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            left: appDimension.v1X(x: size.width, y: size.height),
            top: appDimension.v1Y(x: size.width, y: size.height),
            child: SindhulpalchowkValveWidget(
              size: size,
              role: prefs.getUserRole(),
              appDimension: appDimension,
              valveId: "10",
              valveModel: valveState.value,
              showProgress: false,
              name: "Valve 10",
            ),
          ),
          Positioned(
            left: appDimension.v2X(x: size.width, y: size.height),
            top: appDimension.v2Y(x: size.width, y: size.height),
            child: SindhulpalchowkValveWidget(
              size: size,
              role: prefs.getUserRole(),
              appDimension: appDimension,
              showProgress: false,
              valveId: "11",
              valveModel: valveState.value,
              name: "Valve 11",
            ),
          ),
          Positioned(
            top: appDimension.l5Y(x: size.width, y: size.height),
            left: appDimension.l5X(x: size.width, y: size.height),
            child: HorizontalPipeline(
              length: appDimension.l5Length(x: size.width, y: size.height),
              name: "L5",
              isFlowing: true,
              isTop: true,
              isRight: false,
              isBottom: false,
              isLeft: false,
              singleFlow: true,
            ),
          ),
          Positioned(
            key: const ValueKey('D1'),
            top: appDimension.d1Y(x: size.width, y: size.height),
            left: appDimension.d1X(x: size.width, y: size.height),
            child: DistributionWidget(
              active: valveState.value.device_status == 3,
                name: "By Pass", height: 60, width: 100),
          ),

          Positioned(
              top: appDimension.flow1Y(x: size.width, y: size.height),
              left: appDimension.flow1X(x: size.width, y: size.height),
              child: FlowMeterWidget(sensorModel: sensorState.value,)),
          Positioned(
            key: const ValueKey('S1'),
            top: appDimension.s1Y(x: size.width, y: size.height),
            left: appDimension.s1X(x: size.width, y: size.height),
            child:  SingleSensorWidget(listenerRequired: false,
            sensorModel: sensorState.value,
            sensorId: "10",),
          ),

        ],
    )
    );
  }

}