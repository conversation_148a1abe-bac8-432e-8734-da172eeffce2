import 'package:flutter/material.dart';


extension UIThemeExtension on BuildContext {

  // * (default) TextTheme
  TextStyle get s20Bold => Theme.of(this).textTheme.displayLarge!;
  TextStyle get s18Bold => Theme.of(this).textTheme.displayMedium!;
  TextStyle get s16Bold => Theme.of(this).textTheme.displaySmall!;

  TextStyle get s14Bold => Theme.of(this).textTheme.headlineLarge!;
  TextStyle get s12Bold => Theme.of(this).textTheme.headlineMedium!;
  TextStyle get s10Bold => Theme.of(this).textTheme.headlineSmall!;

  TextStyle get s16Regular => Theme.of(this).textTheme.titleLarge!;
  TextStyle get s14Regular => Theme.of(this).textTheme.titleMedium!;
  TextStyle get s12Regular => Theme.of(this).textTheme.titleSmall!;

  TextStyle get s20Light => Theme.of(this).textTheme.labelLarge!;
  TextStyle get s18Light => Theme.of(this).textTheme.labelMedium!;
  TextStyle get s16Light => Theme.of(this).textTheme.labelSmall!;
  TextStyle get s14Light => Theme.of(this).textTheme.bodyLarge!;
  TextStyle get s12Light => Theme.of(this).textTheme.bodyMedium!;
  TextStyle get s10Light => Theme.of(this).textTheme.bodySmall!;

  TextStyle get durationText => s12Light.copyWith(color: onThemeColor);
  TextStyle get tagsText => s12Light.copyWith(color: ontagsColor);
  TextStyle get appBarText => s20Bold;
  TextStyle get titleText => s20Bold;
  TextStyle get subTitleText => s16Bold;
  TextStyle get itemTitle => s18Bold.copyWith(color: Colors.white);
  TextStyle get itemsubTitle => s14Light.copyWith(color: Colors.white);
  TextStyle get dateTime => s12Light.copyWith(color: Colors.white);
  TextStyle get underLineSubTitleText => s16Bold.copyWith(color: themeColor,decoration: TextDecoration.underline);
  TextStyle get hintText => s18Light.copyWith(color: Colors.grey);
  TextStyle get labelText => s18Light.copyWith(color: onBackgroundColor);
  TextStyle get accountlabelText => s16Light.copyWith(color: onBackgroundColor);
  TextStyle get textformText => s18Light.copyWith(color: Colors.black);
  TextStyle get seeAllText => s18Light.copyWith(color: Colors.grey);
  TextStyle get displayText => s16Light.copyWith(color: Colors.grey);
  TextStyle get errorText => s14Light.copyWith(color: Colors.red);
  TextStyle get bodyText => s14Light;
  TextStyle get  subbodyText => s12Light.copyWith(color: Colors.grey);
  TextStyle get  underLinesubBodyText => s12Light.copyWith(color: themeColor,decoration: TextDecoration.underline);
  TextStyle get buttonText => s18Bold;

  Color get themeColor => Theme.of(this).colorScheme.primary ;
  Color get overlayColor => Theme.of(this).colorScheme.primaryContainer ;
  Color get onThemeColor => Theme.of(this).colorScheme.onPrimary;
  Color get tagsColor => Theme.of(this).colorScheme.surface;
  Color get ontagsColor => Theme.of(this).colorScheme.onSurface;

  Color get buttonPrimaryColor => Theme.of(this).buttonTheme.colorScheme?.primary ?? Colors.green;
  Color get buttonSecondaryColor => Theme.of(this).buttonTheme.colorScheme?.secondary ?? Colors.green;
  Color get onButtonColor => Theme.of(this).buttonTheme.colorScheme?.onPrimary ?? Colors.white;
  Color get progressBarColor => Theme.of(this).progressIndicatorTheme.color ?? Colors.green;
  Color get hintColor => Colors.grey;
  Color get tabBarSelectedColor => Theme.of(this).colorScheme.secondary;

  Color get onProgramColor => Theme.of(this).colorScheme.onSecondaryContainer;
  Color get onLibraryColor => Theme.of(this).colorScheme.secondaryContainer;
  Color get onSessionColor => Theme.of(this).colorScheme.onSecondary;
  Color get onNutritionColor => Theme.of(this).colorScheme.tertiaryContainer;
  Color get onWellnessColor => Theme.of(this).colorScheme.onTertiaryContainer;
  Color get backgroundColor => Theme.of(this).colorScheme.background;
  Color get onBackgroundColor => Theme.of(this).colorScheme.onBackground;
  Color get appBarBgColor => Theme.of(this).appBarTheme.backgroundColor ?? Colors.green;
  Color get onAppBarColor => Theme.of(this).appBarTheme.foregroundColor ?? Colors.white;
  Color get activeTabColor => Theme.of(this).bottomNavigationBarTheme.selectedItemColor ?? Colors.green;




}

extension HexColor on Color {
  static Color? fromHex(String hexColorString) {
    if(hexColorString.trim().isEmpty ||( hexColorString.trim().length<6)){
      return null;
    }
    hexColorString = hexColorString.replaceAll('#', '');
    if (hexColorString.length == 6) {
      hexColorString = "FF$hexColorString"; // 8 char with opacity 100%
    }else if(hexColorString.length==8){
      final list = hexColorString.split('');
      hexColorString = list[6]+list[7]+list[0]+list[1]+list[2]+list[3]+list[4]+list[5];
    }
    try{
      return Color(int.parse(hexColorString, radix: 16));
    }catch(e){
      return null;
    }
  }
}
