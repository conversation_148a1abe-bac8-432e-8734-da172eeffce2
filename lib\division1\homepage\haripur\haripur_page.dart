

import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/chanakhel/system_one_page.dart';
import 'package:si/division1/homepage/chanakhel/system_two_page.dart';
import 'package:si/division1/homepage/haripur/haripur_system_one_page.dart';
import 'package:si/division1/homepage/haripur/haripur_system_two_page.dart';
import 'package:si/division1/homepage/haripur/web_haripur_page.dart';
import 'package:si/utils/app_colors.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../services/shared_preferences_service.dart';
import '../../provider/division_provider.dart';

class HaripurPage extends HookConsumerWidget{

  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    final width = MediaQuery.of(context).size.width;
    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });


    return siteSetting.when(
        success: (setting, message) {
          if(width > 600){
            return BaseScaffold(
                showLeftIcon: false,
                showAppBar: false,
                appbarText: setting?.name ?? "",
                showAction: prefs.getUserRole() >3 ? true : false,
                onActionClick:() async {
                  final setting = await context.push("/location");
                },
                child: WebHaripurPage(setting!));
          }else{
            return BaseScaffold(
                showLeftIcon: false,
                appbarText: setting?.name ?? "",
                showAction: prefs.getUserRole() >3 ? true : false,
                onActionClick:() async {
                  final setting = await context.push("/location");
                },
                child: DefaultTabController(
                  length: 2, // Number of tabs
                  child: Scaffold(
                    body: Column(
                      children: [
                        Container(
                          color: AppColors.themeColor,
                          child: const TabBar(
                            tabs: [
                              Tab(text: 'System 1'),
                              Tab(text: 'System 2'),
                            ],
                            dividerColor: Colors.transparent,
                            indicatorSize: TabBarIndicatorSize.tab,
                            indicatorColor: Colors.white,
                            labelColor: AppColors.onThemeColor, // Customizing the TabBar
                            unselectedLabelColor: Colors.black54,
                            labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                            unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal, fontSize: 14),
                          ),
                        ),
                        Expanded(
                          child: TabBarView(
                            children: [
                              HaripurSystemOnePage(setting!), // Corresponds to Tab 1
                              HaripurSystemTwoPage(setting), // Corresponds to Tab 2
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ));
          }
          
        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }
}