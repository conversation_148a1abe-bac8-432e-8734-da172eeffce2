

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../utils/app_colors.dart';

class SubTileWidget extends HookConsumerWidget{

  const SubTileWidget({required this.title,
    this.isActive,
    required this.onTap});

  final VoidCallback onTap;

  final String title;
  final bool? isActive;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(left: 32.0),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
              fontSize: 14, color: (isActive ?? false) ?  AppColors.themeColor : AppColors.onSideMenu),
        ),
        onTap:onTap,
      ),
    );
  }

}