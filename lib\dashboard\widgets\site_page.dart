

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/dashboard/widgets/float_status.dart';
import 'package:si/dashboard/widgets/tank_monitor.dart';
import 'package:si/dashboard/widgets/timer_status.dart';

import '../../model/dashboard_model.dart';
import 'display_widget.dart';
import 'motor_moving.dart';

class SitePage extends HookConsumerWidget{

  SitePage(this.dashboardModel,this.site);

  final DashboardModel dashboardModel;
  final String site;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
        child: Column(children: [
          Container(
            height: 250,
            child: TankMonitor(dashboardModel,site),
          ),
          Container(
            height: 0.5,
            color: Colors.grey,
          ),
          Row(children: [
            Flexible(
                child: FloatStatus(dashboardModel,site)),
            Container(
              width: 0.5,
              height: 200,
              color: Colors.grey,
            ),
            Flexible(
                child: TimerStatus(dashboardModel,site))
          ]),
          Container(
            height: 0.5,
            color: Colors.grey,
          ),
          <PERSON>(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              MotorMoving(dashboardModel,site),
              Display(dashboardModel,site),
            ],
          )
        ]));
  }

}