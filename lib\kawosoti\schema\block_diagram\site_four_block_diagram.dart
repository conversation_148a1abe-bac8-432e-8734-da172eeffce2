


import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import '../site_one/provider/site_one_provider.dart';
import '../widgets/block_container.dart';
import '../widgets/connector_widget.dart';



class SiteFourBlockDiagram extends HookConsumerWidget{

  const SiteFourBlockDiagram({Key? key, required this.siteRtDbModel}) : super(key: key);

  static const _colors = [
    Color(0xFF76BAE7),
    Color(0xFF00BBF9),
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFF865726);

  final SiteRtDbModel siteRtDbModel;



  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Center(
        child: _buildHeightCalculator(context),
      ),
    );
  }

  /// Returns the height calculator.
  Widget _buildHeightCalculator(BuildContext context) {
    final Brightness brightness = Theme.of(context).brightness;

    return Padding(
        padding: const EdgeInsets.all(10),
        child: Center(
            child: Container(
                height: MediaQuery.of(context).size.height * 3 / 4,
                padding: const EdgeInsets.all(5.0),
                child: SfLinearGauge(
                  orientation: LinearGaugeOrientation.vertical,
                  maximum: 200,
                  tickPosition: LinearElementPosition.outside,
                  labelPosition: LinearLabelPosition.outside,
                  minorTicksPerInterval: 0,
                  interval: 60,
                  onGenerateLabels: () {
                    return <LinearAxisLabel>[
                      const LinearAxisLabel(text: '0 cm', value: 0),
                      const LinearAxisLabel(text: '25 cm', value: 25),
                      const LinearAxisLabel(text: '50 cm', value: 50),
                      const LinearAxisLabel(text: '75 cm', value: 75),
                      const LinearAxisLabel(text: '100 cm', value: 100),
                      const LinearAxisLabel(text: '125 cm', value: 125),
                      const LinearAxisLabel(text: '150 cm', value: 150),
                      const LinearAxisLabel(text: '175 cm', value: 175),
                      const LinearAxisLabel(text: '200 cm', value: 200),
                    ];
                  },
                  axisTrackStyle: const LinearAxisTrackStyle(),
                  markerPointers: <LinearMarkerPointer>[
                    LinearShapePointer(
                        value: 130,
                        enableAnimation: false,
                        onChanged: (dynamic value) {

                        },
                        shapeType: LinearShapePointerType.rectangle,
                        color: const Color(0xff0074E3),
                        height: 1.5,
                        width: 250),
                    LinearWidgetPointer(
                        value: 130,
                        enableAnimation: false,
                        onChanged: (dynamic value) {

                        },
                        child: SizedBox(
                            width: 24,
                            height: 16,
                            child: Image.asset(
                              'icons/rectangle_pointer.png',
                            ))),
                    LinearWidgetPointer(
                        value: 130,
                        enableAnimation: false,
                        onChanged: (dynamic value) {

                        },
                        offset: 230,
                        position: LinearElementPosition.outside,
                        child: Container(
                            width: 60,
                            height: 25,
                            decoration: BoxDecoration(
                                color: Colors.grey,
                                boxShadow: <BoxShadow>[
                                  BoxShadow(
                                    color: brightness == Brightness.light
                                        ? Colors.grey
                                        : Colors.black54,
                                    offset: const Offset(0.0, 1.0), //(x,y)
                                    blurRadius: 6.0,
                                  ),
                                ],
                                borderRadius: BorderRadius.circular(4)),
                            child: Center(
                              child: Text(
                                  130.toStringAsFixed(0) + ' cm',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.normal,
                                      fontSize: 14,
                                      color: Color(0xff0074E3))),
                            ))),
                  ],
                  ranges: <LinearGaugeRange>[
                    LinearGaugeRange(
                      endValue: 130,
                      startWidth: 200,
                      midWidth:  200,
                      endWidth: 200,
                      color: Colors.lightBlueAccent,
                    ),
                  ],
                ))));
  }


}