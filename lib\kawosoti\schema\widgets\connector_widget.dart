

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class ConnectorWidget extends HookConsumerWidget{
  const ConnectorWidget({Key? key, required this.width, required this.height,this.turbidity = 0,  this.turbidyName = "t",this.isLeft = true}) : super(key: key);

  final double height;
  final double width;
  final bool isLeft;
  final double? turbidity;
  final String turbidyName;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: isLeft ?  CrossAxisAlignment.start   :CrossAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(turbidyName +"="+ (turbidity ?? 0.0).toStringAsFixed(2)),
        ),
        Container(
          height: height,
          width: width,
          child: Row(
            children: [
              Expanded(
                child: RotatedBox(
                  quarterTurns: 2,
                  child: LoadingAnimationWidget.prograssiveDots(
                    color: Colors.lightBlueAccent,
                    size: 40,
                  ),
                ),
              ),
              Expanded(
                child: RotatedBox(
                  quarterTurns: 2,
                  child: LoadingAnimationWidget.prograssiveDots(
                    color: Colors.lightBlueAccent,
                    size: 40,
                  ),
                ),
              ),
              Expanded(
                child: RotatedBox(
                  quarterTurns: 2,
                  child: LoadingAnimationWidget.prograssiveDots(
                    color: Colors.lightBlueAccent,
                    size: 40,
                  ),
                ),
              ),
            ],
          ),
          decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: Colors.black))),
          ),
        Text("")
      ],
    );
  }
}


class ConnectorRotateWidget extends HookConsumerWidget{
  const ConnectorRotateWidget({Key? key, required this.width, required this.height,this.turbidity = 0,  this.turbidyName = "t",this.isLeft = true}) : super(key: key);

  final double height;
  final double width;
  final bool isLeft;
  final double? turbidity;
  final String turbidyName;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: isLeft ?  CrossAxisAlignment.start   :CrossAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(turbidyName +"="+ (turbidity ?? 0.0).toStringAsFixed(2)),
        ),
        Container(
          height: height,
          width: width,
          child: Row(
            children: [
              Expanded(
                child: LoadingAnimationWidget.prograssiveDots(
                  color: Colors.lightBlueAccent,
                  size: 40,
                ),
              ),
              Expanded(
                child: LoadingAnimationWidget.prograssiveDots(
                  color: Colors.lightBlueAccent,
                  size: 40,
                ),
              ),
              Expanded(
                child: LoadingAnimationWidget.prograssiveDots(
                  color: Colors.lightBlueAccent,
                  size: 40,
                ),
              ),
            ],
          ),
          decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: Colors.black))),
        ),
        Text("")
      ],
    );
  }
}