

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/style_manager.dart';
import 'package:si/provider/auth_provider.dart';

import '../../common/widgets/app_icon_widget.dart';
import '../../common/widgets/base_scaffold.dart';
import '../../constants/app_sizes.dart';
import '../../utils/app_colors.dart';
import '../../utils/breakpoints.dart';
import 'password_form.dart';

class PasswordEntryPage extends HookConsumerWidget{
  const PasswordEntryPage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useEffect(() {
      SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(statusBarColor: Colors.black));
      return () => {};
    }, []);

    return BaseScaffold(
      showAppBar: false,
      appbar: AppBar(
        backgroundColor: AppColors.themeColor,
        centerTitle: true,
        actions: [IconButton(onPressed: () async{
          EasyLoading.show();
          await ref.read(authRepositoryProvider).signOut();
          EasyLoading.dismiss();
          context.go('/login');

        }, icon: Icon(Icons.logout, color: Colors.white,))] ,
        title: Text("Password" ?? 'Appbar', style: TextStyle(color: AppColors.onThemeColor),),
      ),
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal:sliverTabletHorizontalPadding(MediaQuery.of(context).size.width)),
          child: Container(
            padding: horPadding8,
            child: const SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  gapH24,
                  const AppIconWidget(imageSize: Size(150, 150),),
                  gapH24,
                  const FormSectionForPassword(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

}