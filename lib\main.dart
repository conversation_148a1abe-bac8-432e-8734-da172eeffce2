import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:si/routing/app_router.dart';

import 'division1/hive/hive_key.dart';
import 'provider/dashboard_provider.dart';
import 'services/locator.dart';
import 'services/shared_preferences_service.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  setupLocator();
  final sharedPreferences = await SharedPreferences.getInstance();
  await Hive.initFlutter();

  const FlutterSecureStorage secureStorage = FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true));
  var containsEncryptionKey = await secureStorage.containsKey(
      key: HiveKey.encryptionKey,
      aOptions: const AndroidOptions(encryptedSharedPreferences: true));
  if (!containsEncryptionKey) {
    var key = Hive.generateSecureKey();
    await secureStorage.write(
        key: HiveKey.encryptionKey,
        value: base64UrlEncode(key),
        aOptions: const AndroidOptions(encryptedSharedPreferences: true));
  }
  var encryptionKey = base64Url.decode((await secureStorage.read(
          key: HiveKey.encryptionKey,
          aOptions: const AndroidOptions(encryptedSharedPreferences: true))) ??
      '');
  await Hive.openBox(HiveKey.siBox,
      encryptionCipher: HiveAesCipher(encryptionKey));

  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  runApp(ProviderScope(overrides: [
    sharedPreferencesServiceProvider.overrideWithValue(
      SharedPreferencesService(sharedPreferences),
    ),
  ], child: MyApp()));
}

class MyApp extends StatefulHookConsumerWidget {
  MyApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => AppState();
}

class AppState extends ConsumerState<MyApp> {
  void onBgMessage() async {
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      // handleNotification(fromBackground:true, message: initialMessage);
    }
  }

  void handleNotification(
      {bool fromBackground = false, required RemoteMessage message}) async {
    if (message.data != null) {
      if (message.data.containsKey('title') &&
          message.data.containsKey('body')) {
        ref
            .read(localNotificationProvider)
            .showNotifications(message.data['title'], message.data['body']);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    ref.read(fcmServiceProvider).requestPermission();
    ref.read(fcmServiceProvider).loadFcm();
  }

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      FirebaseMessaging.onMessage.listen((message) {
        if (message.data.containsKey('title') &&
            message.data.containsKey('body')) {
          ref
              .read(localNotificationProvider)
              .showNotifications(message.data['title'], message.data['body']);
        }
      });

      FirebaseMessaging.onMessageOpenedApp
          .listen((message) => handleNotification(message: message));

      return;
    }, const []);

    return MaterialApp.router(
      builder: EasyLoading.init(),
      routerConfig: ref.read(go1RouterProvider),
    );
  }
}

/// A page that fades in an out.
class FadeTransitionPage extends CustomTransitionPage<void> {
  /// Creates a [FadeTransitionPage].
  FadeTransitionPage({
    required LocalKey super.key,
    required super.child,
  }) : super(
            transitionsBuilder: (BuildContext context,
                    Animation<double> animation,
                    Animation<double> secondaryAnimation,
                    Widget child) =>
                FadeTransition(
                  opacity: animation.drive(_curveTween),
                  child: child,
                ));

  static final CurveTween _curveTween = CurveTween(curve: Curves.easeIn);
}
