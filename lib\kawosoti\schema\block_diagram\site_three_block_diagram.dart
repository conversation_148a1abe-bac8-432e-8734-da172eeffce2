


import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/schema/model/site_model.dart';
import 'package:si/kawosoti/schema/widgets/borewell_sensor.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import '../site_one/provider/site_one_provider.dart';
import '../widgets/block_container.dart';
import '../widgets/connector_widget.dart';

class SiteThreeBlockDiagram extends HookConsumerWidget{

  const SiteThreeBlockDiagram({Key? key, required this.siteRtDbModel}) : super(key: key);

  static const _colors = [
    Color(0xFF76BAE7),
    Color(0xFF00BBF9),
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _backgroundColor = Color(0xFFBAE5F3);

  final SiteRtDbModel siteRtDbModel;


  @override
  Widget build(BuildContext context, WidgetRef ref) {


    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          BlockContainer(
            height: 70,
            width: 70,
            text: "Source",
          ),
          ConnectorWidget(
            height: 20,
            width: 100,
            turbidity: siteRtDbModel.Tub3,
            turbidyName: "t3",
          ),
          BlockContainer(
            height: 100,
            width: 100,
            text: "Sediment Tank",
          ),
          ConnectorWidget(
            height: 20,
            width: 100,
            turbidity: siteRtDbModel.Tub1,
            turbidyName: "t1",

          ),

          BlockContainer(
            height: 100,
            width: 100,
            text: "Slow Sand Filter",
          ),
          ConnectorWidget(
            height: 20,
            width: 100,
            turbidity: siteRtDbModel.Tub2,
            turbidyName: "t2",
          ),
          Container(
            height: 220,
            width: 120,
            decoration: BoxDecoration(
                border: Border(left: BorderSide(color: Colors.blue),right: BorderSide(color: Colors.blue),bottom: BorderSide(color: Colors.blue))),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Stack(
                children: [
                  WaveWidget(
                    config: CustomConfig(
                      colors: _colors,
                      durations: _durations,
                      heightPercentages: [1 - 0.8, 1 - 0.8],
                    ),
                    backgroundColor: _backgroundColor,
                    size: Size(double.infinity, double.infinity),
                    waveAmplitude: 0,
                    heightPercentage: 0.1,
                  ),
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('Ph : ${(siteRtDbModel.PH ?? 7.0).toStringAsFixed(2)}'),
                        Text('Cl : ${(siteRtDbModel.Chlorine ?? 0.0).toStringAsFixed(2)}'),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
          ConnectorRotateWidget(
            height: 20,
            width: 100,
            turbidity: siteRtDbModel.Tub3,
            turbidyName: "t3",
          ),
          BoreWellSensor()


        ],
      ),
    );
  }

}