// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      email: json['email'] as String?,
      fullName: json['fullName'] as String?,
      id: json['id'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      role: (json['role'] as num?)?.toInt(),
      siteId: json['siteId'] as String?,
      is_active: json['is_active'] as bool?,
      token: json['token'] as String?,
      send_notification: json['send_notification'] as bool?,
      created_at: const TimestampNullableConverter()
          .fromJson(json['created_at'] as Timestamp?),
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'fullName': instance.fullName,
      'id': instance.id,
      'phoneNumber': instance.phoneNumber,
      'role': instance.role,
      'siteId': instance.siteId,
      'is_active': instance.is_active,
      'token': instance.token,
      'send_notification': instance.send_notification,
      'created_at':
          const TimestampNullableConverter().toJson(instance.created_at),
    };
