

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../../../constants/app_sizes.dart';
import '../../../utils/format.dart';

class PHSensor extends HookConsumerWidget{

  PHSensor({required this.sensorValue, required this.timeStamp});

  final double sensorValue;
  final int timeStamp;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text('PH Sensor'),
        ),
        _buildHeatMeter(context),
        gapH16,
        Text(Format.dateFromTimeStamp(timeStamp)),

      ],
    );
  }

  Widget _buildHeatMeter(BuildContext context) {
    return SfLinearGauge(
        maximum: 14,
        interval: 1,
        minorTicksPerInterval: 2,
        animateAxis: true,
        labelFormatterCallback: (String value) {
            return value;
        },
        axisTrackStyle: const LinearAxisTrackStyle(thickness: 1),
        barPointers: <LinearBarPointer>[
          LinearBarPointer(
              value: 14,
              thickness: 24,
              position: LinearElementPosition.outside,
              shaderCallback: (Rect bounds) {
                return const LinearGradient(colors: <Color>[
                  Colors.orange,
                  Colors.green,
                  Colors.blue
                ], stops: <double>[
                  0.1,
                  0.5,
                  0.8,
                ]).createShader(bounds);
              }),
        ],
        markerPointers: <LinearMarkerPointer>[
          LinearWidgetPointer(
              value: sensorValue,
              offset: 26,
              position: LinearElementPosition.outside,
              child: SizedBox(
                  width: 55,
                  height: 45,
                  child: Center(
                      child: Text(
                        sensorValue > 7.5 ? 'Basic' : sensorValue < 6.5 ? 'Acidic' : 'Neutral',
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: sensorValue < 6.5
                                ? Colors.orange
                                : sensorValue > 7.5
                                ? Colors.blue
                                : Colors.green),
                      )))),
          LinearShapePointer(
            offset: 25,
            onChanged: (dynamic value) {

            },
            value: sensorValue,
            color: sensorValue < 6.5
                ? Colors.orange
                : sensorValue > 7.5
                ? Colors.blue
                : Colors.green,
          ),
        ]);
  }

}