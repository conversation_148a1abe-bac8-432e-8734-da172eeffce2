import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/history/data_source/site_data_source.dart';
import 'package:si/kawosoti/schema/widgets/date_forward_backward.dart';

import '../../../datatable/data_table_2.dart';
import '../../../datatable/paginated_data_table_2.dart';


/// Route options are used to configure certain features of
/// the given example
String getCurrentRouteOption(BuildContext context) {
  var isEmpty = ModalRoute.of(context) != null &&
      ModalRoute.of(context)!.settings.arguments != null &&
      ModalRoute.of(context)!.settings.arguments is String
      ? ModalRoute.of(context)!.settings.arguments as String
      : '';

  return isEmpty;
}
// Route options
const dflt = 'Default';
const noData = 'No data';
const autoRows = 'Auto rows';
const showBordersWithZebraStripes = 'Borders with Zebra';
const custPager = 'Custom pager';
const defaultSorting = 'Default sorting';
const selectAllPage = 'Select all at page';
const rowTaps = 'Row Taps';
const rowHeightOverrides = 'Row height overrides';
const fixedColumnWidth = 'Fixed column width';


class SiteSevenTable extends StatefulHookConsumerWidget {

  const SiteSevenTable({this.userType = 1});

  final int userType;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SiteSevenTableState();
}

class _SiteSevenTableState extends ConsumerState<SiteSevenTable> {

  SiteDataSource? siteDataSource;
  final PaginatorController _controller = PaginatorController();
  int _rowsPerPage = PaginatedDataTable.defaultRowsPerPage;

  List<DataColumn> get _columns {
    return [
      DataColumn2(
        size: ColumnSize.S,
        label: const Text('S.N',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text('T1',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text('Cl',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text('Ph',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text('Oh',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),
      DataColumn2(
        size: ColumnSize.L,
        label: const Text('Borewell',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),

      DataColumn2(
        size: ColumnSize.L,
        label: const Text('Date',style: TextStyle(fontSize: 14,color : Colors.black),),
      ),

    ];
  }

  @override
  void didChangeDependencies() {
    siteDataSource = SiteDataSource(
        userType: widget.userType
    );
    siteDataSource?.SiteId  = "site7";
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          DateForwardBackward(onDateChange: (data){
            siteDataSource?.startDate = data;
            _controller.goToFirstPage();
          }),
          SizedBox(
            height: 20,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                AsyncPaginatedDataTable2(
                    showCheckboxColumn: false,
                    horizontalMargin: 20,
                    columnSpacing: 0,
                    wrapInCard: false,
                    rowsPerPage: 15,
                    minWidth: 800,
                    availableRowsPerPage: [15],
                    autoRowsToHeight:
                    getCurrentRouteOption(context) == autoRows,
                    // Default - do nothing, autoRows - goToLast, other - goToFirst
                    pageSyncApproach: getCurrentRouteOption(context) == dflt
                        ? PageSyncApproach.doNothing
                        : getCurrentRouteOption(context) == autoRows
                        ? PageSyncApproach.goToLast
                        : PageSyncApproach.goToFirst,
                    fit: FlexFit.tight,
                    initialFirstRowIndex: 0,
                    onRowsPerPageChanged: (value) {
                      // No need to wrap into setState, it will be called inside the widget
                      // and trigger rebuild
                      //setState(() {
                      print('Row per page changed to $value');
                      _rowsPerPage = value!;
                      //});
                    },
                    controller: _controller,

                    empty: Center(
                        child: Container(
                            padding: const EdgeInsets.all(20),
                            color: Colors.grey[200],
                            child: const Text('No data',style: TextStyle(fontSize: 18,color : Colors.white),))),
                    loading: Center(
                        child: CircularProgressIndicator(
                          color: Colors.blue,
                        )),
                    errorBuilder: (e) => Center(
                      child: Text(e.toString()),
                    ),
                    columns: _columns,
                    source: siteDataSource!),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
