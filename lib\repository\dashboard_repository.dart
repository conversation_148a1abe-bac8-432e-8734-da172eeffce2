

import 'package:firebase_auth/firebase_auth.dart';
import 'package:si/model/dashboard_model.dart';
import '../kawosoti/schema/model/user_model.dart';
import '../model/setting_model.dart';
import '../services/firestore_path.dart';
import '../services/firestore_services.dart';

abstract class IDashboardRepository {
  Stream<DashboardModel?> getDashboard(String site);
  Stream<UserModel?> getUser();
  Future<bool> setSetting(String site, SettingModel setting);
}

class DashboardRepository extends IDashboardRepository{

  final dbInstance = FirestoreService.instance;

  @override
  Stream<DashboardModel?> getDashboard(String site)=> dbInstance.documentStream<DashboardModel?>(
    path: "9216/${site}",
    builder: (data, documentID) => DashboardModel.fromJson(data!
      ..addAll({
        'id': documentID,
      })),
  );

  @override
  Stream<UserModel?> getUser()=> dbInstance.documentStream<UserModel?>(
    path: FirestorePath.users+"/${FirebaseAuth.instance.currentUser?.uid}",
    builder: (data, documentID) => UserModel.fromJson(data!
      ..addAll({
        'id': documentID,
      })),
  );

  @override
  Future<bool> setSetting(String site, SettingModel setting) {
    // TODO: implement setSetting
    throw UnimplementedError();
  }

}