

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/settings/label_text.dart';
import 'package:si/provider/dashboard_provider.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../constants/assets_manager.dart';
import '../../provider/auth_provider.dart';
import '../model/motor_setting_model.dart';


/*class TankMotorSettingPage extends HookConsumerWidget {
  TankMotorSettingPage(this.divisionSettingModel, this.tankId, this.motorId,this.userRole);

  final DivisionSettingModel? divisionSettingModel;
  final String? tankId;
  final String? motorId;
  final int userRole;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  Future<void> _handleTankSetting(
      BuildContext context,
      WidgetRef ref, {
        required TextEditingController actualHeightController,
        required TextEditingController maxHeightController,
        required TextEditingController minHeightContoller,
        required TextEditingController tankNameController,
        required TextEditingController siteNameController,
        required TextEditingController maxCurrentController,
        required TextEditingController minCurrentController,
        required TextEditingController motorNameController,
        required bool floatEnable,
        required bool levelSensorEnable,
        required bool isActive,
      }) async {

    final repo = ref.read(divisionRepositoryProvider);
    final rtdbrepo = ref.read(divisionRtdbServiceProvider);
    if (_formKey.currentState != null) {
      if (_formKey.currentState!.validate()) {
        // final email = emailController.text;
        // final password = passwordController.text;
        // Logger().d('Email : $email \nPassword: $password');
        try {
          EasyLoading.show(status: 'Updaing..');
          final division = divisionSettingModel!.copyWith(
            name: siteNameController.text.trim().toString(),
            tank10: divisionSettingModel!.tank10!.copyWith(
              name: tankNameController.text.trim().toString(),
              actual_height: int.parse(actualHeightController.text.trim().toString()),
              max_height: int.parse(maxHeightController.text.trim().toString()),
              min_height: int.parse(minHeightContoller.text.trim().toString()),
              float_enable: floatEnable,
              level_sensor_enable: levelSensorEnable,
              is_active: isActive,
            ),
            motor10:  MotorSettingModel(
              is_active: true,
              name: motorNameController.text.trim().toString(),
              overload_current: double.parse(maxCurrentController.text.trim().toString()),
              no_load_current: double.parse(minCurrentController.text.trim().toString()),
            ),
          );

          await repo.setDivisionSetting(division);
          await rtdbrepo.updateMotorParameter("motor1", double.parse(minCurrentController.text.trim().toString()),
              double.parse(maxCurrentController.text.trim().toString()));
          await rtdbrepo.updateTankParameter("tank1", int.parse(maxHeightController.text.trim().toString()),
              int.parse(actualHeightController.text.trim().toString()));

          EasyLoading.showSuccess(' Updated');

          Navigator.pop(context,true);



        } catch (e) {
          EasyLoading.showError('Error');
          //Logger().e('create user from email', e);
        }
        FocusScope.of(context).unfocus();
      } else {
        debugPrint("login validation failed");
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actualHeightController = useTextEditingController();
    final tankNameController = useTextEditingController();
    final siteNameController = useTextEditingController();
    final minHeightController = useTextEditingController();
    final maxHeightController = useTextEditingController();
    final motorNameController = useTextEditingController();
    final maxCurrentController = useTextEditingController();
    final minCurrentController = useTextEditingController();
    final floatEnable = useRef<bool>(false);
    final activeEnable = useRef<bool>(false);
    final sensorEnable = useRef<bool>(false);

    useEffect(() {
      // print('SessionDEt : ${sessionModel.id}');
      actualHeightController.text = divisionSettingModel!.tank10!.actual_height.toString();
      tankNameController.text = divisionSettingModel!.tank10!.name;
      siteNameController.text = divisionSettingModel!.name;
      minHeightController.text = divisionSettingModel!.tank10!.min_height.toString();
      maxHeightController.text = divisionSettingModel!.tank10!.max_height.toString();
      motorNameController.text = divisionSettingModel!.motor10!.name;
      maxCurrentController.text = divisionSettingModel!.motor10!.overload_current.toString();
      minCurrentController.text = divisionSettingModel!.motor10!.no_load_current.toString();
      floatEnable.value = divisionSettingModel!.tank10!.float_enable;
      sensorEnable.value = divisionSettingModel!.tank10!.level_sensor_enable;
      activeEnable.value = divisionSettingModel!.tank10!.is_active;
      return null;
    }, const []);

    return BaseScaffold(
        showAppBar: true,
        appbarText: "Tank Motor Setting",
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [

                      _buildTextFormFieldSection(context, ref,
                          siteNameController,
                          actualHeightController,
                          minHeightController,
                          maxHeightController,
                          tankNameController,
                      motorNameController,
                          maxCurrentController,
                          minCurrentController),
                      HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                        final floatSwitch = useState<bool>(floatEnable.value);
                        return ListTile(
                          title: const Text('Float Switch'),
                          trailing: CupertinoSwitch(
                            value: floatSwitch.value,
                            onChanged: (bool value) {
                              floatSwitch.value = value;
                              floatEnable.value = floatSwitch.value;
                            },
                          ),
                          onTap: () {
                            floatSwitch.value = !floatSwitch.value;
                            floatEnable.value = floatSwitch.value;
                          },
                        );
                      }),
                      HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                        final levelSwitch = useState<bool>(sensorEnable.value);
                        return ListTile(
                          title: const Text('Level sensor'),
                          trailing: CupertinoSwitch(
                            value: levelSwitch.value,
                            onChanged: (bool value) {
                              levelSwitch.value = value;
                              sensorEnable.value = levelSwitch.value;
                            },
                          ),
                          onTap: () {
                            levelSwitch.value = !levelSwitch.value;
                            sensorEnable.value = levelSwitch.value;
                          },
                        );
                      }),
                      HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                        final activeSwitch = useState<bool>(activeEnable.value);
                        return ListTile(
                          title: const Text('Active'),
                          trailing: CupertinoSwitch(
                            value: activeSwitch.value,
                            onChanged: (bool value) {
                              activeSwitch.value = value;
                              activeEnable.value = activeSwitch.value;
                            },
                          ),
                          onTap: () {
                            activeSwitch.value = !activeSwitch.value;
                            activeEnable.value = activeSwitch.value;
                          },
                        );
                      }),

                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 48.0, vertical: 24),
                        child: PrimaryButton(
                          text: 'Update',
                          height: 54,
                          onPressed: () async {
                            _handleTankSetting(context, ref,
                                actualHeightController: actualHeightController,
                                maxHeightController: maxHeightController,
                                minHeightContoller: minHeightController,
                                floatEnable: floatEnable.value,
                                levelSensorEnable: sensorEnable.value,
                                siteNameController: siteNameController,
                                tankNameController: tankNameController,
                                motorNameController: motorNameController,
                                maxCurrentController: maxCurrentController,
                                minCurrentController: minCurrentController,
                                isActive: activeEnable.value);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          ],
        ),

    );
  }

  Widget _buildTextFormFieldSection(
      BuildContext context,
      WidgetRef ref,
      TextEditingController siteNameController,
      TextEditingController actualHeightController,
      TextEditingController minHeightController,
      TextEditingController maxHeightController,
      TextEditingController tanknameController,
      TextEditingController motornameController,
      TextEditingController maxCurrentController,
      TextEditingController minCurrentController,
      ) {
    // final FocusNode? pwdFocusNode = FocusNode();

    // EdgeFunctionController edgeFunctionController = EdgeFunctionController();

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          gapH16,
          LabelText(label: "Enter site name"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "siteName",
                controller: siteNameController,
                hintText: "Site Name",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
                prefixIcon: const Icon(
                  Icons.home_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter tank name"),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "tankName",
              controller: tanknameController,
              hintText: "Tank Name",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.name,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
          ),
          LabelText(label: "Enter actual height"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "actualHeight",
                controller: actualHeightController,
                hintText: "Actual Height",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter minimum height"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "minHeight",
                controller: minHeightController,
                hintText: "Minimum Height",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter maximum height"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "maxHeight",
                controller: maxHeightController,
                hintText: "Maximum Height",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.propane_tank_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter motor name"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "motorName",
                controller: motornameController,
                hintText: "Motor Name",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
                prefixIcon: const Icon(
                  Icons.heat_pump_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter maximum current"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "maxCurrent",
                controller: maxCurrentController,
                hintText: "Max Current",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.amp_stories_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
          LabelText(label: "Enter minimum current"),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
              child: CustomTextFormField(
                name: "minCurrent",
                controller: minCurrentController,
                hintText: "Minimum Current",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(
                  Icons.amp_stories_outlined,
                  color: Colors.grey,
                  size: 18,
                ),
              )
          ),
        ],
      ),
    );
  }
}*/


