
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/constants/style_manager.dart';

import '../../common/widgets/app_icon_widget.dart';
import '../../common/widgets/base_scaffold.dart';
import '../../constants/app_sizes.dart';
import '../../login/ui/login_ui.dart';
import '../../utils/app_colors.dart';
import '../../utils/breakpoints.dart';
import 'signup_text_form_field.dart';


class SignUpPage extends HookConsumerWidget {
  const SignUpPage({super.key, this.fromOnboarding = false});

  final bool fromOnboarding;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useEffect(() {
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(statusBarColor: Colors.black));

      return () => {};
    }, [

    ]);


    return BaseScaffold(
      showAppBar: false,
      child: Center(
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding:  EdgeInsets.symmetric(horizontal: sliverTabletHorizontalPadding(MediaQuery.of(context).size.width)),
            child: Container(
              padding: horPadding8,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  gapH32,
                  const AppIconWidget(imageSize: Size(150, 150)),
                  gapH16,
                  const SignupTextFormField(),
                  gapH16,
                  _buildBottomRichText(context, ref),
                  gapH16
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomRichText(BuildContext context, ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            recognizer: TapGestureRecognizer()..onTap = () {},
            text: "Already a user? ",
            style: TextStyle(fontSize: 16,color: Colors.grey),
            children: [
              TextSpan(
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                        context.pop();
                    },
                  text: "Login",
                  style: context.underLineSubTitleText.copyWith(color: AppColors.themeColor,fontSize: 16)),
            ],
          ),
        ),
      ],
    );
  }
}
