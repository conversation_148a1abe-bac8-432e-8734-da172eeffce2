

import 'package:freezed_annotation/freezed_annotation.dart';
part 'state_model.freezed.dart';

@freezed
abstract class Resource<T> with _$Resource<T>{
  const factory Resource.success(T? data,String? message) = _ResourceData;
  const factory Resource.unInitialized() = _ResourceUnInitiazed;
  const factory Resource.error(String? errorText) = _ResourceError;
  const factory Resource.unauthorized() = _ResourceUnauthorized;
  const factory Resource.loading() = _ResourceLoading;

}