// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SettingModelImpl _$$SettingModelImplFromJson(Map<String, dynamic> json) =>
    _$SettingModelImpl(
      maxHeight: (json['maxHeight'] as num?)?.toInt(),
      minHeight: (json['minHeight'] as num?)?.toInt(),
      maxAmps: (json['maxAmps'] as num?)?.toDouble(),
      minAmps: (json['minAmps'] as num?)?.toDouble(),
      actualHeight: (json['actualHeight'] as num?)?.toInt(),
      timer: (json['timer'] as num?)?.toInt(),
      mode: (json['mode'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SettingModelImplToJson(_$SettingModelImpl instance) =>
    <String, dynamic>{
      'maxHeight': instance.maxHeight,
      'minHeight': instance.minHeight,
      'maxAmps': instance.maxAmps,
      'minAmps': instance.minAmps,
      'actualHeight': instance.actualHeight,
      'timer': instance.timer,
      'mode': instance.mode,
    };
