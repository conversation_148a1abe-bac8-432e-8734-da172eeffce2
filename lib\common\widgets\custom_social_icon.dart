import 'package:flutter/material.dart';

class CustomSocialIcon extends StatelessWidget {
  const CustomSocialIcon({
    Key? key,
    this.iconData,
    this.iconSize,
    this.backgroundColor,
    this.iconColor,
    this.onTap,
    this.assetImagePath,
  }) : super(key: key);

  final IconData? iconData;
  final double? iconSize;
  final Color? backgroundColor, iconColor;
  final GestureTapCallback? onTap;
  final String? assetImagePath;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      customBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      onTap: onTap ?? () {},
      child: CircleAvatar(
        radius: 24,
        backgroundColor: backgroundColor ?? Colors.grey.withOpacity(0.3),
        backgroundImage: assetImagePath != null ? AssetImage(assetImagePath!) : null,
        child: assetImagePath == null
            ? Icon(
                iconData,
                size: 20,
                color: iconColor ?? Colors.black,
              )
            : null,
      ),
    );
  }
}
