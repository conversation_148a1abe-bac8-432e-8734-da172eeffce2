

import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../../../utils/format.dart';

class ChlorineSensor extends HookConsumerWidget{

  ChlorineSensor({required this.sensorValue, required this.timeStamp});

  final double sensorValue;
  final int timeStamp;

  @override
  Widget build(BuildContext context, WidgetRef ref) {


    return  Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text('Chlorine'),
        ),
        Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              SizedBox(
                  width: MediaQuery.of(context).size.width >= 550
                      ? 300
                      : MediaQuery.of(context).size.width * 0.48,
                  child: SfLinearGauge(
                      minimum: 0,
                      maximum: 0.8,
                      interval: 0.05,
                      animateAxis: true,
                      animateRange: true,
                      showLabels: false,
                      showTicks: false,
                      minorTicksPerInterval: 0,
                      axisTrackStyle: LinearAxisTrackStyle(
                        thickness: 15,
                        color: Colors.grey[350],
                      ),
                      markerPointers: <LinearMarkerPointer>[
                        LinearShapePointer(
                            value: sensorValue>0.8 ? 0.8 : sensorValue,
                            onChanged: (dynamic value) {

                            },
                            height: 20,
                            width: 20,
                            color: sensorValue < 0.2
                                ? Colors.amber
                                : sensorValue >0.5
                                ? Colors.amber
                                : Colors.green,
                            position: LinearElementPosition.cross,
                            shapeType: LinearShapePointerType.circle),
                        const LinearWidgetPointer(
                          value: 0.1,
                          enableAnimation: false,
                          position: LinearElementPosition.outside,
                          offset: 4,
                          child: Text(
                            'Average',
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const LinearWidgetPointer(
                          value: 0.35,
                          enableAnimation: false,
                          position: LinearElementPosition.outside,
                          offset: 4,
                          child: Text(
                            'Fair',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                        const LinearWidgetPointer(
                          value: 0.65,
                          enableAnimation: false,
                          position: LinearElementPosition.outside,
                          offset: 4,
                          child: Text(
                            'Average',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),

                      ],
                      ranges: const <LinearGaugeRange>[
                        LinearGaugeRange(
                          startValue: 0,
                          endValue: 0.2,
                          startWidth: 8,
                          midWidth: 8,
                          endWidth: 8,
                          position: LinearElementPosition.cross,
                          color: Colors.amber,
                        ),
                        LinearGaugeRange(
                          startValue: 0.2,
                          endValue: 0.5,
                          startWidth: 8,
                          position: LinearElementPosition.cross,
                          midWidth: 8,
                          endWidth: 8,
                          color: Colors.green,
                        ),
                        LinearGaugeRange(
                          startValue: 0.5,
                          endValue: 0.8,
                          position: LinearElementPosition.cross,
                          startWidth: 8,
                          midWidth: 8,
                          endWidth: 8,
                          color: Colors.amber,
                        ),
                      ])),
              const SizedBox(height: 8),

            ],
          ),
        ),
        Text(Format.dateFromTimeStamp(timeStamp)),
      ],
    );
  }

}