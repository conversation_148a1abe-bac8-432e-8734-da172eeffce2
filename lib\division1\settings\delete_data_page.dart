import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_text_form_field.dart';
import 'package:si/common/widgets/primary_button.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:si/utils/app_colors.dart';
import 'package:si/utils/format.dart';

class DeleteDataPage extends HookConsumerWidget {
  const DeleteDataPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize dates - default to a one-week range ending at one month ago
    final now = DateTime.now();
    final oneMonthAgo = DateTime(now.year, now.month - 1, now.day);
    final oneWeekBeforeOneMonthAgo =
        DateTime(oneMonthAgo.year, oneMonthAgo.month, oneMonthAgo.day - 7);
    final startDate = useState<DateTime>(
        oneWeekBeforeOneMonthAgo); // One week before one month ago
    final endDate =
        useState<DateTime>(oneMonthAgo); // End date is one month ago
    final dataCount = useState<int>(0);
    final isLoading = useState<bool>(false);
    final isCountLoading = useState<bool>(false);
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final collectionController = useTextEditingController();
    final siteIdController = useTextEditingController();
    final fieldType = useState<String>("date"); // "date" or "timestamp"
    final filterBySiteId = useState<bool>(false);

    // Initialize collection name from preferences
    useEffect(() {
      collectionController.text = prefs.getSiteId();
      siteIdController.text = prefs.getSiteId();
      return null;
    }, const []);

    // Function to count documents in the selected date range
    Future<void> countDocuments() async {
      if (collectionController.text.isEmpty) {
        EasyLoading.showError('Please enter a collection name');
        return;
      }

      if (filterBySiteId.value && siteIdController.text.isEmpty) {
        EasyLoading.showError('Please enter a site ID');
        return;
      }

      isCountLoading.value = true;

      try {
        final beforeDay = DateTime(startDate.value.year, startDate.value.month,
            startDate.value.day, 0, 0, 0);
        final afterDay = DateTime(endDate.value.year, endDate.value.month,
            endDate.value.day, 23, 59, 59);

        Query query =
            FirebaseFirestore.instance.collection(collectionController.text);

        // Apply different query based on field type
        if (fieldType.value == "date") {
          // For Date type fields
          query = query
              .where('time', isGreaterThanOrEqualTo: beforeDay)
              .where('time', isLessThanOrEqualTo: afterDay);
        } else {
          // For Timestamp type fields (stored as milliseconds)
          query = query
              .where('time',
                  isGreaterThanOrEqualTo:
                      beforeDay.millisecondsSinceEpoch / 1000)
              .where('time',
                  isLessThanOrEqualTo: afterDay.millisecondsSinceEpoch / 1000);
        }

        // Add siteId filter if enabled
        if (filterBySiteId.value && siteIdController.text.isNotEmpty) {
          query = query.where('siteId', isEqualTo: siteIdController.text);
        }

        final snapshot = await query.count().get();
        dataCount.value = snapshot.count ?? 0;

        // Log the count for debugging
        EasyLoading.showInfo('Count query found ${dataCount.value} documents');
      } catch (e) {
        EasyLoading.showError('Error counting documents: $e');
      } finally {
        isCountLoading.value = false;
      }
    }

    // Function to delete documents in the selected date range
    Future<void> deleteDocuments() async {
      if (collectionController.text.isEmpty) {
        EasyLoading.showError('Please enter a collection name');
        return;
      }

      if (filterBySiteId.value && siteIdController.text.isEmpty) {
        EasyLoading.showError('Please enter a site ID');
        return;
      }

      if (dataCount.value == 0) {
        EasyLoading.showError('No data to delete');
        return;
      }

      // Show confirmation dialog
      final shouldDelete = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Confirm Delete'),
          content: Text(
              'Are you sure you want to delete ${dataCount.value} documents? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (shouldDelete != true) return;

      isLoading.value = true;
      EasyLoading.show(status: 'Deleting data...');

      try {
        final beforeDay = DateTime(startDate.value.year, startDate.value.month,
            startDate.value.day, 0, 0, 0);
        final afterDay = DateTime(endDate.value.year, endDate.value.month,
            endDate.value.day, 23, 59, 59);

        // Get documents in the date range
        Query query =
            FirebaseFirestore.instance.collection(collectionController.text);

        // Apply different query based on field type
        if (fieldType.value == "date") {
          // For Date type fields
          query = query
              .where('time', isGreaterThanOrEqualTo: beforeDay)
              .where('time', isLessThanOrEqualTo: afterDay);
        } else {
          // For Timestamp type fields (stored as milliseconds)
          query = query
              .where('time',
                  isGreaterThanOrEqualTo:
                      beforeDay.millisecondsSinceEpoch / 1000)
              .where('time',
                  isLessThanOrEqualTo: afterDay.millisecondsSinceEpoch / 1000);
        }

        // Add siteId filter if enabled
        if (filterBySiteId.value && siteIdController.text.isNotEmpty) {
          query = query.where('siteId', isEqualTo: siteIdController.text);
        }

        final querySnapshot = await query.get();

        // Log the number of documents found
        EasyLoading.showInfo(
            'Found ${querySnapshot.docs.length} documents to delete');

        // Delete documents in batches (Firestore has a limit of 500 operations per batch)
        int deleted = 0;
        const batchSize = 450; // Leave some room for other operations

        for (var i = 0; i < querySnapshot.docs.length; i += batchSize) {
          final batch = FirebaseFirestore.instance.batch();
          final end = (i + batchSize < querySnapshot.docs.length)
              ? i + batchSize
              : querySnapshot.docs.length;

          for (var j = i; j < end; j++) {
            batch.delete(querySnapshot.docs[j].reference);
            deleted++;
          }

          await batch.commit();
          EasyLoading.showProgress(deleted / querySnapshot.docs.length,
              status: 'Deleted $deleted of ${querySnapshot.docs.length}');
        }

        EasyLoading.showSuccess('Successfully deleted $deleted documents');
        dataCount.value = 0;
      } catch (e) {
        EasyLoading.showError('Error deleting documents: $e');
      } finally {
        isLoading.value = false;
      }
    }

    return BaseScaffold(
      appbarText: 'Delete Data',
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Collection name input
              const Text('Collection Name',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: collectionController,
                hintText: 'Enter collection name',
                textInputAction: TextInputAction.next,
                name: 'collection',
              ),
              const SizedBox(height: 24),

              // Site ID filter
              Row(
                children: [
                  Checkbox(
                    value: filterBySiteId.value,
                    onChanged: (value) {
                      if (value != null) {
                        filterBySiteId.value = value;
                        dataCount.value = 0; // Reset count when filter changes
                      }
                    },
                  ),
                  const Text('Filter by Site ID',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ],
              ),
              if (filterBySiteId.value) ...[
                const SizedBox(height: 8),
                CustomTextFormField(
                  controller: siteIdController,
                  hintText: 'Enter site ID',
                  textInputAction: TextInputAction.next,
                  name: 'siteId',
                ),
                const SizedBox(height: 24),
              ],

              // Field type selection
              const Text('Field Type',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    isExpanded: true,
                    value: fieldType.value,
                    onChanged: (value) {
                      if (value != null) {
                        fieldType.value = value;
                        dataCount.value =
                            0; // Reset count when field type changes
                      }
                    },
                    items: const [
                      DropdownMenuItem(
                        value: "date",
                        child: Text("Date (DateTime)"),
                      ),
                      DropdownMenuItem(
                        value: "timestamp",
                        child: Text("Timestamp (milliseconds)"),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Date range selection
              const Text('Date Range',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        // Allow any date in the past, but restrict max date to one month ago
                        final now = DateTime.now();
                        final oneMonthAgo =
                            DateTime(now.year, now.month - 1, now.day);
                        final pickedDate = await showDatePicker(
                          context: context,
                          initialDate: startDate.value.isBefore(oneMonthAgo)
                              ? startDate.value
                              : oneMonthAgo,
                          firstDate: DateTime(2000), // Allow very old dates
                          lastDate: oneMonthAgo, // Restrict to one month ago
                        );
                        if (pickedDate != null) {
                          startDate.value = pickedDate;
                          dataCount.value = 0; // Reset count when date changes
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(Format.onlydate(startDate.value)),
                            const Icon(Icons.calendar_today, size: 16),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text('to',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        // Allow any date in the past, but restrict max date to one month ago
                        final now = DateTime.now();
                        final oneMonthAgo =
                            DateTime(now.year, now.month - 1, now.day);
                        final pickedDate = await showDatePicker(
                          context: context,
                          initialDate: endDate.value.isBefore(oneMonthAgo)
                              ? endDate.value
                              : oneMonthAgo,
                          firstDate: startDate
                              .value, // Start from the selected start date
                          lastDate: oneMonthAgo, // Restrict to one month ago
                        );
                        if (pickedDate != null) {
                          endDate.value = pickedDate;
                          dataCount.value = 0; // Reset count when date changes
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(Format.onlydate(endDate.value)),
                            const Icon(Icons.calendar_today, size: 16),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Count button
              Center(
                child: PrimaryButton(
                  text: 'Count Data',
                  isLoading: isCountLoading.value,
                  onPressed: countDocuments,
                ),
              ),
              const SizedBox(height: 24),

              // Data count display
              if (dataCount.value > 0)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Found ${dataCount.value} records',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      PrimaryButton(
                        text: 'Delete Data',
                        isLoading: isLoading.value,
                        buttonPrimaryColor: Colors.red,
                        buttonSecondaryColor: Colors.red,
                        onPressed: deleteDocuments,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
