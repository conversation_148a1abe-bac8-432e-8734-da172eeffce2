
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CustomCachedNetworkImageWidget extends ConsumerWidget {
  const CustomCachedNetworkImageWidget({
    Key? key,
    this.boxFit,
    this.height,
    this.width,
    required this.imageUrl,
    this.color,
    this.blendMode,
    this.cacheImageHeightWidth,
    this.isProfile = false,
  }) : super(key: key);

  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? boxFit;
  final int? cacheImageHeightWidth;
  final Color? color;
  final BlendMode? blendMode;
  final bool isProfile;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CachedNetworkImage(
      color: color,
      imageUrl: imageUrl,
      height: height,
      width: width,
      fit: boxFit,
      memCacheWidth: cacheImageHeightWidth,
      memCacheHeight: cacheImageHeightWidth,
      maxWidthDiskCache: cacheImageHeightWidth,
      maxHeightDiskCache: cacheImageHeightWidth,
      progressIndicatorBuilder: (context, url, downloadProgress) => SizedBox(
        height: height ?? 200,
        width: width ?? 200,
        child: Center(
          child: CircularProgressIndicator(
            value: downloadProgress.progress,
          ),
        ),
      ),

      errorWidget: (context, url, error) => const Center(child: Icon(Icons.error)),
    );
  }
}
