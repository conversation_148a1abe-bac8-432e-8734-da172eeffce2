import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:si/model/dashboard_model.dart';

import '../../constants/app_sizes.dart';
import '../../utils/format.dart';
import 'motor_moving.dart';

class Display extends StatelessWidget {

  Display(this.dashboardModel,this.site);
  final DashboardModel dashboardModel;
  final String site;


  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16),
        child: Column(
          children: [
            Container(
              width: 120,
              height: 150,

              decoration: BoxDecoration(
                color: Colors.grey,
                border: Border.all(color: Colors.blueGrey, width: 2),

                borderRadius: BorderRadius.all(Radius.circular(14)),
              ),
              child: Column(
                children: [
                  Container(
                    width: 120,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.green,

                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(14),
                          topRight: Radius.circular(14)),
                    ),
                    child:Center(child: Text((dashboardModel.current ?? 0.00).toString() + " A", style: TextStyle(color: Colors.white, fontSize: 20),)),
                  ),
                  Expanded(
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                       children: [
                         Column(
                           children: [
                             Container(
                               height: 15,
                               width: 15,
                               margin: EdgeInsets.all(10.0),
                               decoration: BoxDecoration(
                                   color: Colors.black,
                                   shape: BoxShape.circle
                               ),),
                             Text("O",style: TextStyle(color: Colors.black, fontSize: 20),)
                           ],
                         ),
                         Column(
                           children: [
                             Container(
                               height: 15,
                               width: 15,
                               margin: EdgeInsets.all(10.0),
                               decoration: BoxDecoration(
                                   color: Colors.black,
                                   shape: BoxShape.circle
                               ),),
                             Text("U",style: TextStyle(color: Colors.black, fontSize: 20),)

                           ],
                         ),
                         Column(
                           children: [
                             Container(
                               height: 15,
                               width: 15,
                               margin: EdgeInsets.all(10.0),
                               decoration: BoxDecoration(
                                   color: Colors.green,
                                   shape: BoxShape.circle
                               ),),
                             Text("N",style: TextStyle(color: Colors.black, fontSize: 20),)

                           ],
                         ),
                       ],

                      ),
                    ),
                  )
                ],
              ),
            ),
            gapH16,

            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Text(
                Format.date(dashboardModel.current_updated_at!),
                style: TextStyle(color: Colors.black),
              ),
            ),
            gapH16,

          ],
        ));
  }
}
