


import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/constants/app_sizes.dart';

class FlowReadingBox extends HookConsumerWidget{

  const FlowReadingBox(this.reading, this.motorState);
  final String reading;
  final bool motorState;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
   return Padding(
     padding: horPadding2,
     child: Container(
       decoration: BoxDecoration(
         border: Border.all(color: motorState ?  Colors.green : Colors.black)
       ),
       height: 20, width: 20,
       child: Center(child: Text(reading, style: TextStyle(color: motorState ? Colors.green : Colors.black),)),
     ),
   );
  }

}