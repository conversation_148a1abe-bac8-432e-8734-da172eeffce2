import 'package:flutter/services.dart';

/// This file contains some helper functions used for string validation.

abstract class StringValidator {
  bool isValid(String value);
}

class RegexValidator implements StringValidator {
  RegexValidator({required this.regexSource});
  final String regexSource;

  @override
  bool isValid(String value) {
    try {
      // https://regex101.com/
      final RegExp regex = RegExp(regexSource);
      final Iterable<Match> matches = regex.allMatches(value);
      for (final match in matches) {
        if (match.start == 0 && match.end == value.length) {
          return true;
        }
      }
      return false;
    } catch (e) {
      // Invalid regex
      assert(false, e.toString());
      return true;
    }
  }
}

class RegexValidatorForPassword implements StringValidator {
  RegexValidatorForPassword({required this.regexSource});
  final String regexSource;

  @override
  bool isValid(String value) {
    try {
      // https://regex101.com/
      final RegExp regex = RegExp(regexSource);

      final bool hasMatched = regex.hasMatch(value);
      // print('hasMatched: $hasMatched');
      return hasMatched;
    } catch (e) {
      // Invalid regex
      assert(false, e.toString());
      return true;
    }
  }
}

class ValidatorInputFormatter implements TextInputFormatter {
  ValidatorInputFormatter({required this.editingValidator});
  final StringValidator editingValidator;

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    final bool oldValueValid = editingValidator.isValid(oldValue.text);
    final bool newValueValid = editingValidator.isValid(newValue.text);
    if (oldValueValid && !newValueValid) {
      return oldValue;
    }
    return newValue;
  }
}

class EmailEditingRegexValidator extends RegexValidator {
  EmailEditingRegexValidator() : super(regexSource: '^(|\\S)+\$');
}

class EmailSubmitRegexValidator extends RegexValidator {
  // EmailSubmitRegexValidator() : super(regexSource: '^\\S+@\\S+\\.\\S+\$');
  EmailSubmitRegexValidator()
      : super(
          regexSource:
               r"^([\w.+-]+)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$",
              //r"^([A-Za-z][\w.+-]+)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-zA-Z]{2,6}(?:\.[a-zA-Z]{2})?)$",
        );
}

class PasswordSubmitRegexValidatorForSpecialCase extends RegexValidatorForPassword {
  PasswordSubmitRegexValidatorForSpecialCase() : super(regexSource: r'^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])((?=.*\W)|(?=.*_))^[^ ]+$');
}

class NonEmptyStringValidator extends StringValidator {
  @override
  bool isValid(String value) {
    return true;
  }
}

class MinLengthStringValidator extends StringValidator {
  MinLengthStringValidator(this.minLength);
  final int minLength;

  @override
  bool isValid(String value) {
    return value.length >= minLength;
  }
}


class MinMaxLengthStringValidator extends StringValidator {
  MinMaxLengthStringValidator(this.minLength, this.maxLength);
  final int minLength;
  final int maxLength;

  @override
  bool isValid(String value) {
    return value.length >= minLength && value.length<=maxLength;
  }
}