// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
              'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
              'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
              'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }



  static const FirebaseOptions web = FirebaseOptions(
      apiKey: "AIzaSyB4GuemuR2BZXAxIndb_eyvlIQbZjX8wtE",
      authDomain: "madhesh-swodeshi-controller.firebaseapp.com",
      databaseURL: "https://madhesh-swodeshi-controller-default-rtdb.asia-southeast1.firebasedatabase.app",
      projectId: "madhesh-swodeshi-controller",
      storageBucket: "madhesh-swodeshi-controller.firebasestorage.app",
      messagingSenderId: "385091920908",
      appId: "1:385091920908:web:101a4a1b325082756dba29",
      measurementId: "G-XGRNR7RH0C"
  );

  static const FirebaseOptions sandboxWeb = FirebaseOptions(
      apiKey: "AIzaSyBMIRjkLIdtm4D7mlFy14zRbjvytWBKDm8",
      authDomain: "sandbox-environment-9f08b.firebaseapp.com",
      databaseURL: "https://sandbox-environment-9f08b-default-rtdb.asia-southeast1.firebasedatabase.app",
      projectId: "sandbox-environment-9f08b",
      storageBucket: "sandbox-environment-9f08b.appspot.com",
      messagingSenderId: "840991021702",
      appId: "1:840991021702:web:df41226d0b60f1d9510f07",
      measurementId: "G-1YX4CF49QJ"
  );

  static const FirebaseOptions talkhuweb = FirebaseOptions(
      apiKey: "AIzaSyB9uAIFmOcz1nD8QIFF5D0NnkERuqeeJTw",
      authDomain: "scada-khanepani.firebaseapp.com",
      databaseURL: "https://scada-khanepani-default-rtdb.asia-southeast1.firebasedatabase.app",
      projectId: "scada-khanepani",
      storageBucket: "scada-khanepani.firebasestorage.app",
      messagingSenderId: "374070421349",
      appId: "1:374070421349:web:ddf2c8093ec7b4f72ae38c",
      measurementId: "G-84KWN96EMR"

  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDzE7yQBZDHXnFVez8KwXOpccFU2Znn_DM',
    appId: '1:452686431502:android:24e7214ccadf62eefe435f',
    messagingSenderId: '452686431502',
    projectId: 'nicole-monroe-sandbox',
    databaseURL: 'https://nicole-monroe-sandbox-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'nicole-monroe-sandbox.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDAQvfBXI997ZEk-Lj060GcQJXqzD3pTK8',
    appId: '1:452686431502:ios:4e7f0bfbbbe6a96efe435f',
    messagingSenderId: '452686431502',
    projectId: 'nicole-monroe-sandbox',
    databaseURL: 'https://nicole-monroe-sandbox-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'nicole-monroe-sandbox.appspot.com',
    androidClientId: '452686431502-1v55rkqkth1a0s7ke9lmehi1l512sr3b.apps.googleusercontent.com',
    iosClientId: '452686431502-7sntsl13744jofheorajrvjev1as1eq6.apps.googleusercontent.com',
    iosBundleId: 'fit.remotecoach.nicolemonroedev',
  );
}