


import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';

class MobileSite11Page extends HookConsumerWidget {

  final DivisionSettingModel? setting;

  const MobileSite11Page(this.setting);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final motor10State = useState<MotorModel>(MotorModel());



    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Container(
                  height: 60,
                  width: double.infinity,
                  color: Colors.blueAccent,
                  child: Center(
                    child: Text(
                      "मुलको पानी",
                      style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  )
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 40,
                    width: 30,
                    color: Colors.blueAccent,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0, left: 8, right: 0),
                    child: NewMotorUnit(ratio: 2,motorId: "10",
                      motorSettingModel: setting!.motor10!,
                      motorModel: motor10State.value,
                    ),
                  ),
                  Container(
                    height: 40,
                    width: 30,
                    color: Colors.blueAccent,
                  )


                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: NewTankUnit(3, "11",
                    setting!.tank11!, 180, double.infinity),
              ),
              Container(
                height: 40,
                width: 30,
                color: Colors.blueAccent,
              ),
              Container(
                height: 60,
                width: double.infinity,
                color: Colors.blueAccent,
                child: const Center(
                  child: Text(
                    "वितरण",
                    style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
              ),


            ],
          ),
        ),
      ],
    );





  }
}