class AppDimension {
  double grassesY({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) + heightMotor(x: x, y: y);
  }

  double t1X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        24;
  }

  double t1Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y);
  }

  double l5Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2;
  }

  double l5X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) -
        3;
  }

  double l6Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 -
        L6Pipelength(x: x, y: y);
  }

  double l6X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) -
        3;
  }

  double m1StartingPointX({
    required double x,
    required double y,
  }) {
    if (x < 1600) {
      return 140.0;
    } else {
      return 220.0;
    }
  }

  double m2StartingPointX({
    required double x,
    required double y,
  }) {
    if (x < 1600) {
      return 140.0;
    } else {
      return 220.0;
    }
  }

  double m1StartingPointY({
    required double x,
    required double y,
  }) {
    if (y < 800) {
      return 130.0;
    } else if (y < 900) {
      return 160.0;
    } else {
      return 190.0;
    }
  }

  double m2StartingPointY({
    required double x,
    required double y,
  }) {
    if (y < 800) {
      return 130.0;
    } else if (y < 900) {
      return 160.0;
    } else {
      return 190.0;
    }
  }

  double m3StartingPointY({
    required double x,
    required double y,
  }) {
    if (y < 800) {
      return 130.0;
    } else if (y < 900) {
      return 160.0;
    } else {
      return 190.0;
    }
  }

  double L1Pipelength({
    required double x,
    required double y,
  }) {
    return 90.0;
  }

  double L21Pipelength({
    required double x,
    required double y,
  }) {
    return 90.0;
  }

  double L31Pipelength({
    required double x,
    required double y,
  }) {
    if (x < 1600) {
      return 400.0;
    } else if (x < 1800) {
      return 475.0;
    } else {
      return 550.0;
    }
  }

  double L2Pipelength({
    required double x,
    required double y,
  }) {
    if (y < 900) {
      return 150.0;
    } else {
      return 210.0;
    }
  }

  double L3Pipelength({
    required double x,
    required double y,
  }) {
    if (x < 1600) {
      return 400.0;
    } else if (x < 1800) {
      return 475.0;
    } else {
      return 550.0;
    }
  }

  double L2Tolerance({
    required double x,
    required double y,
  }) {
    if (y < 900) {
      return 85.0;
    } else {
      return 75.0;
    }
  }

  double L4Pipelength({
    required double x,
    required double y,
  }) {
    return 120.0;
  }

  double L41Pipelength({
    required double x,
    required double y,
  }) {
    return 120.0;
  }

  double l5Pipelength({
    required double x,
    required double y,
  }) {
    return 90.0;
  }

  double L6Pipelength({
    required double x,
    required double y,
  }) {
    return 70.0;
  }

  double L7Pipelength({
    required double x,
    required double y,
  }) {
    return 70.0;
  }

  double L81Pipelength({
    required double x,
    required double y,
  }) {
    return 70.0;
  }

  double L8Pipelength({
    required double x,
    required double y,
  }) {
    return 70.0;
  }

  double L9Pipelength({
    required double x,
    required double y,
  }) {
    return 70.0;
  }

  double L10Pipelength({
    required double x,
    required double y,
  }) {
    return 70.0;
  }

  double valveHeight({
    required double x,
    required double y,
  }) {
    return 60.0;
  }

  double valveWidth({
    required double x,
    required double y,
  }) {
    return 60.0;
  }

  double heightMotor({
    required double x,
    required double y,
  }) {
    return 210.0;
  }

  double heightTank({
    required double x,
    required double y,
  }) {
    return 200.0;
  }

  double widthTank({
    required double x,
    required double y,
  }) {
    return 220.0;
  }

  double widthMotor({
    required double x,
    required double y,
  }) {
    return 160.0;
  }

  double heightStand({
    required double x,
    required double y,
  }) {
    if (y < 900) {
      return 120.0;
    } else {
      return 170.0;
    }
  }

  double widthStand({
    required double x,
    required double y,
  }) {
    return 250.0;
  }

  double l1Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) + 105;
    return m1StartingPointY(x: x, y: y) + 105;
  }

  double l1X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) + widthMotor(x: x, y: y) - 3;
  }

  double l2Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) +
        L2Tolerance(x: x, y: y);
  }

  double l2X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) -
        3 +
        L1Pipelength(x: x, y: y) -
        3;
  }

  double l3Y({
    required double x,
    required double y,
  }) {
    return l1Y(x: x, y: y) - L2Pipelength(x: x, y: y) + 4;
  }

  double l3X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) -
        6;
  }

  double l4Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2;
  }

  double l4X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y);
  }

  double l7Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 -
        L6Pipelength(x: x, y: y);
  }

  double l7X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        14;
  }

  double l8Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y);
  }

  double l8X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        14;
  }

  double l9Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 -
        L6Pipelength(x: x, y: y);
  }

  double l9X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        L7Pipelength(x: x, y: y) +
        14 +
        valveWidth(x: x, y: y);
  }

  double l10Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y);
  }

  double l10X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        L7Pipelength(x: x, y: y) +
        14 +
        valveWidth(x: x, y: y);
  }

  double l21Y({
    required double x,
    required double y,
  }) {
    return m2StartingPointY(
        x: x, y: y) + 105;
  }

  double l31Y({
    required double x,
    required double y,
  }) {
    return l21Y(x: x, y: y) - L2Pipelength(x: x, y: y) + 3;
  }



  double l21X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(
        x: x, y: y) +
        widthMotor(
            x: x, y: y) -
        3;
  }

  double l91Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y);
  }

  double l91X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) +
        L31Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L41Pipelength(x: x, y: y) +
        L81Pipelength(x: x, y: y) +
        14 +
        valveWidth(x: x, y: y);
  }

  double l81Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y);
  }

  double l81X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) +
        L31Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L41Pipelength(x: x, y: y) +
        14;
  }

  double l41Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2;
  }

  double l41X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) + L31Pipelength(x: x, y: y) + widthTank(x: x, y: y)-12;
  }

  double l22Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) +
        L2Tolerance(x: x, y: y);
  }

  double l22X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) +
        L31Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y);
  }

  double l51Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2;
  }

  double l51X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) +
        L31Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L41Pipelength(x: x, y: y) -
        3;
  }

  double d1Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y) -
        20;
  }

  double d1X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        L7Pipelength(x: x, y: y) +
        14 +
        valveWidth(x: x, y: y) +
        L10Pipelength(x: x, y: y) +
        20;
  }

  double d2Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 -
        L6Pipelength(x: x, y: y) -
        20;
  }

  double d2X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        L9Pipelength(x: x, y: y) +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        L7Pipelength(x: x, y: y) +
        14 +
        valveWidth(x: x, y: y) +
        20;
  }

  double d3Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y) -
        20;
  }

  double d3X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) +
        L31Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L41Pipelength(x: x, y: y) +
        L81Pipelength(x: x, y: y) +
        14 +
        L81Pipelength(x: x, y: y) +
        20 +
        valveWidth(x: x, y: y);
  }

  double v1X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        14 +
        L7Pipelength(x: x, y: y);
  }

  double v1Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 -
        L6Pipelength(x: x, y: y) -
        42;
  }

  double v2X({
    required double x,
    required double y,
  }) {
    return m1StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L1Pipelength(x: x, y: y) +
        L3Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L4Pipelength(x: x, y: y) +
        14 +
        L7Pipelength(x: x, y: y);
  }

  double v2Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y) -
        42;
  }

  double v3Y({
    required double x,
    required double y,
  }) {
    return m1StartingPointY(x: x, y: y) +
        heightMotor(x: x, y: y) -
        heightStand(x: x, y: y) -
        heightTank(x: x, y: y) * 0.2 +
        L6Pipelength(x: x, y: y) -
        42;
  }

  double v3X({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) +
        widthMotor(x: x, y: y) +
        L21Pipelength(x: x, y: y) +
        L31Pipelength(x: x, y: y) -
        12 +
        widthTank(x: x, y: y) +
        L41Pipelength(x: x, y: y) +
        14 +
        L81Pipelength(x: x, y: y);
  }

  double m3StartingPointX({
    required double x,
    required double y,
  }) {
    return m2StartingPointX(x: x, y: y) + 170 + widthMotor(x: x, y: y);
  }
}






class SindhupalchowkDimension{

  double wellHeight({required double x, required double y}){
    return 240;
  }

  double wellWidth({required double x, required double y}){
    return 200;
  }

  double sedimentHeight({required double x, required double y}){
    return 300;
  }

  double sedimentWidth({required double x, required double y}){
    return sedimentHeight(x: x, y: y)*1.59;
  }




  double valveHeight({required double x, required double y}){
    return 70;
  }

  double valveWidth({required double x, required double y}){
    return 70;
  }


  double wellX({required double x, required double y}){
    if(x<1600){
      return 32;
    }else{
      return 64;
    }
  }

  double wellY({required double x, required double y}){
    if(y<800){
      return 150;
    }else{
      return 200;
    }
  }

  double l1Length({required double x, required double y}){
    return 150;
  }



  double l1Y({required double x, required double y}){
    return valveControllerHeight(x: x, y: y)-45;
  }

  double l1X({required double x, required double y}){
    return 0;
  }

  double l112Y({required double x, required double y}){
    return l1Y(x: x, y: y) - l2Length(x: x, y: y) + 25;
  }

  double l112X({required double x, required double y}){
    return l1X(x: x, y: y)+l1Length(x: x, y: y);
  }



  double valveControllerX({required double x, required double y}){
    return wellX(x: x, y: y)+wellWidth(x: x, y: y) - 10;
  }

  double valveControllerY({required double x, required double y}){
    return wellY(x: x, y: y)-valveControllerHeight(x: x, y: y)+ wellHeight(x: x, y: y)*0.65;
  }

  double valveControllerWidth({required double x, required double y}){
    return l4Length(x: x, y: y)+valveWidth(x: x, y: y) + l5Length(x: x, y: y) +l1Length(x: x, y: y) + 30;
  }

  double valveControllerHeight({required double x, required double y}){
    return l2Length(x: x, y: y)+80+20;
  }


  double l2Length({required double x, required double y}){
    return 180;
  }

  double l2X({required double x, required double y}){
    return l1X(x: x, y: y)+l1Length(x: x, y: y);
  }

  double l2Y({required double x, required double y}){
    return l1Y(x: x, y: y) -  l2Length(x: x, y: y) + 25;
  }

  double l3Length({required double x, required double y}){
    return 110;
  }

  double l3X({required double x, required double y}){
    return l2X(x: x, y: y) + 25;
  }

  double l3Y({required double x, required double y}){
    return l1Y(x: x, y: y);
  }

  double l4Length({required double x, required double y}){
    return 150;
  }

  double l4X({required double x, required double y}){
    return l2X(x: x, y: y) + 25;
  }

  double l4Y({required double x, required double y}){
    return l2Y(x: x, y: y);
  }

  double l5Length({required double x, required double y}){
    if(x<1600){
      return 100;
    }else{
      return 250;
    }
  }

  double l5X({required double x, required double y}){
    return v2X(x: x, y: y) + valveWidth(x: x, y: y);
  }

  double l5Y({required double x, required double y}){
    return l4Y(x: x, y: y);
  }

  double l6Length({required double x, required double y}){
    if(x<1600){
      return 220;
    }else{
      return 350;
    }
  }

  double l6X({required double x, required double y}){
    return sedimentX(x: x, y: y) + sedimentWidth(x: x, y: y);
  }

  double l6Y({required double x, required double y}){
    return sedimentY(x: x, y: y) + sedimentHeight(x: x, y: y)*0.42;
  }

  double l7Length({required double x, required double y}){
    if(y<800){
      return 100;
    }else{
      return 150;
    }
  }

  double l7X({required double x, required double y}){
    return l6X(x: x, y: y)+l6Length(x: x, y: y);
  }

  double l7Y({required double x, required double y}){
    return l6Y(x: x, y: y);
  }

  double l8X({required double x, required double y}){
    return l7X(x: x, y: y);
  }

  double l8Y({required double x, required double y}){
    return v3Y(x: x, y: y)+valveHeight(x: x, y: y);
  }

  double l81X({required double x, required double y}){
    return l7X(x: x, y: y)- l81Length(x: x, y: y);
  }

  double l81Y({required double x, required double y}){
    return l8Y(x: x, y: y)+l8Length(x: x, y: y)*0.45;
  }

  double l81Length({required double x, required double y}){
    if(y<800){
      return 50;
    }else{
      return 150;
    }
  }

  double l82X({required double x, required double y}){
    return l7X(x: x, y: y)- l81Length(x: x, y: y);
  }

  double l82Y({required double x, required double y}){
    return l8Y(x: x, y: y)+l8Length(x: x, y: y)*0.7;
  }

  double l82Length({required double x, required double y}){
    if(y<800){
      return 50;
    }else{
      return 150;
    }
  }

  double l83X({required double x, required double y}){
    return l7X(x: x, y: y)- l81Length(x: x, y: y);
  }

  double l83Y({required double x, required double y}){
    return l8Y(x: x, y: y)+l8Length(x: x, y: y)-25;
  }

  double l83Length({required double x, required double y}){
    if(y<800){
      return 50;
    }else{
      return 150;
    }
  }

  double l8Length({required double x, required double y}){
    if(y<800){
      return 300;
    }else{
      return 400;
    }
  }

  double l9X({required double x, required double y}){
    return l8X(x: x, y: y) - l9Length(x: x, y: y) + 25;
  }

  double l9Y({required double x, required double y}){
    return l8Y(x: x, y: y)+l8Length(x: x, y: y);
  }

  double d1X({required double x, required double y}){
    return v1X(x: x, y: y) + valveWidth(x: x, y: y)+3;
  }

  double d1Y({required double x, required double y}){
    return v1Y(x: x, y: y)+13;
  }

  double d2X({required double x, required double y}){
    return t1X(x: x, y: y) - 100;
  }

  double d2Y({required double x, required double y}){
    return t1Y(x: x, y: y)+t1Y(x: x, y: y)*0.2;
  }

  double d3X({required double x, required double y}){
    return t2X(x: x, y: y) - 100;
  }

  double d3Y({required double x, required double y}){
    return t2Y(x: x, y: y)+t1Y(x: x, y: y)*0.2;
  }


  double l9Length({required double x, required double y}){
    return 500;
  }

  double l10X({required double x, required double y}){
    return filterX(x: x, y: y) - l10Length(x: x, y: y)+3;
  }

  double l10Y({required double x, required double y}){
    return filterY(x: x, y: y) +  80;
  }


  double l10Length({required double x, required double y}){
    if(x<1600){
      return 150;
    }else{
      return 250;
    }
  }



  double l11X({required double x, required double y}){
    return filterX(x: x, y: y) - l11Length(x: x, y: y)+3;
  }

  double l11Y({required double x, required double y}){
    return filterY(x: x, y: y)+filterHeight(x: x, y: y)*0.8;
  }

  double l11Length({required double x, required double y}){
    return l10Length(x: x, y: y)+t1Width(x: x, y: y)+160;
  }

  double l12X({required double x, required double y}){
    return l11X(x: x, y: y);
  }

  double l12Y({required double x, required double y}){
    return l11Y(x: x, y: y)-l12Length(x: x, y: y);
  }

  double l12Length({required double x, required double y}){
    return 180;
  }

  double l13X({required double x, required double y}){
    return l12X(x: x, y: y) - l13Length(x: x, y: y);
  }

  double l13Y({required double x, required double y}){
    return l12Y(x: x, y: y);
  }

  double l13Length({required double x, required double y}){
    return 80;
  }

  double t1Width({required double x, required double y}){
    return 200;
  }

  double t1Height({required double x, required double y}){
    return 240;
  }

  double t1X({required double x, required double y}){
    return l10X(x: x, y: y) - t1Width(x: x, y: y)+5;
  }

  double t1Y({required double x, required double y}){
    return l10Y(x: x, y: y) - t1Height(x: x, y: y)*0.3;
  }

  double s1X({required double x, required double y}){
    return l2X(x: x, y: y) - 100 - 10;
  }

  double s1Y({required double x, required double y}){
    return l4Y(x: x, y: y);
  }

  double s2X({required double x, required double y}){
    return l6X(x: x, y: y) +20;
  }

  double s2Y({required double x, required double y}){
    return l6Y(x: x, y: y) + 40;
  }

  double s3X({required double x, required double y}){
    return filterX(x: x, y: y) - 130;
  }

  double s3Y({required double x, required double y}){
    return l10Y(x: x, y: y) + 40;
  }

  double t2X({required double x, required double y}){
    return l13X(x: x, y: y) - t2Width(x: x, y: y) + 5;
  }

  double t2Y({required double x, required double y}){
    return l13Y(x: x, y: y ) - t2Height(x: x, y: y)*0.3;
  }

  double t2Width({required double x, required double y}){
    return 180;
  }

  double t2Height({required double x, required double y}){
    return 250;
  }

  double v1X({required double x, required double y}){
    return l3X(x: x, y: y) + l3Length(x: x, y: y);
  }

  double v1Y({required double x, required double y}){
    return l3Y(x: x, y: y) - 24;
  }

  double v2X({required double x, required double y}){
    return l4X(x: x, y: y) + l4Length(x: x, y: y);
  }

  double v2Y({required double x, required double y}){
    return l4Y(x: x, y: y) - 24;
  }

  double v3X({required double x, required double y}){
    return l6X(x: x, y: y) +  l6Length(x: x, y: y) - 20;
  }

  double v3Y({required double x, required double y}){
    return l7Y(x: x, y: y) + l7Length(x: x, y: y);
  }

  double v4X({required double x, required double y}){
    return l81X(x: x, y: y)- valveWidth(x: x, y: y);
  }

  double v4Y({required double x, required double y}){
    return l81Y(x: x, y: y) - 25 ;
  }

  double v5X({required double x, required double y}){
    return v4X(x: x, y: y);
  }

  double v5Y({required double x, required double y}){
    return l82Y(x: x, y: y) - 25;
  }
  double v6X({required double x, required double y}){
    return v4X(x: x, y: y);
  }

  double v6Y({required double x, required double y}){
    return l83Y(x: x, y: y) -25;
  }

  double sedimentX({required double x, required double y}){
    return valveControllerX(x: x, y: y)+valveControllerWidth(x: x, y: y) - 5;
  }

  double sedimentY({required double x, required double y}){
    return valveControllerY(x: x, y: y) - 20 ;
  }

  double filterX({required double x, required double y}){
    return v6X(x: x, y: y) - filterWidth(x: x, y: y);
  }

  double filterY({required double x, required double y}){
    return v4Y(x: x, y: y)-50;
  }

  double filterHeight({required double x, required double y}){
    if(y<800){
      return 250;
    }else{
      return 350;
    }
  }

  double filterWidth({required double x, required double y}){
    return 500;
  }


  double flow1X({required double x, required double y}){
    return v2X(x: x, y: y) + valveWidth(x: x, y: y);
  }

  double flow1Y({required double x, required double y}){
    return l4Y(x: x, y: y) - 80;
  }


}

