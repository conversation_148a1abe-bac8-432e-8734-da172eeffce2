import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/kawosoti/schema/model/user_model.dart';
import 'package:si/services/firestore_path.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'item_user.dart';


final userQuery = FirebaseFirestore.instance.collection(FirestorePath.users).withConverter<UserModel>(
  fromFirestore: (snapshot, _) => UserModel.fromJson(snapshot.data()!),
  toFirestore: (history, _) => history.toJson(),
);

class UserListPage extends HookConsumerWidget {

  const UserListPage({required this.siteId});

  final String siteId;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.read(sharedPreferencesServiceProvider);
    print(prefs.getSiteId());
    return BaseScaffold(
      appbarText: "Users",

      child: FirestoreListView<UserModel>(
          query: userQuery.where("siteId",isEqualTo:prefs.getSiteId()).where("role", isLessThan: 3),
          itemBuilder: (context, snapshot) {
            final user = snapshot.data();
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0,vertical: 4),
              child: ItemUser(user),
            );
          },
      ),

    );
  }

}