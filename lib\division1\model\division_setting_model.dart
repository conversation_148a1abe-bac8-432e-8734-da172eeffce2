import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:si/division1/model/motor_setting_model.dart';
import 'package:si/division1/model/tank_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';


part 'division_setting_model.freezed.dart';
part 'division_setting_model.g.dart';

@freezed
class DivisionSettingModel with _$DivisionSettingModel{
  const factory DivisionSettingModel({
    @Default(0) int motor_number,
    @Default(0) int tank_number,
    @Default("") String name,
    @Default("") String password,
    @Default("") String id,
    TankSettingModel? tank10,
    BorewellSettingModel? bw10,
    TankSettingModel? tank12,
    TankSettingModel? tank13,
    TankSettingModel? tank14,
    SensorSettingModel? sensor10,
    SensorSettingModel? sensor20,
    SensorSettingModel? sensor21,
    SensorSettingModel? sensor11,
    SensorSettingModel? sensor32,
    List<int>? tank_id,
    List<int>? motor_id,
    List<int>? bw_id,
    List<int>? filter_id,
    List<int>? valve_id,
    TankSettingModel? tank11,
    MotorSettingModel? motor10,
    MotorSettingModel? motor11,
    MotorSettingModel? motor12,
    MotorSettingModel? motor13,
    MotorSettingModel? motor14,
    ValveSettingModel? valve10,
    ValveSettingModel? valve11,
    ValveSettingModel? valve12,
    ValveSettingModel? valve13,
    ValveSettingModel? valve14,
    ValveSettingModel? valve20,
    ValveSettingModel? valve21,
    ValveSettingModel? valve22,
    ValveSettingModel? valve30,
    ValveSettingModel? valve31,
    ValveSettingModel? valve32,
    ValveSettingModel? valve33,
    ValveSettingModel? valve40,
    ValveSettingModel? valve41,
    ValveSettingModel? valve42,
    ValveSettingModel? valve43,
    ValveSettingModel? valve44,
    ValveSettingModel? valve50,
    MotorSettingModel? filter10,
    MotorSettingModel? filter11,
    MotorSettingModel? filter20,
    MotorSettingModel? filter30,
    TankSettingModel? tank20,
    TankSettingModel? tank21,
    TankSettingModel? tank22,
    int? valveNumber,
    bool? has_valve,
    MotorSettingModel? motor20,
    MotorSettingModel? motor21,
    MotorSettingModel? motor22,
    TankSettingModel? tank30,
    TankSettingModel? tank31,
    TankSettingModel? tank32,
    TankSettingModel? tank40,
    TankSettingModel? tank41,
    TankSettingModel? tank50,
    TankSettingModel? tank51,
    MotorSettingModel? motor30,
    MotorSettingModel? motor31,
    MotorSettingModel? motor40,
    MotorSettingModel? motor41,
    MotorSettingModel? motor50,
    MotorSettingModel? motor51,
    LocationModel? location,
    String? installed_at,
    String? bom,
    bool? is_customized,

  }) = _DivisionSettingModel;

  factory DivisionSettingModel.fromJson(Map<String, dynamic> json) => _$DivisionSettingModelFromJson(json);
}


@freezed
class LocationModel with _$LocationModel{
  const factory LocationModel({
    @Default(27.325) double latitude,
    @Default(85.045) double longitude,


  }) = _LocationModel;

  factory LocationModel.fromJson(Map<String, dynamic> json) => _$LocationModelFromJson(json);
}

