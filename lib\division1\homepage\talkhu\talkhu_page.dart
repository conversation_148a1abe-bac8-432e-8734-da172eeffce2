import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';


import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../../utils/app_colors.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../haripur/haripur_system_one_page.dart';
import '../haripur/haripur_system_two_page.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import 'mobile/mobile_talkhu_page.dart';
import 'web/web_talkhu_page.dart';

class TalkhuPage extends HookConsumerWidget{

  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    final width = MediaQuery.of(context).size.width;
    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    /*return BaseScaffold(child: Center(
      child: PrimaryButton(text: "Add Site", onPressed: () async {
        await context.push("/addSites");
      },),
    ));*/


    return siteSetting.when(
        success: (setting, message) {
          if(width > 600){
            return BaseScaffold(
                showLeftIcon: false,
                showAppBar: false,
                appbarText: setting?.name ?? "",
                showAction: prefs.getUserRole() >3 ? true : false,
                onActionClick:() async {
                  final setting = await context.push("/location");
                },
                child: WebTalkhuPage(setting!));
          }else{
            return MobileTalkhuPage(setting!);
          }

        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }
}
