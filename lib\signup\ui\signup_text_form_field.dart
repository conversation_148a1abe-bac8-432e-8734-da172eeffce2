
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/alertdialog/async_value_ui.dart';
import 'package:si/login/provider/login_provider.dart';

import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../kawosoti/schema/model/user_model.dart';
import '../../utils/string_validators.dart';
import '../controller/signup_state.dart';
import '../provider/signup_provider.dart';
import 'form_label_text.dart';

class SignupTextFormField extends ConsumerStatefulWidget {
  const SignupTextFormField({super.key});

  @override
  ConsumerState<SignupTextFormField> createState() =>
      _SignupTextFormFieldState();
}

class _SignupTextFormFieldState extends ConsumerState<SignupTextFormField> {
  final regexForUpperCase = '(?=.*?[A-Z])';
  //RegExp(r'^(?=.*?[A-Z])$');
  final regexForLowerCase = '(?=.*?[a-z])';
  final regexForDigit = '(?=.*?[0-9])';
  final regexForSpecialCharacter = '(?=.*?[!@#\$&*~])';

  final _signupFormKey = GlobalKey<FormState>();
  final _node = FocusScopeNode();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _siteIdController = TextEditingController();
  final _phoneController = TextEditingController();

  String get fullName => _fullNameController.text;
  String get email => _emailController.text;
  String get password => _passwordController.text;
  String get phonenumber => _phoneController.text;
  String get siteId => _siteIdController.text;


  bool _submitted = false;

  Future<void> _submit(SignupState state) async {
    setState(() => _submitted = true);
    // only submit the form if validation passes
    if (_signupFormKey.currentState!.validate()) {
      final controller = ref.read(signupControllerProvider.notifier);
      final success =
          await controller.submit(email: email, password: password,
              fullname: fullName,phonenumber: phonenumber, siteId: siteId);
      if (success) {
        ref.read(userCallbackProvider.notifier).state = UserCallbackModel(email: email,password: password);
        if(context.mounted){
          context.pop();
        }
      }
    }
  }

  void _firstNameEditingComplete(SignupState state) {
    if (state.canSubmitFirstName(fullName)) {
      _node.nextFocus();
    }
  }

  void _emailEditingComplete(SignupState state) {
    if (state.canSubmitEmail(email)) {
      _node.nextFocus();
    }
  }

  void _phoneNumberEditingComplete(SignupState state) {
    if (state.canSubmitPhoneNumber(_phoneController.text)) {
      _node.nextFocus();
    }
  }

  void _passwordEditingComplete(SignupState state) {
    if (!state.canSubmitPassword(password) &&
        !state.canSubmitPasswordWithCustomRegexValidator(password)) {
      _node.previousFocus();
      return;
    }
    _submit(state);
  }

  @override
  void dispose() {
    // * TextEditingControllers should be always disposed
    _node.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _siteIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final nameInputFormatter = [
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_-]')),
    ];
    ref.listen<AsyncValue>(
      signupControllerProvider.select((state) => state.value),
      (_, state) {
        state.showAlertDialogOnError(
            ref: ref, context: context, key: "signup");
      },
    );
    final state = ref.watch(signupControllerProvider);
    return Padding(
      padding: const EdgeInsets.all(Sizes.p8),
      child: FocusScope(
        node: _node,
        child: Form(
          key: _signupFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //first name
              CustomTextFormField(
                name: "fullname",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
                textCapitalization: TextCapitalization.sentences,
                prefixIcon: const Icon(
                  Icons.account_circle_outlined,
                  color: Colors.grey,
                ),
                enabled: !state.isLoading,
                controller: _fullNameController,
                hintText: "Full Name",
                validator: (firstName) => !_submitted
                    ? null
                    : state.firstNameErrorText(firstName ?? ''),
              ),
              FormLabelText(label: "Enter full Name"),
              gapH16,
              CustomTextFormField(
                name: "phonenumber",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.phone,
                validator: (phone) => !_submitted
                    ? null
                    : state.phoneErrorText(phone ?? ''),
                onEditingComplete: () =>
                    _phoneNumberEditingComplete(state),
                prefixIcon: const Icon(
                  Icons.phone,
                  color: Colors.grey,
                ),
                textInputFormatter: [
                  FilteringTextInputFormatter.allow(
                      RegExp(r'^\d{0,11}'))
                ],
                enabled: !state.isLoading,
                controller: _phoneController,
                hintText: "Phone Number",
              ),
              FormLabelText(
                  label: "Enter phone number"),
              gapH16,
              // email field
              CustomTextFormField(
                name: "email",
                enabled: !state.isLoading,
                controller: _emailController,
                hintText: "Email",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.emailAddress,
                prefixIcon: const Icon(
                  Icons.email_outlined,
                  color: Colors.grey,
                ),
                validator: (email) =>
                    !_submitted ? null : state.emailErrorText(email ?? ''),
                onEditingComplete: () => _emailEditingComplete(state),
                textInputFormatter: <TextInputFormatter>[
                  ValidatorInputFormatter(
                      editingValidator: EmailEditingRegexValidator()),
                ],
              ),
              FormLabelText(label: "Enter email"),
              gapH16,
              // Password field
              Consumer(
                builder: (BuildContext context, WidgetRef ref, Widget? child) {
                  final obscured = ref.watch(signUpEyeToggleProvider);
                  return CustomTextFormField(
                    controller: _passwordController,
                    name: "password",
                    enabled: !state.isLoading,
                    hintText: "Password",
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    obscureText: obscured,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    onEditingComplete: () => _passwordEditingComplete(state),
                    inputDecoration: defaultInputDecoration(context).copyWith(
                      hintText: "Password",
                      hintStyle: TextStyle(color: Colors.grey),
                      enabled: !state.isLoading,
                      errorMaxLines: 3,
                      prefixIcon: const Icon(
                        FontAwesomeIcons.key,
                        color: Colors.grey,
                      ),
                      suffixIcon: Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: IconButton(
                          onPressed: () => ref
                              .read(signUpEyeToggleProvider.notifier)
                              .state = !obscured,
                          // onPressed: () {},
                          icon: Icon(
                            obscured
                                ? Icons.visibility_off_outlined
                                : Icons.visibility,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              FormLabelText(label: "Enter password"),
              gapH16,
              // Password field
              CustomTextFormField(
                name: "siteId",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                textCapitalization: TextCapitalization.sentences,
                prefixIcon: const Icon(
                  Icons.account_circle_outlined,
                  color: Colors.grey,
                ),
                enabled: !state.isLoading,
                controller: _siteIdController,
                hintText: "SiteId",
                validator: (firstName) => !_submitted
                    ? null
                    : state.firstNameErrorText(firstName ?? ''),
              ),

              FormLabelText(label: "Enter siteId. (e.g: site10,site1 etc)"),
              gapH24,
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: PrimaryButton(
                  isLoading: state.isLoading,
                  isGradient: true,
                  height: Sizes.p60,
                  text: "Signup",
                  onPressed: () async {
                    _submit(state);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

