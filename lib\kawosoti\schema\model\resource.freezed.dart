// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'resource.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Resource<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T? data, String? message) success,
    required TResult Function() unInitialized,
    required TResult Function(String? errorText) error,
    required TResult Function() loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T? data, String? message)? success,
    TResult? Function()? unInitialized,
    TResult? Function(String? errorText)? error,
    TResult? Function()? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T? data, String? message)? success,
    TResult Function()? unInitialized,
    TResult Function(String? errorText)? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ResourceData<T> value) success,
    required TResult Function(_ResourceUnInitialized<T> value) unInitialized,
    required TResult Function(_ResourceError<T> value) error,
    required TResult Function(_ResourceLoading<T> value) loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ResourceData<T> value)? success,
    TResult? Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult? Function(_ResourceError<T> value)? error,
    TResult? Function(_ResourceLoading<T> value)? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ResourceData<T> value)? success,
    TResult Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult Function(_ResourceError<T> value)? error,
    TResult Function(_ResourceLoading<T> value)? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResourceCopyWith<T, $Res> {
  factory $ResourceCopyWith(
          Resource<T> value, $Res Function(Resource<T>) then) =
      _$ResourceCopyWithImpl<T, $Res, Resource<T>>;
}

/// @nodoc
class _$ResourceCopyWithImpl<T, $Res, $Val extends Resource<T>>
    implements $ResourceCopyWith<T, $Res> {
  _$ResourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ResourceDataImplCopyWith<T, $Res> {
  factory _$$ResourceDataImplCopyWith(_$ResourceDataImpl<T> value,
          $Res Function(_$ResourceDataImpl<T>) then) =
      __$$ResourceDataImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T? data, String? message});
}

/// @nodoc
class __$$ResourceDataImplCopyWithImpl<T, $Res>
    extends _$ResourceCopyWithImpl<T, $Res, _$ResourceDataImpl<T>>
    implements _$$ResourceDataImplCopyWith<T, $Res> {
  __$$ResourceDataImplCopyWithImpl(
      _$ResourceDataImpl<T> _value, $Res Function(_$ResourceDataImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? message = freezed,
  }) {
    return _then(_$ResourceDataImpl<T>(
      freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T?,
      freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ResourceDataImpl<T> implements _ResourceData<T> {
  const _$ResourceDataImpl(this.data, this.message);

  @override
  final T? data;
  @override
  final String? message;

  @override
  String toString() {
    return 'Resource<$T>.success(data: $data, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResourceDataImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(data), message);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResourceDataImplCopyWith<T, _$ResourceDataImpl<T>> get copyWith =>
      __$$ResourceDataImplCopyWithImpl<T, _$ResourceDataImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T? data, String? message) success,
    required TResult Function() unInitialized,
    required TResult Function(String? errorText) error,
    required TResult Function() loading,
  }) {
    return success(data, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T? data, String? message)? success,
    TResult? Function()? unInitialized,
    TResult? Function(String? errorText)? error,
    TResult? Function()? loading,
  }) {
    return success?.call(data, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T? data, String? message)? success,
    TResult Function()? unInitialized,
    TResult Function(String? errorText)? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ResourceData<T> value) success,
    required TResult Function(_ResourceUnInitialized<T> value) unInitialized,
    required TResult Function(_ResourceError<T> value) error,
    required TResult Function(_ResourceLoading<T> value) loading,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ResourceData<T> value)? success,
    TResult? Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult? Function(_ResourceError<T> value)? error,
    TResult? Function(_ResourceLoading<T> value)? loading,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ResourceData<T> value)? success,
    TResult Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult Function(_ResourceError<T> value)? error,
    TResult Function(_ResourceLoading<T> value)? loading,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _ResourceData<T> implements Resource<T> {
  const factory _ResourceData(final T? data, final String? message) =
      _$ResourceDataImpl<T>;

  T? get data;
  String? get message;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResourceDataImplCopyWith<T, _$ResourceDataImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResourceUnInitializedImplCopyWith<T, $Res> {
  factory _$$ResourceUnInitializedImplCopyWith(
          _$ResourceUnInitializedImpl<T> value,
          $Res Function(_$ResourceUnInitializedImpl<T>) then) =
      __$$ResourceUnInitializedImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$ResourceUnInitializedImplCopyWithImpl<T, $Res>
    extends _$ResourceCopyWithImpl<T, $Res, _$ResourceUnInitializedImpl<T>>
    implements _$$ResourceUnInitializedImplCopyWith<T, $Res> {
  __$$ResourceUnInitializedImplCopyWithImpl(
      _$ResourceUnInitializedImpl<T> _value,
      $Res Function(_$ResourceUnInitializedImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResourceUnInitializedImpl<T> implements _ResourceUnInitialized<T> {
  const _$ResourceUnInitializedImpl();

  @override
  String toString() {
    return 'Resource<$T>.unInitialized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResourceUnInitializedImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T? data, String? message) success,
    required TResult Function() unInitialized,
    required TResult Function(String? errorText) error,
    required TResult Function() loading,
  }) {
    return unInitialized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T? data, String? message)? success,
    TResult? Function()? unInitialized,
    TResult? Function(String? errorText)? error,
    TResult? Function()? loading,
  }) {
    return unInitialized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T? data, String? message)? success,
    TResult Function()? unInitialized,
    TResult Function(String? errorText)? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (unInitialized != null) {
      return unInitialized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ResourceData<T> value) success,
    required TResult Function(_ResourceUnInitialized<T> value) unInitialized,
    required TResult Function(_ResourceError<T> value) error,
    required TResult Function(_ResourceLoading<T> value) loading,
  }) {
    return unInitialized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ResourceData<T> value)? success,
    TResult? Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult? Function(_ResourceError<T> value)? error,
    TResult? Function(_ResourceLoading<T> value)? loading,
  }) {
    return unInitialized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ResourceData<T> value)? success,
    TResult Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult Function(_ResourceError<T> value)? error,
    TResult Function(_ResourceLoading<T> value)? loading,
    required TResult orElse(),
  }) {
    if (unInitialized != null) {
      return unInitialized(this);
    }
    return orElse();
  }
}

abstract class _ResourceUnInitialized<T> implements Resource<T> {
  const factory _ResourceUnInitialized() = _$ResourceUnInitializedImpl<T>;
}

/// @nodoc
abstract class _$$ResourceErrorImplCopyWith<T, $Res> {
  factory _$$ResourceErrorImplCopyWith(_$ResourceErrorImpl<T> value,
          $Res Function(_$ResourceErrorImpl<T>) then) =
      __$$ResourceErrorImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({String? errorText});
}

/// @nodoc
class __$$ResourceErrorImplCopyWithImpl<T, $Res>
    extends _$ResourceCopyWithImpl<T, $Res, _$ResourceErrorImpl<T>>
    implements _$$ResourceErrorImplCopyWith<T, $Res> {
  __$$ResourceErrorImplCopyWithImpl(_$ResourceErrorImpl<T> _value,
      $Res Function(_$ResourceErrorImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorText = freezed,
  }) {
    return _then(_$ResourceErrorImpl<T>(
      freezed == errorText
          ? _value.errorText
          : errorText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ResourceErrorImpl<T> implements _ResourceError<T> {
  const _$ResourceErrorImpl(this.errorText);

  @override
  final String? errorText;

  @override
  String toString() {
    return 'Resource<$T>.error(errorText: $errorText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResourceErrorImpl<T> &&
            (identical(other.errorText, errorText) ||
                other.errorText == errorText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, errorText);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResourceErrorImplCopyWith<T, _$ResourceErrorImpl<T>> get copyWith =>
      __$$ResourceErrorImplCopyWithImpl<T, _$ResourceErrorImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T? data, String? message) success,
    required TResult Function() unInitialized,
    required TResult Function(String? errorText) error,
    required TResult Function() loading,
  }) {
    return error(errorText);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T? data, String? message)? success,
    TResult? Function()? unInitialized,
    TResult? Function(String? errorText)? error,
    TResult? Function()? loading,
  }) {
    return error?.call(errorText);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T? data, String? message)? success,
    TResult Function()? unInitialized,
    TResult Function(String? errorText)? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(errorText);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ResourceData<T> value) success,
    required TResult Function(_ResourceUnInitialized<T> value) unInitialized,
    required TResult Function(_ResourceError<T> value) error,
    required TResult Function(_ResourceLoading<T> value) loading,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ResourceData<T> value)? success,
    TResult? Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult? Function(_ResourceError<T> value)? error,
    TResult? Function(_ResourceLoading<T> value)? loading,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ResourceData<T> value)? success,
    TResult Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult Function(_ResourceError<T> value)? error,
    TResult Function(_ResourceLoading<T> value)? loading,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _ResourceError<T> implements Resource<T> {
  const factory _ResourceError(final String? errorText) =
      _$ResourceErrorImpl<T>;

  String? get errorText;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResourceErrorImplCopyWith<T, _$ResourceErrorImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResourceLoadingImplCopyWith<T, $Res> {
  factory _$$ResourceLoadingImplCopyWith(_$ResourceLoadingImpl<T> value,
          $Res Function(_$ResourceLoadingImpl<T>) then) =
      __$$ResourceLoadingImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$ResourceLoadingImplCopyWithImpl<T, $Res>
    extends _$ResourceCopyWithImpl<T, $Res, _$ResourceLoadingImpl<T>>
    implements _$$ResourceLoadingImplCopyWith<T, $Res> {
  __$$ResourceLoadingImplCopyWithImpl(_$ResourceLoadingImpl<T> _value,
      $Res Function(_$ResourceLoadingImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResourceLoadingImpl<T> implements _ResourceLoading<T> {
  const _$ResourceLoadingImpl();

  @override
  String toString() {
    return 'Resource<$T>.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResourceLoadingImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T? data, String? message) success,
    required TResult Function() unInitialized,
    required TResult Function(String? errorText) error,
    required TResult Function() loading,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T? data, String? message)? success,
    TResult? Function()? unInitialized,
    TResult? Function(String? errorText)? error,
    TResult? Function()? loading,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T? data, String? message)? success,
    TResult Function()? unInitialized,
    TResult Function(String? errorText)? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ResourceData<T> value) success,
    required TResult Function(_ResourceUnInitialized<T> value) unInitialized,
    required TResult Function(_ResourceError<T> value) error,
    required TResult Function(_ResourceLoading<T> value) loading,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ResourceData<T> value)? success,
    TResult? Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult? Function(_ResourceError<T> value)? error,
    TResult? Function(_ResourceLoading<T> value)? loading,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ResourceData<T> value)? success,
    TResult Function(_ResourceUnInitialized<T> value)? unInitialized,
    TResult Function(_ResourceError<T> value)? error,
    TResult Function(_ResourceLoading<T> value)? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _ResourceLoading<T> implements Resource<T> {
  const factory _ResourceLoading() = _$ResourceLoadingImpl<T>;
}
