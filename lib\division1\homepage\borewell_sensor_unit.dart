


import 'dart:math';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/tank_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../../services/shared_preferences_service.dart';
import '../model/tank_setting_model.dart';

class BoreWellSensorUnit extends HookConsumerWidget{

  BoreWellSensorUnit(
      {
        this.bwId = '10',
        this.bwModel,
        this.ratio = 3.0
      });


  final String bwId;

  final double ratio;

  final BorewellSettingModel? bwModel;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final division = (bwModel?.height ?? 100.0) / 5;
    final preference = ref.watch(sharedPreferencesServiceProvider);
    final bwLevel = useState<double>(20.0);
    if(bwModel?.is_active ?? false){
      EasyLoading.dismiss();
      ref.listen<AsyncValue<DatabaseEvent>>(bwProvider(bwId), (previous, next)  async {
        if (next.asData?.value.snapshot.value != null) {
          final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
          final aftercleanData = Map<String, dynamic>.from(afterData);
          final aftertankModel = BwModel.fromJson(aftercleanData);
          if(bwModel?.is_active ??  false){
            if(aftertankModel.level < 0.0){
              bwLevel.value = 2.0;
            }else{
              bwLevel.value = aftertankModel.level;
            }
          }else{
          }
        }
      });
    }else{
      bwLevel.value = 2.0;
    }

    double height = bwModel?.height ?? 100.0;

    return Container(
      width: 160,
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border.all(color:Colors.black),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Center(
          child: Container(
              height: 250,
              padding: const EdgeInsets.all(0.0),
              child: SfLinearGauge(
                orientation: LinearGaugeOrientation.vertical,
                maximum: bwModel?.height ?? 100.0,
                tickPosition: LinearElementPosition.outside,
                labelPosition: LinearLabelPosition.outside,
                minorTicksPerInterval: 0,
                interval: 5,
                onGenerateLabels: () {
                  return getLinearAxisLabel(division.ceil(),bwModel?.height ?? 100.0);

                },
                axisTrackStyle: const LinearAxisTrackStyle(),
                markerPointers: <LinearMarkerPointer>[
                  LinearShapePointer(
                      value: (bwModel?.is_active ?? false) ? bwLevel.value : 10.7,
                      enableAnimation: false,
                      onChanged: (dynamic value) {

                      },
                      shapeType: LinearShapePointerType.rectangle,
                      color: const Color(0xff0074E3),
                      height: 1.5,
                      width: 140),
                  LinearWidgetPointer(
                      value: (bwModel?.is_active ?? false) ? bwLevel.value : 10.7,
                      enableAnimation: false,
                      onChanged: (dynamic value) {

                      },
                      child: SizedBox(
                          width: 16,
                          height: 14,
                          child: Image.asset(
                            'assets/icons/rectangle_pointer.png',
                          ))),
                  LinearWidgetPointer(
                      value: (bwModel?.is_active ?? false) ? bwLevel.value : 10.7,
                      enableAnimation: false,
                      onChanged: (dynamic value) {

                      },
                      offset: 110,
                      position: LinearElementPosition.outside,
                      child: Container(
                          width: 35,
                          height: 20,
                          decoration: BoxDecoration(
                              color: Colors.grey,
                              boxShadow: <BoxShadow>[
                                BoxShadow(
                                  color: Colors.grey,
                                  offset: const Offset(0.0, 1.0), //(x,y)
                                  blurRadius: 6.0,
                                ),
                              ],
                              borderRadius: BorderRadius.circular(4)),
                          child: Center(
                            child: Text(
                                (bwModel?.is_active ?? false) ? (height-bwLevel.value).toStringAsFixed(2) : "87.7 m",
                                style: const TextStyle(
                                    fontWeight: FontWeight.normal,
                                    fontSize: 10,
                                    color: Color(0xff0074E3))),
                          ))),
                ],
                ranges: <LinearGaugeRange>[
                  LinearGaugeRange(
                    endValue: (bwModel?.is_active ?? false) ? bwLevel.value : 10.7,
                    startWidth: 90,
                    midWidth:  140,
                    endWidth: 140,
                    color: Colors.lightBlueAccent,
                  ),
                  LinearGaugeRange(
                    startValue: (bwModel?.is_active ?? false) ? bwLevel.value : 10.7,
                    endValue: 130,
                    startWidth: 130,
                    midWidth:  130,
                    endWidth: 130,
                    color: Colors.brown.withOpacity(0.7),
                  ),
                ],
              ))),
    );
  }

}

List<LinearAxisLabel> getLinearAxisLabel(int division, double boreHeight){
  List<LinearAxisLabel> labels = [];
  if(division>10){
    final div = boreHeight/10.ceil();
    for(int i = 0; i < div; i++){
      labels.add(LinearAxisLabel(text: '${10 * i} m', value: boreHeight - (10 * i)));
    }
    if(boreHeight % 10 != 0){
      labels.add(LinearAxisLabel(text: '${boreHeight.ceil()} m', value: 0));
    }
    return labels;
  }else{
    for(int i = 0; i < division-1 ; i++){
      labels.add(LinearAxisLabel(text: '${5 * i} m', value: boreHeight - (5 * i)));
    }
    if(boreHeight % 5 != 0){
      labels.add(LinearAxisLabel(text: '${boreHeight.ceil()} m', value: 0));
    }
    return labels;
  }

}