import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/repository/auth_repository.dart';

import 'login_state.dart';

class LoginController extends StateNotifier<LoginState> {
  LoginController({
    required this.authRepository,
  }) : super(LoginState());
  final IAuthRepository authRepository;

  Future<bool> submit(String email, String password, String token) async {

    state = state.copyWith(value: const AsyncValue.loading());
    final value = await AsyncValue.guard(() => _authenticate(email, password,token));
    state = state.copyWith(value: value);
    return value.hasError==false;
  }

  Future<bool> sendLoadingState(){
    state = state.copyWith(value: const AsyncValue.loading());
    return Future.value(true);

  }
  Future<bool?> _authenticate(String email, String password, String token) async  {
    final returningvalue =  await authRepository
        .signInWithEmail(email,password, token);
    return returningvalue;
  }
}


class PasswordController extends StateNotifier<PasswordState> {
  PasswordController({
    required this.authRepository,
  }) : super(PasswordState());
  final IAuthRepository authRepository;

  Future<bool> verifyPassword(String password) async {

    state = state.copyWith(value: const AsyncValue.loading()) as PasswordState;
    final value = await AsyncValue.guard(() => _authenticate(password));
    state = state.copyWith(value: value) as PasswordState;
    return value.hasError==false;
  }

  Future<bool> sendLoadingState(){
    state = state.copyWith(value: const AsyncValue.loading()) as PasswordState;
    return Future.value(true);

  }
  Future<String?> _authenticate(String password) async  {
    final returningvalue =  await authRepository
        .verifyPasswordAndReturnSite(password);
    return returningvalue;
  }
}
