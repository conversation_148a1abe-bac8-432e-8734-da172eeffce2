

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../../../constants/app_sizes.dart';
import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';
import '../../new_common_motor_unit.dart';
import '../../new_motor_unit.dart';
import '../../new_tank_unit.dart';

class SourceSitePage extends HookConsumerWidget{

  const SourceSitePage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor10State = useState<MotorModel>(const MotorModel());
    final motor11State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            print("NOT NULL");
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status ==
              motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status ==
              motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Container(
                height: 60,
                width: double.infinity,
                color: Colors.blueAccent,
                child: Center(
                  child: Text(
                    "मुलको पानी",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        Container(
                          height: 40,
                          width: 20,
                          color: Colors.blueAccent,
                        ),
                        NewCommonValveUnit(
                          valveModel: MotorModel(),
                          valveSettingModel: setting.valve10,
                          valveId: "10",
                        ),
                        Container(
                          height: 270,
                          width: 20,
                          color: Colors.blueAccent,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        gapH16,
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: NewTankUnit(1, "10", setting!.tank10!, 170, 200),
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                children: [
                                  Container(
                                    height: 40,
                                    width: 20,
                                    color: Colors.blueAccent,
                                  ),
                                  NewCommonValveUnit(
                                    valveModel: MotorModel(),
                                    valveSettingModel: setting.valve12,
                                    valveId: "12",
                                  ),
                                  Container(
                                    height: 40,
                                    width: 20,
                                    color: Colors.blueAccent,
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Column(
                                children: [
                                  Container(
                                    height: 40,
                                    width: 20,
                                    color: Colors.blueAccent,
                                  ),
                                  NewCommonValveUnit(
                                    valveModel: MotorModel(),
                                    valveSettingModel: setting.valve13,
                                    valveId: "13",
                                  ),
                                  Container(
                                    height: 40,
                                    width: 20,
                                    color: Colors.blueAccent,
                                  ),
                                ],
                              ),
                            )
                          ],
                        )

                      ],
                    ),
                  ),

                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        Container(
                          height: 40,
                          width: 20,
                          color: Colors.blueAccent,
                        ),
                        NewCommonValveUnit(
                          valveModel: MotorModel(),
                          valveSettingModel: setting.valve11,
                          valveId: "11",
                        ),
                        Container(
                          height: 270,
                          width: 20,
                          color: Colors.blueAccent,
                        ),
                      ],
                    ),
                  )
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      Container(
                          decoration: BoxDecoration(
                            color: Colors.blueAccent,
                            border: Border.all(color:Colors.blueAccent),
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                          ),
                          child: Column(
                            children: [
                              NewMotorUnit(motorId: '10',motorModel: motor10State.value,motorSettingModel: setting.motor10,ratio: 2.5,),
                              Text("SW1",style: TextStyle(color: Colors.white),),
                            ],
                          )),
                      Container(
                        height: 40,
                        width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.blueAccent,
                          border: Border.all(color:Colors.blueAccent),
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                        ),
                        child: Column(
                          children: [
                            NewMotorUnit(motorId: '11',motorModel: motor11State.value,motorSettingModel: setting.motor11,ratio: 2.5,),
                            Text("SW2",style: TextStyle(color: Colors.white),),
                          ],
                        ),
                      ),
                      Container(
                        height: 40,
                        width: 20,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                ],
              ),
              Container(
                height: 40,
                width: double.infinity,
                color: Colors.blueAccent,
                child: Center(child: Text("डाडा गाउ",style: TextStyle(color: Colors.white),)),
              ),

            ],
          ),
        ),
      ],
    );
  }
}