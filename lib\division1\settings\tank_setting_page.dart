

import 'dart:developer';

import 'package:advanced_chips_input/advanced_chips_input.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/division1/settings/label_text.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../common/widgets/base_scaffold.dart';
import '../../common/widgets/widgets.dart';
import '../../constants/app_sizes.dart';
import '../../constants/assets_manager.dart';
import '../../provider/auth_provider.dart';
import '../homepage/tank_list_page.dart';
import '../model/motor_setting_model.dart';
import 'add_site_page.dart';


final keySimpleChipsInput = GlobalKey<FormState>();

class TankSettingPage extends HookConsumerWidget {
  TankSettingPage( this.tankId);

  final String? tankId;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  Future<void> _handleTankSetting(
      BuildContext context,
      WidgetRef ref, {
        required TextEditingController actualHeightController,
        required TextEditingController maxHeightController,
        required TextEditingController minHeightContoller,
        required TextEditingController tankNameController,
        required TextEditingController siteNameController,
        required TextEditingController uplaodTimeController,
        required TextEditingController offset,
        required bool floatEnable,
        required bool levelSensorEnable,
        required bool isBw,
        required bool isActive,
      }) async {

    final repo = ref.read(divisionRepositoryProvider);
    final rtdbrepo = ref.read(divisionRtdbServiceProvider);
    final siteId = ref.read(sharedPreferencesServiceProvider).getSiteId();

        // final email = emailController.text;
        // final password = passwordController.text;
        // Logger().d('Email : $email \nPassword: $password');
        try {
          EasyLoading.show(status: 'Updating..');
          await repo.setTankSetting(siteId: siteId,
              tankId:"tank"+tankId!,
              tankName:tankNameController.text.trim().toString(),
              actualHeight:int.parse(actualHeightController.text.trim().toString()),
              maxHeight:int.parse(maxHeightController.text.trim().toString()),
              minHeight:int.parse(minHeightContoller.text.trim().toString()),
              uploadTime: int.parse(uplaodTimeController.text.trim().toString()),
              offset: int.parse(offset.text.trim().toString()),
              isBw:isBw,
              floatEnable: false,
              levelSensorEnable: true,
              isActive: isActive);
          await rtdbrepo.updateTankParameter(siteId:siteId,tankId:tankId!,
              maxHeight:int.parse(maxHeightController.text.trim().toString()),
              actualHeight: int.parse(actualHeightController.text.trim().toString()),
              upload_time: int.parse(uplaodTimeController.text.trim().toString()),
              name: tankNameController.text.trim().toString(),
              minHeight:int.parse(minHeightContoller.text.trim().toString()));
          final realSetting = await repo.getSiteSetting(siteId);
          await ref.read(localStorageProvider).setSettings(realSetting!);
          EasyLoading.dismiss();
          Navigator.pop(context,'refresh');

        } catch (e) {
          EasyLoading.showError('Error');
          //Logger().e('create user from email', e);
        }
        FocusScope.of(context).unfocus();

  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controlNumbers = useState<String>("");

    final actualHeightController = useTextEditingController();
    final tankNameController = useTextEditingController();
    final siteNameController = useTextEditingController();
    final minHeightController = useTextEditingController();
    final controlNumbersController = useTextEditingController();
    final maxHeightController = useTextEditingController();
    final uploadTimeController = useTextEditingController();
    final tankOffSetController = useTextEditingController();
    final isBoreSensor = useState(false);
    final tankSettingModel = getTankModel(tankId ?? "10", ref.read(localStorageProvider).getSettings());
    final prefs = ref.read(sharedPreferencesServiceProvider);

    useEffect(() {
      // print('SessionDEt : ${sessionModel.id}');
      Future<void> fetchData() async {
        final result = await ref.read(divisionRtdbServiceProvider).getControl(prefs.getSiteId(),tankId!);
        controlNumbers.value = result;
      }

      fetchData(); //
      actualHeightController.text = tankSettingModel.actual_height.toString();
      tankNameController.text = tankSettingModel.name;
      minHeightController.text = tankSettingModel.min_height.toString();
      maxHeightController.text = tankSettingModel.max_height.toString();
      uploadTimeController.text = tankSettingModel.upload_time.toString();
      tankOffSetController.text = tankSettingModel.offset.toString();
      isBoreSensor.value = tankSettingModel.is_bw ?? false;
      return null;
    }, const []);

    return BaseScaffold(
      showAppBar: true,
      appbarText: "Tank Setting",
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildTextFormFieldSection(context, ref,
                        siteNameController,
                        actualHeightController,
                        minHeightController,
                        maxHeightController,
                        tankNameController,
                        uploadTimeController,
                    tankOffSetController),
                    HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      return ListTile(
                        title: const Text('Borewell Sensor:'),
                        trailing: CupertinoSwitch(
                          value: isBoreSensor.value,
                          onChanged: (bool value) {
                           isBoreSensor.value = value;
                          },
                        ),
                        onTap: () {
                          isBoreSensor.value = !isBoreSensor.value;
                        },
                      );
                    }),
                    Column(
                      children: [
                        LabelText(label: "Control Motors Id: ${controlNumbers.value}"),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: AdvancedChipsInput(
                            formKey: keySimpleChipsInput,
                            controller: controlNumbersController,

                            onEditingComplete: () {
                              print('onEditingComplete');
                            },
                            onSaved: (value) async {
                              print('onSaved: $value');
                              if(value.isNotEmpty){
                                if(value.substring(0, value.length-1) == "000"){
                                  await ref.read(divisionRtdbServiceProvider).setControl(prefs.getSiteId(), tankId!, "");
                                }else{
                                  await ref.read(divisionRtdbServiceProvider).setControl(prefs.getSiteId(), tankId!, value.substring(0, value.length - 1));
                                }
                              }
                            },
                            separatorCharacter: ',',
                            placeChipsSectionAbove: true,
                            widgetContainerDecoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                            ),
                            chipContainerDecoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.all(Radius.circular(50)),
                            ),
                            chipTextStyle: TextStyle(color: Colors.white),
                            validateInput: true,
                            validateInputMethod: (value) {
                              if (value.length < 2) {
                                return 'Input should be at least 2 characters long';
                              }
                              return null;
                            },
                            onChipDeleted: (chipText, index) {
                              print('Deleted chip: $chipText at index $index');
                            },
                          ),
                        ),
                      ],
                    ),
                    /*HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final floatSwitch = useState<bool>(floatEnable.value);
                      return ListTile(
                        title: const Text('Float Switch'),
                        trailing: CupertinoSwitch(
                          value: floatSwitch.value,
                          onChanged: (bool value) {
                            floatSwitch.value = value;
                            floatEnable.value = floatSwitch.value;
                          },
                        ),
                        onTap: () {
                          floatSwitch.value = !floatSwitch.value;
                          floatEnable.value = floatSwitch.value;
                        },
                      );
                    }),
                    HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final levelSwitch = useState<bool>(sensorEnable.value);
                      return ListTile(
                        title: const Text('Level sensor'),
                        trailing: CupertinoSwitch(
                          value: levelSwitch.value,
                          onChanged: (bool value) {
                            levelSwitch.value = value;
                            sensorEnable.value = levelSwitch.value;
                          },
                        ),
                        onTap: () {
                          levelSwitch.value = !levelSwitch.value;
                          sensorEnable.value = levelSwitch.value;
                        },
                      );
                    }),
                    HookConsumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                      final activeSwitch = useState<bool>(activeEnable.value);
                      return ListTile(
                        title: const Text('Active'),
                        trailing: CupertinoSwitch(
                          value: activeSwitch.value,
                          onChanged: (bool value) {
                            activeSwitch.value = value;
                            activeEnable.value = activeSwitch.value;
                          },
                        ),
                        onTap: () {
                          activeSwitch.value = !activeSwitch.value;
                          activeEnable.value = activeSwitch.value;
                        },
                      );
                    }),*/

                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24.0, vertical: 24),
                      child: PrimaryButton(
                        text: 'Update',
                        height: 54,
                        onPressed: () async {
                          keySimpleChipsInput.currentState!.save();
                          _handleTankSetting(context,ref,
                              actualHeightController: actualHeightController,
                              maxHeightController: maxHeightController,
                              minHeightContoller: minHeightController,
                              floatEnable: false,
                              levelSensorEnable: false,
                              siteNameController: siteNameController,
                              tankNameController: tankNameController,
                              offset: tankOffSetController,
                              isBw:isBoreSensor.value,
                              uplaodTimeController: uploadTimeController,
                              isActive: true);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

        ],
      ),

    );
  }

  Widget _buildTextFormFieldSection(
      BuildContext context,
      WidgetRef ref,
      TextEditingController siteNameController,
      TextEditingController actualHeightController,
      TextEditingController minHeightController,
      TextEditingController maxHeightController,
      TextEditingController tanknameController,
      TextEditingController uploadTimeController,
      TextEditingController tankOffsetController,
      ) {
    // final FocusNode? pwdFocusNode = FocusNode();

    // EdgeFunctionController edgeFunctionController = EdgeFunctionController();
    final _node = FocusScopeNode();


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        gapH16,
        LabelText(label: "Enter tank name"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "tankName",
              controller: tanknameController,
              hintText: "Tank Name",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.name,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),
        LabelText(label: "Enter actual height"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "actualHeight",
              controller: actualHeightController,
              hintText: "Actual Height",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),
        LabelText(label: "Enter minimum height"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "minHeight",
              controller: minHeightController,
              hintText: "Minimum Height",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),
        LabelText(label: "Enter maximum height"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "maxHeight",
              controller: maxHeightController,
              hintText: "Maximum Height",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),
        LabelText(label: "Enter upload time"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "uploadTime",
              controller: uploadTimeController,
              hintText: "Enter upload time in mins",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),
        LabelText(label: "Enter Tank offset"),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
            child: CustomTextFormField(
              name: "offset",
              controller: tankOffsetController,
              hintText: "Enter offset",
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(
                Icons.propane_tank_outlined,
                color: Colors.grey,
                size: 18,
              ),
            )
        ),


      ],
    );
  }
}

