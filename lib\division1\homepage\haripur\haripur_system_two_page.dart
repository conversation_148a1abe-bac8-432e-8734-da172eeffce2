

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/homepage/widgets/motor_widget.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/homepage/widgets/tank_widget.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/division_setting_model.dart';
import '../../model/motor_model.dart';

class HaripurSystemTwoPage extends HookConsumerWidget{

  const HaripurSystemTwoPage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dimension = ref.read(dimensionProvider);
    final size = MediaQuery.of(context).size;
    final motor12State = useState<MotorModel>(MotorModel());
    final motor13State = useState<MotorModel>(MotorModel());
    final valve12State = useState<MotorModel>(MotorModel());
    final prefs = ref.read(sharedPreferencesServiceProvider);

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor12State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor12State.value.device_status == motor12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("13"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor13State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor13State.value.device_status == motor13State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve12State.value = MotorModel(
                output_status: datasnapshot['output_status'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve12State.value.device_status == valve12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return Center(
      child: Container(
        width: 360,
        child: Stack(
          children: [
            Positioned(
              left: 16,
              top: 280,
              child: TankBlock(
                tankSettingModel: setting.tank11!,
                tankId: "11",
                height: 260,
                width: 220,
              ),
            ),

            Positioned(
              left: 360/2-10,
              top: 116,
              child: VerticalPipeline(
                length: 190,
                isTop: false,
                topFlow: true,
                singleFlow: false,
                isBottom: false,
              ),
            ),
            Positioned(
              left: 24,
                top: 16,
                child: NewMotorUnit(
                  motorSettingModel: setting.motor12!,
                  motorModel: motor12State.value,
                  motorId: "12",
                )),

            Positioned(
              top: 16,
              right: 24,
                child: NewMotorUnit(
                  motorSettingModel: setting.motor13!,
                  motorModel: motor13State.value,
                  motorId: "13",
                )),
            Positioned(
              left: 24+360/3-2,
              top: 100,
              child: HorizontalPipeline(
                length: 70+5,
                isLeft: false,
                isRight: false,
              ),
            ),
            Positioned(
              left: 235,
              top: 420,
              child: HaripurValveWidget(
                size: size,
                role: prefs.getUserRole(),
                appDimension: dimension,
                valveId: "12",
                motorModel: valve12State.value,
                name: setting.valve12?.name ?? "Valve 12",
              ),
            ),
            Positioned(
              left: 290,
              top: 448,
              child: Image.asset(
                valve12State.value.output_status == 3 ? "assets/icons/distribution.png" : "assets/icons/distribution_grey.png",
                height: 40,
                width: 70,
                fit: BoxFit.contain,
              ),
            ),



          ],
        ),
      ),
    );
  }

}