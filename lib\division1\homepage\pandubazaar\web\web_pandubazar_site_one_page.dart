

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';

import 'package:firebase_database/firebase_database.dart';
import 'package:si/division1/homepage/widgets/sensor_widget.dart';

import '../../../../common/widgets/widgets.dart';
import '../../../../constants/app_sizes.dart';
import '../../../../provider/auth_provider.dart';
import '../../../model/division_setting_model.dart';
import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';
import '../../new_tank_unit.dart';
import '../../widgets/tank_widget.dart';


class WebPandubazarSiteOnePage extends HookConsumerWidget{


  const WebPandubazarSiteOnePage(this.setting);

  final DivisionSettingModel setting;


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor10State = useState<MotorModel>(const MotorModel());
    final motor11State = useState<MotorModel>(const MotorModel());
    final motor12State = useState<MotorModel>(const MotorModel());
    final motor20State = useState<MotorModel>(const MotorModel());
    final motor21State = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("11"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor11State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor11State.value.device_status == motor11State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("12"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor12State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor12State.value.device_status == motor12State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status == motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("21"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor21State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor21State.value.device_status == motor21State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });


    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child:  Padding(
            padding: const EdgeInsets.only(left: 32.0,top: 8,),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset("assets/icons/pandubazar.webp", height: 40, width: 40),
                gapW8,
                Column(
                  children: [
                    Text(setting!.name!!, style: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),),
                    Text("कटुन्जे, भक्तपुर", style: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),),
                  ],
                ),
                Expanded(
                  child: Text("Swodeshi SCADA System",textAlign: TextAlign.center,style: TextStyle(fontSize: 18,  color: Colors.black, fontWeight: FontWeight.w900)),
                ),
                Column(
                  children: [
                    Text("Powered by", style: TextStyle(fontSize: 10, color: Colors.black, fontWeight: FontWeight.bold),),
                    Text("Swodeshi Innovation", style: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),),
                  ],
                ),
                gapW8,
                Image.asset("assets/icons/logo_scada.webp", height: 35, width: 35),
                gapW16,



              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: gapH16,
        ),
        SliverToBoxAdapter(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  children: [
                    NewWebMotorUnit(motorId: '10',motorModel: motor10State.value,motorSettingModel: setting.motor10,width: 150,),
                    Container(
                      height: 30,width: 20,
                        color: Colors.blueAccent,
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  children: [
                    NewWebMotorUnit(motorId: '11',motorModel: motor11State.value,motorSettingModel: setting.motor11,width: 150,),
                    Container(
                      height: 30,width: 20,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  children: [
                    NewWebMotorUnit(motorId: '12',motorModel: motor12State.value,motorSettingModel: setting.motor12,width: 150,),
                    Container(
                      height: 30,width: 20,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  children: [
                    NewWebMotorUnit(motorId: '20',motorModel: motor20State.value,motorSettingModel: setting.motor20,width: 150,),
                    Container(
                      height: 30,width: 20,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  children: [
                    NewWebMotorUnit(motorId: '21',motorModel: motor21State.value,motorSettingModel: setting.motor21,width: 150,),
                    Container(
                      height: 30,width: 20,
                      color: Colors.blueAccent,
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            color: Colors.blueAccent,
              height: 20,
            width: double.infinity,
          ),
        ),
        SliverToBoxAdapter(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              gapW32,
              TurbiditySensorWidget(sensorId: "10", isPortrait:  true,
              setting: setting.sensor10,),
              gapW16,
              Column(
                children: [
                  Container(
                    width: 20,
                    height: 30,
                    color: Colors.blueAccent,
                  ),
                  TankBlock(
                    tankId: "10",
                    tankSettingModel: setting.tank10!,
                    height: 200,
                    width: 150,
                  ),
                  //NewTankUnit(3, "10", setting.tank10!, 200, 200,sensorId: "10"),
                  Container(
                    width: 20,
                    height: 30,
                    color: Colors.blueAccent,
                  )
                ],
              ),
              Container(
                color: Colors.blueAccent,
                height: 20,
                width: 30,
              ),
              Row(
                children: [
                  Column(
                    children: [
                      Container(
                        width: 20,
                        height: 30,
                        color: Colors.blueAccent,
                      ),
                      TankBlock(
                        tankId: "11",
                        tankSettingModel: setting.tank11!,
                        height: 200,
                        width: 150,
                      ),
                      //NewTankUnit(3, "11", setting.tank11!, 200, 200,sensorId: "11"),
                      Container(
                        width: 20,
                        height: 30,
                        color: Colors.blueAccent,
                      ),

                    ],
                  ),
                  gapW16,
                  ThreeSensorWidget(sensorId: "11", isPortrait: true,
                  setting: setting.sensor11,),

                ],
              ),
              Container(
                height: 20,
                width: 30,
              ),
              Row(
                children: [
                  Column(
                    children: [
                      Container(
                        width: 20,
                        height: 30,
                        color: Colors.blueAccent,
                      ),
                      TankBlock(
                        tankId: "20",
                        tankSettingModel: setting.tank20!,
                        height: 200,
                        width: 150,
                      ),
                      //NewTankUnit(3, "20", setting.tank20!, 200, 200, sensorId: "12"),
                      Container(
                        width: 20,
                        height: 30,
                        color: Colors.blueAccent,
                      )
                    ],
                  ),
                  gapW16,
                  TurbiditySensorWidget(sensorId: "20",isPortrait: true,
                  setting: setting.sensor20,),
                ],
              ),
              gapW16,
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            height: 30,
            color: Colors.blueAccent,
            child: const Center(
              child: Text(
                "वितरण",
                style: TextStyle(color: Colors.white,fontSize: 18),
              ),
            ),
          ),
        )
      ],
    );
  }

}