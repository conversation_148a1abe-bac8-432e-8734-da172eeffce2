class KatunjeDimension {


  double filterWidth({required double x, required double y}) {
    return 400;
  }

  double filterHeight({required double x, required double y}) {
    return 250;
  }

  double filterX({required double x, required double y}) {
    return 430;
  }

  double filterY({required double x, required double y}) {
    return 290;
  }

  double m10X({required double x, required double y}) {
    return 24;
  }

  double m10Y({required double x, required double y}) {
    return 60;
  }

  double m30X({required double x, required double y}) {
    return m11X(x: x, y: y)+320;
  }

  double m30Y({required double x, required double y}) {
    return 50;
  }

  double m31X({required double x, required double y}) {
    return m30X(x: x, y: y) + 240;
  }

  double m31Y({required double x, required double y}) {
    return 50;
  }

  double l10X({required double x, required double y}) {
    return m10X(x: x, y: y) + 160;
  }

  double l10Y({required double x, required double y}) {
    return m10Y(x: x, y: y) + 96;
  }

  double l10Length({required double x, required double y}) {
    return 260;
  }

  double l1X({required double x, required double y}) {
    return l10X(x: x, y: y)+l10Length(x: x, y: y);
  }

  double l1Y({required double x, required double y}) {
    return l10Y(x: x, y: y);
  }

  double l1Length({required double x, required double y}) {
    return 135;
  }

  double l11X({required double x, required double y}) {
    return m11X(x: x, y: y) + 160-2;
  }


  double l11Y({required double x, required double y}) {
    return m11Y(x: x, y: y) + 96;
  }

  double l11Length({required double x, required double y}) {
    return 73;
  }

  double l12X({required double x, required double y}) {
    return m12X(x: x, y: y) + 160-2;
  }

  double l12Y({required double x, required double y}) {
    return m12Y(x: x, y: y) + 96;
  }

  double l12Length({required double x, required double y}) {
    return 250;
  }

  double l20X({required double x, required double y}) {
    return m20X(x: x, y: y)+160;
  }

  double l20Y({required double x, required double y}) {
    return m20Y(x: x, y: y) + 96;
  }

  double l20Length({required double x, required double y}) {
    return 90;
  }

  double l2X({required double x, required double y}) {
    return l20X(x: x, y: y)+l20Length(x: x, y: y) - 12;
  }

  double l2Y({required double x, required double y}) {
    return l20Y(x: x, y: y) - l2Length(x: x, y: y)+2;
  }

  double l2Length({required double x, required double y}) {
    return 115;
  }

  double l3X({required double x, required double y}) {
    return l20X(x: x, y: y)+l20Length(x: x, y: y) + 5;
  }

  double l3Y({required double x, required double y}) {
    return l20Y(x: x, y: y) - l2Length(x: x, y: y)+2;
  }

  double l3Length({required double x, required double y}) {
    return 115;
  }

  double l4X({required double x, required double y}) {
    return m30X(x: x, y: y) - l4Length(x: x, y: y);
  }

  double l4Y({required double x, required double y}) {
    return m30Y(x: x, y: y) + 96;
  }

  double l4Length({required double x, required double y}) {
    return 60;
  }

  double l5X({required double x, required double y}) {
    return l4X(x: x, y: y);
  }

  double l5Y({required double x, required double y}) {
    return l4Y(x: x, y: y);
  }

  double l5Length({required double x, required double y}) {
    return 145;
  }

  double l6X({required double x, required double y}) {
    return m31X(x: x, y: y) - l6Length(x: x, y: y);
  }

  double l6Y({required double x, required double y}) {
    return l4Y(x: x, y: y);
  }

  double l6Length({required double x, required double y}) {
    return 60;
  }

  double l7X({required double x, required double y}) {
    return l6X(x: x, y: y);
  }

  double l7Y({required double x, required double y}) {
    return l4Y(x: x, y: y);
  }

  double l7Length({required double x, required double y}) {
    return 145;
  }

  double l8X({required double x, required double y}) {
    return m22X(x: x, y: y) - l8Length(x: x, y: y);
  }

  double l8Y({required double x, required double y}) {
    return l21Y(x: x, y: y);
  }

  double l8Length({required double x, required double y}) {
    return 90;
  }

  double l9X({required double x, required double y}) {
    return l8X(x: x, y: y);
  }

  double l9Y({required double x, required double y}) {
    return l8Y(x: x, y: y) - l9Length(x: x, y: y);
  }

  double l9Length({required double x, required double y}) {
    return 113;
  }

  double l21X({required double x, required double y}) {
    return m21X(x: x, y: y)- l21Length(x: x, y: y);
  }

  double l21Y({required double x, required double y}) {
    return l20Y(x: x, y: y) ;
  }

  double l21Length({required double x, required double y}) {
    return 75;
  }

  double l40X({required double x, required double y}) {
    return filterX(x: x, y: y)+filterWidth(x: x, y: y) -3;
  }

  double l40Y({required double x, required double y}) {
    return filterY(x: x, y: y) + filterHeight(x: x, y: y)/2 - 5;
  }

  double l40Length({required double x, required double y}) {
    return 135;
  }

  double l45X({required double x, required double y}) {
    return l40X(x: x, y: y) + l40Length(x: x, y: y);
  }

  double l45Y({required double x, required double y}) {
    return t10Y(x: x, y: y) + t10Height(x: x, y: y)+ t20Height(x: x, y: y)+80;
  }

  double l45Length({required double x, required double y}) {
    return l42Length(x: x, y: y);
  }

  double l41X({required double x, required double y}) {
    return l40X(x: x, y: y) + l40Length(x: x, y: y);
  }

  double l41Y({required double x, required double y}) {
    return t10Y(x: x, y: y) + t10Height(x: x, y: y)*0.4;
  }

  double l41Length({required double x, required double y}) {
    return 540;
  }

  double l42X({required double x, required double y}) {
    return l41X(x: x, y: y);
  }

  double l42Y({required double x, required double y}) {
    return l41Y(x: x, y: y);
  }

  double l42Length({required double x, required double y}) {
    return 80;
  }

  double l43X({required double x, required double y}) {
    return l42X(x: x, y: y);
  }

  double l43Y({required double x, required double y}) {
    return t20Y(x: x, y: y) + t20Height(x: x, y: y)*0.4;
  }

  double l43Length({required double x, required double y}) {
    return l42Length(x: x, y: y);
  }

  double l44X({required double x, required double y}) {
    return l42X(x: x, y: y);
  }

  double l44Y({required double x, required double y}) {
    return l41Y(x: x, y: y) + l41Length(x: x, y: y);
  }

  double l44Length({required double x, required double y}) {
    return l42Length(x: x, y: y);
  }

  double m11X({required double x, required double y}) {
    return 200;
  }

  double m11Y({required double x, required double y}) {
    return 220;
  }

  double m12X({required double x, required double y}) {
    return m10X(x: x, y: y);
  }

  double m12Y({required double x, required double y}) {
    return m11Y(x: x, y: y) + 125;
  }

  double m20X({required double x, required double y}) {
    return m11X(x: x, y: y);
  }

  double m20Y({required double x, required double y}) {
    return m12Y(x: x, y: y)+160;
  }

  double m21X({required double x, required double y}) {
    return m20X(x: x, y: y)+330;
  }

  double m21Y({required double x, required double y}) {
    return m20Y(x: x, y: y);
  }

  double m22X({required double x, required double y}) {
    return m21X(x: x, y: y) + 260;
  }


  double m22Y({required double x, required double y}) {
    return m21Y(x: x, y: y);
  }


  double t10X({required double x, required double y}) {
    return m31X(x: x, y: y) + 280;
  }

  double t10Y({required double x, required double y}) {
    return 8;
  }

  double t10Width({required double x, required double y}) {
    return 140;
  }

  double t10Height({required double x, required double y}) {
    return 180;
  }

  double t20X({required double x, required double y}) {
    return t10X(x: x, y: y);
  }

  double t20Y({required double x, required double y}) {
    return filterY(x: x, y: y) - 105;
  }

  double t20Width({required double x, required double y}) {
    return 140;
  }

  double t20Height({required double x, required double y}) {
    return 180;
  }

  double t30X({required double x, required double y}) {
    return t10X(x: x, y: y);
  }

  double t30Y({required double x, required double y}) {
    return t20Y(x: x, y: y) + t20Height(x: x, y: y);
  }

  double t30Width({required double x, required double y}) {
    return 140;
  }

  double t30Height({required double x, required double y}) {
    return 180;
  }

  double t11X({required double x, required double y}) {
    return t10X(x: x, y: y);
  }

  double t11Y({required double x, required double y}) {
    return t20Y(x: x, y: y) + t20Height(x: x, y: y) + t30Height(x: x, y: y);
  }

  double t11Width({required double x, required double y}) {
    return 140;
  }

  double t11Height({required double x, required double y}) {
    return 150;
  }

}