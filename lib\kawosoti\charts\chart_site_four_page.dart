import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:msh_checkbox/msh_checkbox.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/kawosoti/charts/borewell_chart.dart';
import 'package:si/kawosoti/charts/chlorine_chart.dart';
import 'package:si/kawosoti/charts/line_chart.dart';
import 'package:si/kawosoti/charts/oh_chart.dart';
import 'package:si/kawosoti/charts/ph_chart.dart';
import 'package:si/kawosoti/charts/turbidity_chart.dart';
import 'package:si/kawosoti/schema/widgets/date_forward_backward.dart';

import '../../utils/app_colors.dart';
import '../schema/site_one/provider/chart_one_controller.dart';

class ChartSiteFourPage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chartController = ref.watch(chartOneProvider("site4"));

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            DateForwardBackward(onDateChange: (date) {
              ref.read(chartOneProvider("site4").notifier).getData(date);
            }),
            chartController.when(
              success: (data, message) {
                print(data);
                return Consumer(
                    builder: (context, ref, child) {

                      return Column(
                        children: [

                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Borewell',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: BorewellChart(chartModel: data!,))
                          ),
                        ],
                      );
                    }
                );
              },

              error: (er) {
                return Container();
              },
              unInitialized: () {
                return Container();
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            )
          ],
        ),
      ),
    );
  }
}


