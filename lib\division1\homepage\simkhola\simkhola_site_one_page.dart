import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/borewell_sensor_unit.dart';
import 'package:si/division1/homepage/new_motor_unit.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/tank_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';

import '../../../constants/app_sizes.dart';
import '../../model/motor_model.dart';


class SimkholaSiteOnePage extends HookConsumerWidget {

  const SimkholaSiteOnePage(this.setting);

  final DivisionSettingModel setting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final motor10State = useState<MotorModel>(MotorModel());
    final motor20State = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider("20"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor20State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor20State.value.device_status == motor20State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return  CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              gapH8,
             Row(
               mainAxisAlignment: MainAxisAlignment.center,
               children: [
                 Column(
                   children: [
                     BoreWellSensorUnit(bwId: '10',bwModel :setting.bw10!,ratio:2.0),
                     Container(
                         height : 20
                     )
                   ],
                 ),
                 Container(width: 50,height: 20,color : Colors.blueAccent),

                 Column(
                   children: [
                     Container(height : 30),
                     NewMotorUnit(ratio: 3,motorId: "10",motorSettingModel: setting.motor10!,
                     motorModel: motor10State.value,),
                     Container(
                       color: motor10State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                       height: 60,
                       width: 20,
                     ),
                   ],
                 ),

               ],
             ),
             Container(width: double.infinity,height: 30,color : Colors.blueAccent),
              Row(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        children: [
                          BoreWellSensorUnit(bwId: '10',bwModel :BorewellSettingModel(
                            height: 99.9,
                            name: "Borewell",
                            is_active: false,
                            upload_time: 15,
                          ),ratio:2.0),
                          Container(
                              height : 20
                          )
                        ],
                      ),
                      Container(width: 30,height: 20,color : Colors.blueAccent),

                      Column(
                        children: [
                          Container(height : 30),
                          NewMotorUnit(ratio: 3,motorId: "20",motorSettingModel: setting.motor20!,
                          motorModel: motor20State.value,),
                          Container(
                            color: motor20State.value.device_status==3 ? Colors.blueAccent : Colors.grey,
                            height: 60,
                            width: 20,
                          ),
                        ],
                      ),
                      Container(width: 20),

                      Container(width: 20,height: 310,color : Colors.blueAccent),


                    ],
                  ),
                ],
              ),
              Container(width: double.infinity,height: 20,color : Colors.blueAccent),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      Container(height: 30,width : 20, ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 0.0),
                        child: NewTankUnit(3, "10", setting.tank10!!, 180, 150),
                      ),
                      Container(height: 30,width : 20, color: Colors.blueAccent,),
                    ],
                  ),
                  Container(height:20, width : 40,color : Colors.blueAccent),
                  Column(
                    children: [
                      Container(height: 30,width : 20, color: Colors.blueAccent,),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 0.0),
                        child: NewTankUnit(3, "20", setting.tank20!!, 180, 150),
                      ),
                      Container(height: 30,width : 20),

                    ],
                  ),
                ],
              ),
              Container(width: double.infinity,height: 50,color : Colors.blueAccent,
              child: Center(child: Text("Distribution Pipeline", style: TextStyle(color: Colors.white)))),

            ],
          ),
        ),
      ],
    );
  }
}
