import 'package:flutter/material.dart';

import '../../constants/app_sizes.dart';

//this is for pagination loading
class CustomLoadingIndicator extends StatelessWidget {
  final Color? color;

  const CustomLoadingIndicator({Key? key, this.color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
          SizedBox(height: MediaQuery.of(context).size.height * 0.35),
        CircularProgressIndicator(color: Colors.green,),

      ],
    ));
  }
}

class LoadingIndicator extends StatelessWidget {
  final Color? color;

  const LoadingIndicator({Key? key, this.color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  Center(child: Padding(
      padding: padding4,
      child: CircularProgressIndicator(
        color: color,
      ),
    ));
  }
}
