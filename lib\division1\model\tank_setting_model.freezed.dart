// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tank_setting_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TankSettingModel _$TankSettingModelFromJson(Map<String, dynamic> json) {
  return _TankSettingModel.fromJson(json);
}

/// @nodoc
mixin _$TankSettingModel {
  bool get level_sensor_enable => throw _privateConstructorUsedError;
  bool get float_enable => throw _privateConstructorUsedError;
  int get max_height => throw _privateConstructorUsedError;
  int get min_height => throw _privateConstructorUsedError;
  int get actual_height => throw _privateConstructorUsedError;
  int get offset => throw _privateConstructorUsedError;
  bool get is_active => throw _privateConstructorUsedError;
  bool get is_bw => throw _privateConstructorUsedError;
  int get upload_time => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  /// Serializes this TankSettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TankSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TankSettingModelCopyWith<TankSettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TankSettingModelCopyWith<$Res> {
  factory $TankSettingModelCopyWith(
          TankSettingModel value, $Res Function(TankSettingModel) then) =
      _$TankSettingModelCopyWithImpl<$Res, TankSettingModel>;
  @useResult
  $Res call(
      {bool level_sensor_enable,
      bool float_enable,
      int max_height,
      int min_height,
      int actual_height,
      int offset,
      bool is_active,
      bool is_bw,
      int upload_time,
      String name});
}

/// @nodoc
class _$TankSettingModelCopyWithImpl<$Res, $Val extends TankSettingModel>
    implements $TankSettingModelCopyWith<$Res> {
  _$TankSettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TankSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? level_sensor_enable = null,
    Object? float_enable = null,
    Object? max_height = null,
    Object? min_height = null,
    Object? actual_height = null,
    Object? offset = null,
    Object? is_active = null,
    Object? is_bw = null,
    Object? upload_time = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      level_sensor_enable: null == level_sensor_enable
          ? _value.level_sensor_enable
          : level_sensor_enable // ignore: cast_nullable_to_non_nullable
              as bool,
      float_enable: null == float_enable
          ? _value.float_enable
          : float_enable // ignore: cast_nullable_to_non_nullable
              as bool,
      max_height: null == max_height
          ? _value.max_height
          : max_height // ignore: cast_nullable_to_non_nullable
              as int,
      min_height: null == min_height
          ? _value.min_height
          : min_height // ignore: cast_nullable_to_non_nullable
              as int,
      actual_height: null == actual_height
          ? _value.actual_height
          : actual_height // ignore: cast_nullable_to_non_nullable
              as int,
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      is_bw: null == is_bw
          ? _value.is_bw
          : is_bw // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: null == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TankSettingModelImplCopyWith<$Res>
    implements $TankSettingModelCopyWith<$Res> {
  factory _$$TankSettingModelImplCopyWith(_$TankSettingModelImpl value,
          $Res Function(_$TankSettingModelImpl) then) =
      __$$TankSettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool level_sensor_enable,
      bool float_enable,
      int max_height,
      int min_height,
      int actual_height,
      int offset,
      bool is_active,
      bool is_bw,
      int upload_time,
      String name});
}

/// @nodoc
class __$$TankSettingModelImplCopyWithImpl<$Res>
    extends _$TankSettingModelCopyWithImpl<$Res, _$TankSettingModelImpl>
    implements _$$TankSettingModelImplCopyWith<$Res> {
  __$$TankSettingModelImplCopyWithImpl(_$TankSettingModelImpl _value,
      $Res Function(_$TankSettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TankSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? level_sensor_enable = null,
    Object? float_enable = null,
    Object? max_height = null,
    Object? min_height = null,
    Object? actual_height = null,
    Object? offset = null,
    Object? is_active = null,
    Object? is_bw = null,
    Object? upload_time = null,
    Object? name = null,
  }) {
    return _then(_$TankSettingModelImpl(
      level_sensor_enable: null == level_sensor_enable
          ? _value.level_sensor_enable
          : level_sensor_enable // ignore: cast_nullable_to_non_nullable
              as bool,
      float_enable: null == float_enable
          ? _value.float_enable
          : float_enable // ignore: cast_nullable_to_non_nullable
              as bool,
      max_height: null == max_height
          ? _value.max_height
          : max_height // ignore: cast_nullable_to_non_nullable
              as int,
      min_height: null == min_height
          ? _value.min_height
          : min_height // ignore: cast_nullable_to_non_nullable
              as int,
      actual_height: null == actual_height
          ? _value.actual_height
          : actual_height // ignore: cast_nullable_to_non_nullable
              as int,
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      is_bw: null == is_bw
          ? _value.is_bw
          : is_bw // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: null == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TankSettingModelImpl implements _TankSettingModel {
  const _$TankSettingModelImpl(
      {this.level_sensor_enable = true,
      this.float_enable = true,
      this.max_height = 400,
      this.min_height = 50,
      this.actual_height = 450,
      this.offset = 0,
      this.is_active = true,
      this.is_bw = false,
      this.upload_time = 15,
      this.name = ''});

  factory _$TankSettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TankSettingModelImplFromJson(json);

  @override
  @JsonKey()
  final bool level_sensor_enable;
  @override
  @JsonKey()
  final bool float_enable;
  @override
  @JsonKey()
  final int max_height;
  @override
  @JsonKey()
  final int min_height;
  @override
  @JsonKey()
  final int actual_height;
  @override
  @JsonKey()
  final int offset;
  @override
  @JsonKey()
  final bool is_active;
  @override
  @JsonKey()
  final bool is_bw;
  @override
  @JsonKey()
  final int upload_time;
  @override
  @JsonKey()
  final String name;

  @override
  String toString() {
    return 'TankSettingModel(level_sensor_enable: $level_sensor_enable, float_enable: $float_enable, max_height: $max_height, min_height: $min_height, actual_height: $actual_height, offset: $offset, is_active: $is_active, is_bw: $is_bw, upload_time: $upload_time, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TankSettingModelImpl &&
            (identical(other.level_sensor_enable, level_sensor_enable) ||
                other.level_sensor_enable == level_sensor_enable) &&
            (identical(other.float_enable, float_enable) ||
                other.float_enable == float_enable) &&
            (identical(other.max_height, max_height) ||
                other.max_height == max_height) &&
            (identical(other.min_height, min_height) ||
                other.min_height == min_height) &&
            (identical(other.actual_height, actual_height) ||
                other.actual_height == actual_height) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.is_active, is_active) ||
                other.is_active == is_active) &&
            (identical(other.is_bw, is_bw) || other.is_bw == is_bw) &&
            (identical(other.upload_time, upload_time) ||
                other.upload_time == upload_time) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      level_sensor_enable,
      float_enable,
      max_height,
      min_height,
      actual_height,
      offset,
      is_active,
      is_bw,
      upload_time,
      name);

  /// Create a copy of TankSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TankSettingModelImplCopyWith<_$TankSettingModelImpl> get copyWith =>
      __$$TankSettingModelImplCopyWithImpl<_$TankSettingModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TankSettingModelImplToJson(
      this,
    );
  }
}

abstract class _TankSettingModel implements TankSettingModel {
  const factory _TankSettingModel(
      {final bool level_sensor_enable,
      final bool float_enable,
      final int max_height,
      final int min_height,
      final int actual_height,
      final int offset,
      final bool is_active,
      final bool is_bw,
      final int upload_time,
      final String name}) = _$TankSettingModelImpl;

  factory _TankSettingModel.fromJson(Map<String, dynamic> json) =
      _$TankSettingModelImpl.fromJson;

  @override
  bool get level_sensor_enable;
  @override
  bool get float_enable;
  @override
  int get max_height;
  @override
  int get min_height;
  @override
  int get actual_height;
  @override
  int get offset;
  @override
  bool get is_active;
  @override
  bool get is_bw;
  @override
  int get upload_time;
  @override
  String get name;

  /// Create a copy of TankSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TankSettingModelImplCopyWith<_$TankSettingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BorewellSettingModel _$BorewellSettingModelFromJson(Map<String, dynamic> json) {
  return _BorewellSettingModel.fromJson(json);
}

/// @nodoc
mixin _$BorewellSettingModel {
  double? get height => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  bool get is_active => throw _privateConstructorUsedError;
  int? get upload_time => throw _privateConstructorUsedError;

  /// Serializes this BorewellSettingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BorewellSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BorewellSettingModelCopyWith<BorewellSettingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BorewellSettingModelCopyWith<$Res> {
  factory $BorewellSettingModelCopyWith(BorewellSettingModel value,
          $Res Function(BorewellSettingModel) then) =
      _$BorewellSettingModelCopyWithImpl<$Res, BorewellSettingModel>;
  @useResult
  $Res call({double? height, String? name, bool is_active, int? upload_time});
}

/// @nodoc
class _$BorewellSettingModelCopyWithImpl<$Res,
        $Val extends BorewellSettingModel>
    implements $BorewellSettingModelCopyWith<$Res> {
  _$BorewellSettingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BorewellSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = freezed,
    Object? name = freezed,
    Object? is_active = null,
    Object? upload_time = freezed,
  }) {
    return _then(_value.copyWith(
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: freezed == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BorewellSettingModelImplCopyWith<$Res>
    implements $BorewellSettingModelCopyWith<$Res> {
  factory _$$BorewellSettingModelImplCopyWith(_$BorewellSettingModelImpl value,
          $Res Function(_$BorewellSettingModelImpl) then) =
      __$$BorewellSettingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double? height, String? name, bool is_active, int? upload_time});
}

/// @nodoc
class __$$BorewellSettingModelImplCopyWithImpl<$Res>
    extends _$BorewellSettingModelCopyWithImpl<$Res, _$BorewellSettingModelImpl>
    implements _$$BorewellSettingModelImplCopyWith<$Res> {
  __$$BorewellSettingModelImplCopyWithImpl(_$BorewellSettingModelImpl _value,
      $Res Function(_$BorewellSettingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BorewellSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = freezed,
    Object? name = freezed,
    Object? is_active = null,
    Object? upload_time = freezed,
  }) {
    return _then(_$BorewellSettingModelImpl(
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      is_active: null == is_active
          ? _value.is_active
          : is_active // ignore: cast_nullable_to_non_nullable
              as bool,
      upload_time: freezed == upload_time
          ? _value.upload_time
          : upload_time // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BorewellSettingModelImpl implements _BorewellSettingModel {
  const _$BorewellSettingModelImpl(
      {this.height = 0.0,
      this.name = "",
      this.is_active = true,
      this.upload_time = 15});

  factory _$BorewellSettingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BorewellSettingModelImplFromJson(json);

  @override
  @JsonKey()
  final double? height;
  @override
  @JsonKey()
  final String? name;
  @override
  @JsonKey()
  final bool is_active;
  @override
  @JsonKey()
  final int? upload_time;

  @override
  String toString() {
    return 'BorewellSettingModel(height: $height, name: $name, is_active: $is_active, upload_time: $upload_time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BorewellSettingModelImpl &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.is_active, is_active) ||
                other.is_active == is_active) &&
            (identical(other.upload_time, upload_time) ||
                other.upload_time == upload_time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, height, name, is_active, upload_time);

  /// Create a copy of BorewellSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BorewellSettingModelImplCopyWith<_$BorewellSettingModelImpl>
      get copyWith =>
          __$$BorewellSettingModelImplCopyWithImpl<_$BorewellSettingModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BorewellSettingModelImplToJson(
      this,
    );
  }
}

abstract class _BorewellSettingModel implements BorewellSettingModel {
  const factory _BorewellSettingModel(
      {final double? height,
      final String? name,
      final bool is_active,
      final int? upload_time}) = _$BorewellSettingModelImpl;

  factory _BorewellSettingModel.fromJson(Map<String, dynamic> json) =
      _$BorewellSettingModelImpl.fromJson;

  @override
  double? get height;
  @override
  String? get name;
  @override
  bool get is_active;
  @override
  int? get upload_time;

  /// Create a copy of BorewellSettingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BorewellSettingModelImplCopyWith<_$BorewellSettingModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
