import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/kawosoti/charts/chart_site_three_page.dart';
import 'package:si/kawosoti/charts/chart_site_two_page.dart';
import 'package:si/kawosoti/history/history_site_one_page.dart';
import 'package:si/kawosoti/history/history_site_three_page.dart';
import 'package:si/kawosoti/history/history_site_two_page.dart';
import 'package:si/kawosoti/schema/screens/site_three_screen.dart';
import 'package:si/kawosoti/schema/screens/site_two_screen.dart';
import 'package:si/kawosoti/users/user_page.dart';
import 'package:si/kawosoti/web_dashboard_page.dart';
import '../kawosoti/charts/chart_site_eight_page.dart';
import '../kawosoti/charts/chart_site_five_page.dart';
import '../kawosoti/charts/chart_site_four_page.dart';
import '../kawosoti/charts/chart_site_one_page.dart';
import '../kawosoti/charts/chart_site_seven_page.dart';
import '../kawosoti/charts/chart_site_six_page.dart';
import '../kawosoti/history/history_site_eight_page.dart';
import '../kawosoti/history/history_site_five_page.dart';
import '../kawosoti/history/history_site_four_page.dart';
import '../kawosoti/history/history_site_seven_page.dart';
import '../kawosoti/history/history_site_six_page.dart';
import '../kawosoti/kawasoti_setting_page.dart';
import '../kawosoti/schema/screens/site_eight_screen.dart';
import '../kawosoti/schema/screens/site_five_screen.dart';
import '../kawosoti/schema/screens/site_four_screen.dart';
import '../kawosoti/schema/screens/site_one_screen.dart';
import '../kawosoti/schema/screens/site_seven_screen.dart';
import '../kawosoti/schema/screens/site_six_screen.dart';
import '../login/ui/login_ui.dart';
import '../kawosoti/main_kawosoki.dart';
import '../provider/auth_provider.dart';
import '../signup/ui/signup_page.dart';

enum AppRoute { login, signUp, forgetPassword, dashboard }

final ValueKey<String> _scaffoldKey = const ValueKey<String>('App scaffold');
final GlobalKey<NavigatorState> _rootNavigatorKey =
GlobalKey<NavigatorState>(debugLabel: 'root');
final GlobalKey<NavigatorState> _tabANavigatorKey =
GlobalKey<NavigatorState>(debugLabel: 'tabANav');

final gokawosokiRouterProvider = Provider<GoRouter>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      redirect: (context, state) {
        final isLoggedIn = authRepository.isLogin();
        if (isLoggedIn) {
          return state.matchedLocation;
        } else {
          if (!isLoggedIn && state.matchedLocation == '/login') {
            return '/login';
          } else if (!isLoggedIn && state.matchedLocation == '/signup') {
            return '/signup';
          } else {
            return "/login";
          }
        }
      },
      routes: <RouteBase>[
        GoRoute(
          path: '/',
          redirect: (_, __) => '/site1',
        ),
        GoRoute(
          path: '/login',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              KawosokiFadeTransitionPage(
                key: state.pageKey,
                child: LoginPage(),
              ),
        ),
        GoRoute(
          path: '/signup',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              KawosokiFadeTransitionPage(
                key: state.pageKey,
                child: SignUpPage(),
              ),
        ),
        StatefulShellRoute(
          builder: (context, state, navigationShell) {
            return navigationShell;
          },
          navigatorContainerBuilder: (context, navigationShell, children) {
            return WebDashBoardPage(
                navigationShell: navigationShell, children: children);
          },
          branches: <StatefulShellBranch>[
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site1",
                      builder: (context,state) => const SiteOneScreen(),
                      routes: <RouteBase>[

                      ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site2",
                      builder: (context,state) => const SiteTwoScreen(),
                      routes: <RouteBase>[

                      ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site3",
                    builder: (context,state) => const SiteThreeScreen(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site4",
                    builder: (context,state) => const SiteFourScreen(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site5",
                    builder: (context,state) => const SiteFiveScreen(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site6",
                    builder: (context,state) => const SiteSixScreen(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site7",
                    builder: (context,state) => const SiteSevenScreen(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/site8",
                    builder: (context,state) => const SiteEightScreen(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history1",
                      builder: (context,state) => HistorySiteOnePage(),
                      routes: <RouteBase>[

                      ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history2",
                      builder: (context,state) => HistorySiteTwoPage(),
                      routes: <RouteBase>[

                      ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history3",
                    builder: (context,state) => HistorySiteThreePage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history4",
                    builder: (context,state) => HistorySiteFourPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history5",
                    builder: (context,state) => HistorySiteFivePage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history6",
                    builder: (context,state) => HistorySiteSixPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history7",
                    builder: (context,state) => HistorySiteSevenPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/history8",
                    builder: (context,state) => HistorySiteEightPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart1",
                      builder: (context,state) => ChartSiteOnePage(),
                      routes: <RouteBase>[

                      ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart2",
                      builder: (context,state) => ChartSiteTwoPage(),
                      routes: <RouteBase>[

                      ]
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart3",
                    builder: (context,state) => ChartSiteThreePage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart4",
                    builder: (context,state) => ChartSiteFourPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart5",
                    builder: (context,state) => ChartSiteFivePage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart6",
                    builder: (context,state) => ChartSiteSixPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart7",
                    builder: (context,state) => ChartSiteSevenPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/chart8",
                    builder: (context,state) => ChartSiteEightPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/setting",
                    builder: (context,state) => KawosotiSettingPage(),
                  ),
                ]),
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(path: "/users",
                    builder: (context,state) => UserPage(),
                  ),
                ]),



          ],

        )
      ]);
});

