import 'dart:developer';
import 'dart:io';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:progress_stepper/progress_stepper.dart';
import 'package:si/division1/homepage/collection_water_volume.dart';
import 'package:si/division1/homepage/flow_reading_box.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/motor_model.dart';
import 'package:si/division1/model/motor_setting_model.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:si/utils/format.dart';

import '../../constants/app_sizes.dart';
import '../provider/division_provider.dart';
import 'motor_moving.dart';

class NewMotorUnit extends HookConsumerWidget {
  NewMotorUnit(
      {this.motorId = 'motor1',
        this.motorSettingModel,
        this.ratio = 3.0, this.motorModel,
        this.active =  true,
       });

  final String motorId;
  final double ratio;
  final MotorSettingModel? motorSettingModel;
  final MotorModel? motorModel;
  final bool? active;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final setting = ref.read(localStorageProvider).getSettings();
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final role = prefs.getUserRole();


    final motorState = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider(motorId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motorState.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);

          }
          if (motorState.value.device_status ==
              motorState.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return GestureDetector(
      onLongPress: () async {
        if(role>3){
          final setting = await context.push("/home/<USER>", extra: motorId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },

      child: Container(
        height: role > 2 ? 224 : 200,
        width: prefs.getSiteId() == "site1" ?  360/ratio :MediaQuery.of(context).size.width / ratio ,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: motorState.value.device_status == 3 ?   Colors.green : Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child:  Text(
                role>2 ? (motorSettingModel?.name ?? 'Motor')+"("+motorId+")" : (motorSettingModel?.name ?? 'Motor'),
                style: TextStyle(color: motorState.value.device_status == 3 ?  Colors.green :Colors.grey, fontSize: 12),
              ),
            ),

            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: MotorMoving(motorState.value.device_status == 3, motorState.value,
                  motorSettingModel,
                  faultStatus:
                  motorState.value?.VoltageFaultStatus ==1),
            ),

            Padding(
              padding: const EdgeInsets.all(4.0),
              child: !(motorSettingModel?.has_voltage ?? false)? Text(
                (motorState.value?.motorAmps?.toStringAsFixed(2)  ?? '')+ ' A'
                    ,
                style: TextStyle(
                  fontSize: 12,
                    color:
                    motorState.value?.device_status == 3 ? Colors.green : Colors.grey),
              ) : Text(

                     (motorState.value?.motorAmps?.toStringAsFixed(2)  ?? '0.00') + ' A/'+ ( (motorState.value?.voltage  ?? 0.0) + (motorSettingModel?.CF ?? 0.0)).toStringAsFixed(2) + ' V',
                style: TextStyle(
                  fontSize: 12,
                    color:
                    motorState.value.device_status == 3 ? Colors.green : Colors.grey),
              ),
            ),

            ProgressStepper(
              width: 80,
              height: 15,
              stepCount: 3,
              builder: ( context,index, widthOfStep) {
                if (index == 1) {
                  return ProgressStepWithArrow(
                    width: widthOfStep,
                    height: 15,
                    defaultColor: Colors.grey,
                    progressColor: Colors.green,
                    borderWidth: 1,
                    wasCompleted: motorState.value?.mobile_status==3,
                    child: Center(
                      child: Text(
                        index.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                }
                return ProgressStepWithChevron(
                  width: widthOfStep,
                  height: 15,
                  defaultColor: Colors.grey,
                  progressColor: Colors.green,
                  borderWidth: 1,
                  wasCompleted: index==2 ? motorState.value?.device_status==3 : index ==3 ? true : false,
                  child: Center(
                    child: Text(
                      index.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ),
                );
              },
            ),
            gapH8,
            Consumer(builder: (context, WidgetRef ref, child) {
              final userActive = ref
                  .watch(userProfileProvider)
                  .asData
                  ?.value
                  ?.role ??
                  5;
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FlutterSwitch(
                      width: 60.0,
                      height: 25.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 25.0,
                      value: motorState.value?.device_status == 3 && motorState.value?.mobile_status == 3 ,
                      borderRadius: 30.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {
                        if(active!=null && active!){
                          EasyLoading.show();
                          if (kIsWeb || (await hasNetwork() && !kIsWeb)) {
                            if (userActive == 0) {
                              EasyLoading.showError(
                                  'Something wrong with your device.');
                            } else if (userActive == 1 ) {
                              EasyLoading.showError(
                                  'You do not have enough permission. Please contact concern admins for activating the user.');
                            } else if (userActive == 2) {
                              if (motorState.value?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              }

                            } else if (userActive >=3) {
                              if (motorState.value?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              }
                            }else{
                              EasyLoading.showError(
                                  'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                            }
                          } else {
                            EasyLoading.showError(
                                'No internet Connection');
                          }
                        }
                      }),
                ),
              );
            }),
            gapH8,
            if(role>2)
              Text(Format.dateFromTimeStamp(motorState.value?.time ?? DateTime.now().millisecondsSinceEpoch), style: TextStyle(color: Colors.black, fontSize: 12),)



          ],
        ),
      ),
    );

  }

  Future<bool> hasNetwork() async {
    try {
      final result = await InternetAddress.lookup('example.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}




class NewWebMotorUnit extends HookConsumerWidget {
  NewWebMotorUnit(
      {this.motorId = 'motor1',
        this.motorSettingModel,
        this.width = 200.0, this.motorModel,
        this.active =  true,
      });

  final String motorId;
  final double width;
  final MotorSettingModel? motorSettingModel;
  final MotorModel? motorModel;
  final bool? active;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final setting = ref.read(localStorageProvider).getSettings();
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final role = prefs.getUserRole();


    final motorState = useState<MotorModel>(const MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider(motorId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motorState.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }else{
            print("NULL");
          }
          if (motorState.value.device_status ==
              motorState.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return GestureDetector(
      onLongPress: () async {
        if(role>3){
          final setting = await context.push("/home/<USER>", extra: motorId);
          if(setting!=null && setting == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },
      child: Container(
        height: 170,
        width: width ,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: motorState.value.device_status == 3 ?   Colors.green : Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child:  Text(
                role>2 ? (motorSettingModel?.name ?? 'Motor')+"("+motorId+")" : (motorSettingModel?.name ?? 'Motor'),
                style: TextStyle(color: motorState.value.device_status == 3 ?  Colors.green :Colors.grey, fontSize: 12),
              ),
            ),

            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: MotorMoving(motorState.value.device_status == 3, motorState.value,
                  motorSettingModel,
                  faultStatus:
                  motorState.value?.VoltageFaultStatus ==1),
            ),

            Padding(
              padding: const EdgeInsets.all(4.0),
              child: !(motorSettingModel?.has_voltage ?? false)? Text(
                (motorState.value?.motorAmps?.toStringAsFixed(2)  ?? '')+ ' A',
                style: TextStyle(
                    fontSize: 12,
                    color:
                    motorState.value?.device_status == 3 ? Colors.green : Colors.grey),
              ) : Text(
                (motorState.value?.motorAmps?.toStringAsFixed(2)  ?? '0.00') + ' A/'+ ( (motorState.value?.voltage  ?? 0.0) + (motorSettingModel?.CF ?? 0.0)).toStringAsFixed(2) + ' V',
                style: TextStyle(
                    fontSize: 12,
                    color:
                    motorState.value.device_status == 3 ? Colors.green : Colors.grey),
              ),
            ),
            ProgressStepper(
              width: 80,
              height: 15,
              stepCount: 3,
              builder: ( context,index, widthOfStep) {
                if (index == 1) {
                  return ProgressStepWithArrow(
                    width: widthOfStep,
                    height: 15,
                    defaultColor: Colors.grey,
                    progressColor: Colors.green,
                    borderWidth: 1,
                    wasCompleted: motorState.value?.mobile_status==3,
                    child: Center(
                      child: Text(
                        index.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                }
                return ProgressStepWithChevron(
                  width: widthOfStep,
                  height: 15,
                  defaultColor: Colors.grey,
                  progressColor: Colors.green,
                  borderWidth: 1,
                  wasCompleted: index==2 ? motorState.value?.device_status==3 : index ==3 ? true : false,
                  child: Center(
                    child: Text(
                      index.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ),
                );
              },
            ),
            Consumer(builder: (context, WidgetRef ref, child) {
              final userActive = ref
                  .watch(userProfileProvider)
                  .asData
                  ?.value
                  ?.role ??
                  5;
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FlutterSwitch(
                      width: 60.0,
                      height: 23.0,
                      activeColor: Colors.green,
                      inactiveColor: Colors.grey,
                      valueFontSize: 12.0,
                      toggleSize: 25.0,
                      value: motorState.value?.device_status == 3 && motorState.value?.mobile_status == 3 ,
                      borderRadius: 30.0,
                      padding: 4.0,
                      showOnOff: true,
                      onToggle: (val) async {
                        if(active!=null && active!){
                          EasyLoading.show();
                          if (await hasNetwork()) {
                            if (userActive == 0) {
                              EasyLoading.showError(
                                  'Something wrong with your device.');
                            } else if (userActive == 1 ) {
                              EasyLoading.showError(
                                  'You do not have enough permission. Please contact concern admins for activating the user.');
                            } else if (userActive == 2) {
                              if (motorState.value?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopMotor(motorId);
                              }
                              await ref.read(divisionRepositoryProvider).
                              addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                            } else if (userActive >=3) {
                              if (motorState.value?.device_status != 3) {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .startMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "ON:${prefs.getFullName()}");
                              } else {
                                await ref
                                    .read(divisionRtdbServiceProvider)
                                    .stopMotor(motorId);
                                await ref.read(divisionRepositoryProvider).
                                addMotorHistory(prefs.getSiteId(), motorSettingModel?.name ?? "", int.parse(motorId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                              }
                            }else{
                              EasyLoading.showError(
                                  'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                            }
                          } else {
                            EasyLoading.showError(
                                'No internet Connection');
                          }
                        }
                      }),
                ),
              );
            }),
            /*if(role>2)
              Text(Format.dateFromTimeStamp(motorState.value?.time ?? DateTime.now().millisecondsSinceEpoch), style: TextStyle(color: Colors.black, fontSize: 12),)
            else
              Text(Format.dateFromTimeStamp(DateTime.now().millisecondsSinceEpoch), style: TextStyle(color: Colors.black, fontSize: 12),)*/


          ],
        ),
      ),
    );

  }

  Future<bool> hasNetwork() async {
    try {
      final result = await InternetAddress.lookup('example.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
