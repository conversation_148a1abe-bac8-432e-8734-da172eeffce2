

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:si/constants/app_sizes.dart';

class LabelText extends StatelessWidget{

  const LabelText({Key? key, required this.label}) : super(key: key);
  final String label;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: horPadding24,
      child: Text(label,style: TextStyle(color: Colors.grey, fontSize: 14)),
    );
  }

}