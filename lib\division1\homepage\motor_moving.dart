

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:si/division1/homepage/distribution_water_volume.dart';

import '../../constants/app_sizes.dart';
import '../model/motor_model.dart';
import '../model/motor_setting_model.dart';

class MotorMoving extends StatelessWidget{

  final bool moving;
  final bool faultStatus;
  final MotorModel? motorModel;
  final MotorSettingModel? motorSettingModel;

  MotorMoving(this.moving, this.motorModel,this.motorSettingModel,{this.faultStatus = false});


  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        /*Transform.rotate(angle: 300, child: SpinKitThreeInOut(
          size: 40,
          color: Colors.blue,
        ),),
        Transform.rotate(angle: 300, child: SpinKitThreeInOut(
          size: 40,
          color: Colors.blue,
        ),),
        Transform.rotate(angle: 300, child: SpinKitThreeInOut(
          size: 40,
          color: Colors.blue,
        ),),
        Transform.rotate(angle: 300, child: SpinKitThreeInOut(
          size: 40,
          color: Colors.blue,
        ),),*/
        Expanded(
          child: Column(
            children: [
              Container(
                height: 60,
                width: 60,
                decoration: new BoxDecoration(
                    color: Colors.transparent,
                    shape: BoxShape.circle,
                    border: Border.all(color: faultStatus ?  Colors.red   : ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)) ? Colors.green : Colors.grey ,width: 5)
                ),
                child: Stack(
                  children: [
                    Visibility(
                      visible: !((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
                      child: Center(
                        child: Container(
                          height: 45,
                            width: 45,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: faultStatus ? Colors.red  : Colors.grey,
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: ! ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
                      child: Center(
                        child: Container(
                          height: 25,
                          width: 25,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
                      child: Center(
                        child: SpinKitDualRing(
                          color: faultStatus ? Colors.red  : Colors.green,
                          size: 45.0,
                        ),
                      ),
                    ),
                    Visibility(
                      visible: ((motorModel?.motorAmps ?? 0.0) > (motorSettingModel?.current_threshold ?? 0.0)),
                      child: Center(
                        child: LoadingAnimationWidget.beat(
                          color: faultStatus ? Colors.red  : Colors.green,
                          size: 20,
                        ),
                      ),
                    ),
                    Visibility(
                      visible: ((motorModel?.motorAmps ?? 0.0)>(motorSettingModel?.current_threshold ?? 0.0)),
                      child: Center(
                        child: LoadingAnimationWidget.hexagonDots(
                          color: faultStatus ? Colors.red  : Colors.green,
                          size: 35,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),


      ],
    );
  }



}