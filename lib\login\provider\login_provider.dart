

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/provider/auth_provider.dart';

import '../../kawosoti/schema/model/user_model.dart';
import '../controller/login_controller.dart';
import '../controller/login_state.dart';

final loginEyeToggleProvider = StateProvider.autoDispose<bool>((ref) {
  return true;
});

final deleteEyeToggleProvider = StateProvider.autoDispose<bool>((ref) {
  return true;
});

final userCallbackProvider = StateProvider.autoDispose<UserCallbackModel>((ref){
  return UserCallbackModel(email:"",password:"");
});

final firebaseAuthProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);



final loginControllerProvider = StateNotifierProvider.autoDispose
    <LoginController, LoginState>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return LoginController(
    authRepository: authRepository,
  );
});
