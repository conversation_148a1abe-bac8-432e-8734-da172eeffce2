

import 'dart:io';

import 'package:circular_progress_stack/circular_progress_stack.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/utils/app_dimension.dart';

import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import 'powerSwitch/enum.dart';
import 'powerSwitch/power_switch_button.dart';

class StepWiseValveWidget extends HookConsumerWidget{

  StepWiseValveWidget({required this.name,
    required this.valveId, required this.role, required this.motorModel});

  final String name;

  final String valveId;

  final int role;

  final MotorModel? motorModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final valve10State = useState<MotorModel>(MotorModel());

    final prefs = ref.read(sharedPreferencesServiceProvider);
    print(prefs.getSiteId());

    final userActive = ref
        .watch(userProfileProvider)
        .asData
        ?.value
        ?.role ??
        5;

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valve10State.value = MotorModel(
                output_status: datasnapshot['output_status'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (valve10State.value.device_status == valve10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    Future<void> turnOnvalve(int step)async {
      if (userActive == 0) {
        EasyLoading.showError(
            'Something wrong with your device.');
      } else if (userActive == 1 ) {
        EasyLoading.showError(
            'You do not have enough permission. Please contact concern admins for activating the user.');
      } else if (userActive == 2) {
        if (valve10State.value?.device_status != step) {
          await ref
              .read(divisionRtdbServiceProvider)
              .startStepValve(valveId,step);
          ref.read(divisionRepositoryProvider).
          addValveHistory(prefs.getSiteId(), name, int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}. ${getStepPercentage(step)}");
        }
      } else if (userActive >=3) {
        print("SEOIRH");
        if (valve10State.value?.device_status != step) {
          print("HERE");
          await ref
              .read(divisionRtdbServiceProvider)
              .startStepValve(valveId, step);
          ref.read(divisionRepositoryProvider).
          addValveHistory(prefs.getSiteId(), name, int.parse(valveId), prefs.getFullName(), "ON:${prefs.getFullName()}. ${getStepPercentage(step)}");
        }
      }else{
        EasyLoading.showError(
            'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
      }
    }

    return  Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        height: 300,
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      height: 20,
                      child: Text(name)
                  ),
                  gapH8,
                  Image.asset(
                    "assets/icons/valve.png",
                    height: 80,
                    width: 80,
                  ),
                  gapH16,
                  Consumer(
                      builder: (context,ref,child) {

                        return FlutterSwitch(
                            width: 50.0,
                            height: 20.0,
                            activeColor: Colors.green,
                            inactiveColor: Colors.grey,
                            valueFontSize: 10.0,
                            toggleSize: 20.0,
                            value: valve10State.value?.device_status != 2 && valve10State.value?.mobile_status != 2,
                            borderRadius: 30.0,
                            padding: 4.0,
                            showOnOff: true,
                            onToggle: (val) async {
                              EasyLoading.show();
                              if (userActive == 0) {
                                EasyLoading.showError(
                                    'Something wrong with your device.');
                              } else if (userActive == 1 ) {
                                EasyLoading.showError(
                                    'You do not have enough permission. Please contact concern admins for activating the user.');
                              } else if (userActive == 2) {
                                if (valve10State.value?.device_status != 2) {
                                  await ref
                                      .read(divisionRtdbServiceProvider)
                                      .stopValve(valveId);
                                  await ref.read(divisionRepositoryProvider).
                                  addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                                }
                              } else if (userActive >=3) {
                                if (valve10State.value?.device_status != 2) {
                                  await ref
                                      .read(divisionRtdbServiceProvider)
                                      .stopValve(valveId);
                                  await ref.read(divisionRepositoryProvider).
                                  addValveHistory(prefs.getSiteId(), name ?? "", int.parse(valveId), prefs.getFullName(), "OFF:${prefs.getFullName()}");
                                }
                              }else{
                                EasyLoading.showError(
                                    'The user has no access to turn on and off the motor. Please contact concern admins for activating the user.');
                              }
                            });
                      }
                  ),
                  gapH24,
                  Center(
                    child: Stack(
                      children: [
                        AnimatedStackCircularProgressBar(
                          size: 80,
                          progressStrokeWidth: 15,
                          backStrokeWidth: 15,
                          startAngle: 0,
                          backColor: const Color(0xffD7DEE7),
                          bars: [

                            AnimatedBarValue(
                              barColor: Colors.green,
                              barValues: valve10State.value?.device_status == 3 ? 100 :
                              valve10State.value?.device_status == 4 ? 25 :
                              valve10State.value?.device_status == 5 ? 50 :
                              valve10State.value?.device_status == 6 ? 75 : 0,
                              fullProgressColors: Colors.red,
                            ),

                          ],
                        ),
                        Container(
                          height: 80,
                          width: 80,
                          child: Center(
                            child: Text(valve10State.value?.device_status == 3 ? "100 %" :
                            valve10State.value?.device_status == 4 ? "25 %" :
                            valve10State.value?.device_status == 5 ? "50 %" :
                            valve10State.value?.device_status == 6 ? "75 %" : "0 %"),
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              PowerSwitchButton(
                                isLoading: valve10State.value.mobile_status == 4 && valve10State.value.device_status!=4,
                                isActive: valve10State.value.device_status == 4,
                                size: 100.0, // Required: Size of the switch button
                                strokeWidth: 5.0, // Required: Width of the dashed border
                                dashWidth: 1.0, // Required: Width of each dash in the dashed border
                                dashSpace:
                                2.0, // Required: Space between each dash in the dashed border
                                onColor: Colors.green, // Required: Color when the switch is on
                                offColor: Colors.red, // Required: Color when the switch is off
                                backgroundColor:
                                Colors.grey[200]!, // Required: Background color of the switch
                                iconColor:
                                Colors.white, // Required: Color of the icon in the switch
                                onToggle: (bool value) {
                                  // Required: Callback function that is triggered when the switch is toggled
                                  print(value); // Prints the switch state (on/off) to the console
                                },
                                shape: Shape
                                    .CIRCLE, // Optional: Shape of the switch button (circle or square)
                                borderRadius: 20, // Optional: Border radius for square shape
                                customIcon: Icons
                                    .power_settings_new, // Optional: Custom icon for the switch
                                label: 'Power Switch', // Optional: Label text inside the switch
                                disabled: false, // Optional: Whether the switch is disabled or not
                                onGradient: const LinearGradient(
                                  colors: [
                                    Colors.green,
                                    Colors.lightGreen
                                  ], // Optional: Gradient colors for the on state
                                ),
                                offGradient: const LinearGradient(
                                  colors: [
                                    Colors.red,
                                    Colors.orange
                                  ], // Optional: Gradient colors for the off state
                                ),
                                animationDuration: const Duration(
                                    milliseconds:
                                    300), // Optional: Duration of the toggle animation
                                animationCurve:
                                Curves.bounceInOut, // Optional: Curve of the toggle animation
                                loadingCallback: () async {
                                  // Optional: Callback function that handles the loading state
                                 // Simulates a loading delay
                                  if(valve10State.value.device_status!=4 && valve10State.value.mobile_status!=4){
                                    await turnOnvalve(4);
                                    return true;
                                  }else{
                                    return false;
                                  }
                                  },
                                loadingText:
                                "Loading..", // Optional: Text displayed during the loading state
                              ),
                              Text("25%")
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              PowerSwitchButton(
                                isLoading: valve10State.value.mobile_status == 5 && valve10State.value.device_status!=5,
                                isActive: valve10State.value.device_status == 5,
                                size: 100.0, // Required: Size of the switch button
                                strokeWidth: 5.0, // Required: Width of the dashed border
                                dashWidth: 1.0, // Required: Width of each dash in the dashed border
                                dashSpace:
                                2.0, // Required: Space between each dash in the dashed border
                                onColor: Colors.green, // Required: Color when the switch is on
                                offColor: Colors.red, // Required: Color when the switch is off
                                backgroundColor:
                                Colors.grey[200]!, // Required: Background color of the switch
                                iconColor:
                                Colors.white, // Required: Color of the icon in the switch
                                onToggle: (bool value) {
                                  // Required: Callback function that is triggered when the switch is toggled
                                  print(value); // Prints the switch state (on/off) to the console
                                },
                                shape: Shape
                                    .CIRCLE, // Optional: Shape of the switch button (circle or square)
                                borderRadius: 20, // Optional: Border radius for square shape
                                customIcon: Icons
                                    .power_settings_new, // Optional: Custom icon for the switch
                                label: 'Power Switch', // Optional: Label text inside the switch
                                disabled: false, // Optional: Whether the switch is disabled or not
                                onGradient: const LinearGradient(
                                  colors: [
                                    Colors.green,
                                    Colors.lightGreen
                                  ], // Optional: Gradient colors for the on state
                                ),
                                offGradient: const LinearGradient(
                                  colors: [
                                    Colors.red,
                                    Colors.orange
                                  ], // Optional: Gradient colors for the off state
                                ),
                                animationDuration: const Duration(
                                    milliseconds:
                                    300), // Optional: Duration of the toggle animation
                                animationCurve:
                                Curves.bounceInOut, // Optional: Curve of the toggle animation
                                loadingCallback: () async {
                                  if(valve10State.value.device_status!=5 && valve10State.value.device_status!=5){
                                    turnOnvalve(5);
                                    return true;
                                  }else{
                                    return false;
                                  }
                                },
                                loadingText:
                                "Loading..", // Optional: Text displayed during the loading state
                              ),
                              Text("50%")
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              PowerSwitchButton(
                                isLoading: valve10State.value.mobile_status == 6 && valve10State.value.device_status!=6,
                                isActive: valve10State.value.device_status == 6,
                                size: 100.0, // Required: Size of the switch button
                                strokeWidth: 5.0, // Required: Width of the dashed border
                                dashWidth: 1.0, // Required: Width of each dash in the dashed border
                                dashSpace:
                                2.0, // Required: Space between each dash in the dashed border
                                onColor: Colors.green, // Required: Color when the switch is on
                                offColor: Colors.red, // Required: Color when the switch is off
                                backgroundColor:
                                Colors.grey[200]!, // Required: Background color of the switch
                                iconColor:
                                Colors.white, // Required: Color of the icon in the switch
                                onToggle: (bool value) {
                                  // Required: Callback function that is triggered when the switch is toggled
                                  print(value); // Prints the switch state (on/off) to the console
                                },
                                shape: Shape
                                    .CIRCLE, // Optional: Shape of the switch button (circle or square)
                                borderRadius: 20, // Optional: Border radius for square shape
                                customIcon: Icons
                                    .power_settings_new, // Optional: Custom icon for the switch
                                label: 'Power Switch', // Optional: Label text inside the switch
                                disabled: false, // Optional: Whether the switch is disabled or not
                                onGradient: const LinearGradient(
                                  colors: [
                                    Colors.green,
                                    Colors.lightGreen
                                  ], // Optional: Gradient colors for the on state
                                ),
                                offGradient: const LinearGradient(
                                  colors: [
                                    Colors.red,
                                    Colors.orange
                                  ], // Optional: Gradient colors for the off state
                                ),
                                animationDuration: const Duration(
                                    milliseconds:
                                    300), // Optional: Duration of the toggle animation
                                animationCurve:
                                Curves.bounceInOut, // Optional: Curve of the toggle animation
                                loadingCallback: () async {
                                  if(valve10State.value.device_status!=6 && valve10State.value.mobile_status!=6){
                                    turnOnvalve(6);
                                    return true;
                                  }else{
                                    return false;
                                  }
                                },
                                loadingText:
                                "Loading..", // Optional: Text displayed during the loading state
                              ),
                              Text("75%")
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              PowerSwitchButton(
                                isLoading: valve10State.value.mobile_status == 3 && valve10State.value.mobile_status!=3,
                                isActive: valve10State.value.device_status == 3,
                                size: 100.0, // Required: Size of the switch button
                                strokeWidth: 5.0, // Required: Width of the dashed border
                                dashWidth: 1.0, // Required: Width of each dash in the dashed border
                                dashSpace:
                                2.0, // Required: Space between each dash in the dashed border
                                onColor: Colors.green, // Required: Color when the switch is on
                                offColor: Colors.red, // Required: Color when the switch is off
                                backgroundColor:
                                Colors.grey[200]!, // Required: Background color of the switch
                                iconColor:
                                Colors.white, // Required: Color of the icon in the switch
                                onToggle: (bool value) {
                                  // Required: Callback function that is triggered when the switch is toggled
                                  print(value); // Prints the switch state (on/off) to the console
                                },
                                shape: Shape
                                    .CIRCLE, // Optional: Shape of the switch button (circle or square)
                                borderRadius: 20, // Optional: Border radius for square shape
                                customIcon: Icons
                                    .power_settings_new, // Optional: Custom icon for the switch
                                label: 'Power Switch', // Optional: Label text inside the switch
                                disabled: false, // Optional: Whether the switch is disabled or not
                                onGradient: const LinearGradient(
                                  colors: [
                                    Colors.green,
                                    Colors.lightGreen
                                  ], // Optional: Gradient colors for the on state
                                ),
                                offGradient: const LinearGradient(
                                  colors: [
                                    Colors.red,
                                    Colors.orange
                                  ], // Optional: Gradient colors for the off state
                                ),
                                animationDuration: const Duration(
                                    milliseconds:
                                    300), // Optional: Duration of the toggle animation
                                animationCurve:
                                Curves.bounceInOut, // Optional: Curve of the toggle animation
                                loadingCallback: () async {
                                  if(valve10State.value.device_status!=3 && valve10State.value.mobile_status!=3){
                                    turnOnvalve(3);
                                    return true;
                                  }else{
                                    return false;
                                  }
                                },
                                loadingText:
                                "Loading..", // Optional: Text displayed during the loading state
                              ),
                              Text("100%")
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

}

String getStepPercentage(int step){
  if(step==2){
    return "0%";
  }else if(step==4) {
    return "25%";
  }else if(step==5) {
    return "50%";
  }else if(step==6){
    return "75%";
  }else{
    return "100%";
  }
}