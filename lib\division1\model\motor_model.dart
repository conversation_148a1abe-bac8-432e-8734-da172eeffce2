



import '../../utils/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'motor_model.freezed.dart';
part 'motor_model.g.dart';

@freezed
class MotorModel with _$MotorModel {
  const factory MotorModel({
    @Default(0.0) double? motorAmps,
    @Default(0) int  device_status,
    @Default(0) int  mobile_status,
    @Default(0) int  output_status,
    @Default(0) int  time,
    @Default(2) int  VoltageFaultStatus,
    @Default(0.0) double? voltage,
    @Default(0.0) double? freq,
  }) = _MotorModel;

  factory MotorModel.fromJson(Map<String, dynamic> json) => _$MotorModelFromJson(json);
}


@freezed
class SensorModel with _$SensorModel {
  const factory SensorModel({
    @Default(0.0) double? cl,
    @Default(0.0) double? tur,
    @Default(0.0) double? ph,
    @Default(0.0) double?  flow,
    @Default(0.0) double?  cum_flow,
    @Default(0) int?  time,
  }) = _SensorModel;

  factory SensorModel.fromJson(Map<String, dynamic> json) => _$SensorModelFromJson(json);
}
