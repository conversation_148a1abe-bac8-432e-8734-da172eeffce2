import 'package:flutter/material.dart';
import 'package:si/constants/style_manager.dart';

import '../../constants/app_sizes.dart';


/// Primary button based on [ElevatedButton].
/// Useful for CTAs in the app.
/// @param text - text to display on the button.
/// @param isLoading - if true, a loading indicator will be displayed instead of
/// the text.
/// @param onPressed - callback to be called when the button is pressed.
class OutlineButton extends StatelessWidget {
  const OutlineButton(
      {Key? key, required this.text, this.isLoading = false,
        this.buttonColor,
        this.height = Sizes.p60,this.textColor ,this.onPressed})
      : super(key: key);
  final String text;
  final Color? buttonColor;
  final Color? textColor;
  final bool isLoading;
  final double height;
  final VoidCallback? onPressed;
  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      style: ElevatedButton.styleFrom(
        side: BorderSide(width: 1.0, color: buttonColor ?? context.themeColor),
        textStyle: context.buttonText,
        minimumSize: Size.fromHeight(height),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.0),
        ),// foreground
      ),
      onPressed: (){
        onPressed?.call();
      },
      child: Text(text, style: context.buttonText),
    );
  }
}
